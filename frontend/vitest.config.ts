/**
 * Vitest Configuration
 * 
 * This configuration sets up Vites<PERSON> for testing the React/TypeScript frontend
 * with proper TypeScript support, React Testing Library integration, and
 * coverage reporting aligned with project quality standards.
 */

import { defineConfig } from "vitest/config"
import react from "@vitejs/plugin-react"
import { resolve } from "path"

export default defineConfig({
  plugins: [react()],
  
  test: {
    // Test environment configuration
    environment: "jsdom",
    
    // Setup files to run before each test
    setupFiles: ["./src/test/setup.ts"],
    
    // Global test configuration
    globals: true,
    
    // Coverage configuration
    coverage: {
      provider: "v8",
      reporter: ["text", "json", "html"],
      exclude: [
        "node_modules/",
        "src/test/",
        "**/*.d.ts",
        "**/*.config.*",
        "**/coverage/**",
        "**/.next/**",
        "**/dist/**",
        "**/*.test.*",
        "**/*.spec.*",
      ],
      // Enforce minimum coverage thresholds per project rules
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80,
        },
      },
    },
    
    // Include patterns for test files
    include: [
      "**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}",
    ],
    
    // Exclude patterns
    exclude: [
      "node_modules",
      "dist",
      ".next",
      "coverage",
    ],
    
    // Test timeout configuration
    testTimeout: 10000,
    hookTimeout: 10000,
    
    // Reporter configuration
    reporters: ["verbose", "json", "html"],
    
    // Output directory for test results
    outputFile: {
      json: "./coverage/test-results.json",
      html: "./coverage/test-results.html",
    },
  },
  
  // Resolve configuration for path aliases
  resolve: {
    alias: {
      "@": resolve(__dirname, "./src"),
      "@/components": resolve(__dirname, "./src/components"),
      "@/hooks": resolve(__dirname, "./src/hooks"),
      "@/lib": resolve(__dirname, "./src/lib"),
      "@/types": resolve(__dirname, "./src/types"),
      "@/utils": resolve(__dirname, "./src/utils"),
      "@/test": resolve(__dirname, "./src/test"),
    },
  },
  
  // Define global constants for tests
  define: {
    __TEST__: true,
  },
  
  // Esbuild configuration for TypeScript
  esbuild: {
    target: "node14",
  },
})
