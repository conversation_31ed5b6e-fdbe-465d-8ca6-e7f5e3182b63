#!/bin/bash

# Repository Management Test Runner
# 
# Comprehensive test script for repository management functionality
# including unit tests, integration tests, and coverage reporting.

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the frontend directory
if [ ! -f "package.json" ]; then
    print_error "This script must be run from the frontend directory"
    exit 1
fi

# Check if pnpm is available
if ! command -v pnpm &> /dev/null; then
    print_error "pnpm is required but not installed"
    exit 1
fi

print_status "Starting Repository Management Test Suite"

# Function to run tests with proper error handling
run_test_suite() {
    local test_name="$1"
    local test_pattern="$2"
    local description="$3"
    
    print_status "Running $test_name..."
    echo "Description: $description"
    
    if pnpm test "$test_pattern" --reporter=verbose; then
        print_success "$test_name completed successfully"
        return 0
    else
        print_error "$test_name failed"
        return 1
    fi
}

# Initialize test results tracking
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Test suites to run
declare -A TEST_SUITES=(
    ["Repository Components"]="src/components/repository/__tests__/*.test.tsx|Unit tests for repository UI components"
    ["Repository Hooks"]="src/hooks/api/__tests__/useRepository.test.ts|Unit tests for repository API hooks"
    ["Repository Integration"]="src/test/integration/RepositoryManagement.integration.test.tsx|End-to-end integration tests"
    ["Error Handling"]="src/components/error/__tests__/*.test.tsx|Error boundary and handling tests"
)

# Run individual test suites
for test_name in "${!TEST_SUITES[@]}"; do
    IFS='|' read -r pattern description <<< "${TEST_SUITES[$test_name]}"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if run_test_suite "$test_name" "$pattern" "$description"; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    
    echo "" # Add spacing between test suites
done

# Run coverage report for repository components
print_status "Generating coverage report for repository components..."

if pnpm test --coverage \
    --coverage.include="src/components/repository/**" \
    --coverage.include="src/hooks/api/useRepository.ts" \
    --coverage.include="src/lib/errors/repository-errors.ts" \
    --coverage.reporter=text \
    --coverage.reporter=html \
    --coverage.reporter=json-summary; then
    print_success "Coverage report generated successfully"
    print_status "Coverage report available at: coverage/index.html"
else
    print_warning "Coverage report generation failed"
fi

# Run type checking for repository components
print_status "Running TypeScript type checking..."

if pnpm tsc --noEmit \
    --project tsconfig.json \
    --include "src/components/repository/**/*" \
    --include "src/hooks/api/useRepository.ts" \
    --include "src/lib/errors/repository-errors.ts"; then
    print_success "Type checking passed"
else
    print_error "Type checking failed"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# Run linting for repository components
print_status "Running ESLint for repository components..."

if pnpm eslint \
    "src/components/repository/**/*.{ts,tsx}" \
    "src/hooks/api/useRepository.ts" \
    "src/lib/errors/repository-errors.ts" \
    --format=stylish; then
    print_success "Linting passed"
else
    print_error "Linting failed"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# Performance tests (if available)
if [ -f "src/test/performance/repository-performance.test.ts" ]; then
    print_status "Running performance tests..."
    
    if run_test_suite "Performance Tests" "src/test/performance/repository-performance.test.ts" "Performance benchmarks for repository operations"; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
fi

# Accessibility tests (if available)
if [ -f "src/test/accessibility/repository-a11y.test.tsx" ]; then
    print_status "Running accessibility tests..."
    
    if run_test_suite "Accessibility Tests" "src/test/accessibility/repository-a11y.test.tsx" "Accessibility compliance tests"; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
fi

# Print final results
echo ""
echo "========================================"
echo "Repository Management Test Results"
echo "========================================"
echo "Total Test Suites: $TOTAL_TESTS"
echo "Passed: $PASSED_TESTS"
echo "Failed: $FAILED_TESTS"
echo ""

if [ $FAILED_TESTS -eq 0 ]; then
    print_success "All repository management tests passed! 🎉"
    echo ""
    echo "Repository management functionality is ready for production:"
    echo "✅ All components tested and working"
    echo "✅ API integration verified"
    echo "✅ Error handling comprehensive"
    echo "✅ User interactions validated"
    echo "✅ Accessibility compliance checked"
    echo ""
    exit 0
else
    print_error "$FAILED_TESTS test suite(s) failed"
    echo ""
    echo "Please fix the failing tests before proceeding:"
    echo "❌ Check test output above for specific failures"
    echo "❌ Ensure all dependencies are properly mocked"
    echo "❌ Verify component props and API contracts"
    echo "❌ Fix any TypeScript or linting errors"
    echo ""
    exit 1
fi
