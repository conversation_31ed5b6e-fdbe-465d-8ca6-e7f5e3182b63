/**
 * Session Provider
 * 
 * This module provides React context for session state management,
 * conversation tracking, and user session persistence across the application.
 */

"use client"

import React from "react"

import type { ConversationMessage, SessionState } from "@/lib/api/types"
import { createSessionId, createMessageId } from "@/lib/api/types"
import { use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/hooks/useErrorHandler"

/**
 * Session context value interface
 */
export interface SessionContextValue {
  // Session state
  session: SessionState
  
  // Session management
  createSession: (userId?: string) => Promise<void>
  endSession: () => void
  updateSession: (updates: Partial<SessionState>) => void
  
  // Conversation management
  addMessage: (message: Omit<ConversationMessage, "id" | "timestamp">) => void
  updateMessage: (messageId: string, updates: Partial<ConversationMessage>) => void
  removeMessage: (messageId: string) => void
  clearHistory: () => void
  
  // Utility functions
  isActive: boolean
  messageCount: number
  lastActivity: Date | null
  
  // Session actions
  setUserId: (userId: string | null) => void
  setMetadata: (metadata: Record<string, unknown>) => void
}

/**
 * Session context
 */
const SessionContext = React.createContext<SessionContextValue | null>(null)

/**
 * Session provider props
 */
interface SessionProviderProps {
  children: React.ReactNode
  initialSession?: Partial<SessionState>
  enablePersistence?: boolean
  maxHistorySize?: number
}

/**
 * Default session state
 */
const DEFAULT_SESSION_STATE: SessionState = {
  sessionId: null,
  userId: null,
  conversationHistory: [],
  isActive: false,
  createdAt: undefined,
  lastActivity: undefined,
  metadata: {},
}

/**
 * Session provider component
 * 
 * Provides session management functionality throughout the application
 * with automatic persistence and conversation tracking.
 */
export function SessionProvider({
  children,
  initialSession = {},
  enablePersistence = true,
  maxHistorySize = 100,
}: SessionProviderProps) {
  const [session, setSession] = React.useState<SessionState>({
    ...DEFAULT_SESSION_STATE,
    ...initialSession,
  })
  
  const { handleError } = useErrorHandler({
    showToast: false,
    defaultContext: { component: "SessionProvider" },
  })
  
  // Load session from storage on mount
  React.useEffect(() => {
    if (!enablePersistence || typeof window === "undefined") return
    
    try {
      const stored = localStorage.getItem("session_state")
      if (stored) {
        const parsedSession = JSON.parse(stored)
        
        // Validate and restore session
        if (parsedSession.sessionId) {
          setSession(prev => ({
            ...prev,
            ...parsedSession,
            createdAt: parsedSession.createdAt ? new Date(parsedSession.createdAt) : undefined,
            lastActivity: parsedSession.lastActivity ? new Date(parsedSession.lastActivity) : undefined,
            conversationHistory: parsedSession.conversationHistory?.map((msg: any) => ({
              ...msg,
              timestamp: new Date(msg.timestamp),
            })) || [],
          }))
        }
      }
    } catch (error) {
      handleError(error as Error, { context: "session_load" })
      // Clear corrupted session data
      localStorage.removeItem("session_state")
    }
  }, [enablePersistence, handleError])
  
  // Persist session to storage when it changes
  React.useEffect(() => {
    if (!enablePersistence || typeof window === "undefined") return

    // Only persist if session has meaningful data
    if (session.sessionId || session.conversationHistory.length > 0) {
      try {
        localStorage.setItem("session_state", JSON.stringify(session))
      } catch (error) {
        handleError(error as Error, { context: "session_persist" })
      }
    }
  }, [session, enablePersistence, handleError])
  
  // Create new session
  const createSession = React.useCallback(async (userId?: string) => {
    try {
      const newSessionId = createSessionId()
      const now = new Date()
      
      setSession({
        sessionId: newSessionId,
        userId: userId || null,
        conversationHistory: [],
        isActive: true,
        createdAt: now,
        lastActivity: now,
        metadata: {},
      })
    } catch (error) {
      handleError(error as Error, { context: "session_create", userId })
    }
  }, [handleError])
  
  // End current session
  const endSession = React.useCallback(() => {
    setSession(DEFAULT_SESSION_STATE)
    
    if (enablePersistence && typeof window !== "undefined") {
      localStorage.removeItem("session_state")
    }
  }, [enablePersistence])
  
  // Update session
  const updateSession = React.useCallback((updates: Partial<SessionState>) => {
    setSession(prev => ({
      ...prev,
      ...updates,
      lastActivity: new Date(),
    }))
  }, [])
  
  // Add message to conversation
  const addMessage = React.useCallback((message: Omit<ConversationMessage, "id" | "timestamp">) => {
    const newMessage: ConversationMessage = {
      ...message,
      id: createMessageId(),
      timestamp: new Date(),
    }
    
    setSession(prev => {
      const newHistory = [...prev.conversationHistory, newMessage]
      
      // Trim history if it exceeds max size
      if (newHistory.length > maxHistorySize) {
        newHistory.splice(0, newHistory.length - maxHistorySize)
      }
      
      return {
        ...prev,
        conversationHistory: newHistory,
        lastActivity: new Date(),
      }
    })
  }, [maxHistorySize])
  
  // Update existing message
  const updateMessage = React.useCallback((messageId: string, updates: Partial<ConversationMessage>) => {
    setSession(prev => ({
      ...prev,
      conversationHistory: prev.conversationHistory.map(msg =>
        msg.id === messageId ? { ...msg, ...updates } : msg
      ),
      lastActivity: new Date(),
    }))
  }, [])
  
  // Remove message
  const removeMessage = React.useCallback((messageId: string) => {
    setSession(prev => ({
      ...prev,
      conversationHistory: prev.conversationHistory.filter(msg => msg.id !== messageId),
      lastActivity: new Date(),
    }))
  }, [])
  
  // Clear conversation history
  const clearHistory = React.useCallback(() => {
    setSession(prev => ({
      ...prev,
      conversationHistory: [],
      lastActivity: new Date(),
    }))
  }, [])
  
  // Set user ID
  const setUserId = React.useCallback((userId: string | null) => {
    setSession(prev => ({
      ...prev,
      userId,
      lastActivity: new Date(),
    }))
  }, [])
  
  // Set metadata
  const setMetadata = React.useCallback((metadata: Record<string, unknown>) => {
    setSession(prev => ({
      ...prev,
      metadata: { ...prev.metadata, ...metadata },
      lastActivity: new Date(),
    }))
  }, [])
  
  // Computed values
  const isActive = session.isActive && !!session.sessionId
  const messageCount = session.conversationHistory.length
  const lastActivity = session.lastActivity || null
  
  // Context value
  const contextValue: SessionContextValue = {
    session,
    createSession,
    endSession,
    updateSession,
    addMessage,
    updateMessage,
    removeMessage,
    clearHistory,
    isActive,
    messageCount,
    lastActivity,
    setUserId,
    setMetadata,
  }
  
  return (
    <SessionContext.Provider value={contextValue}>
      {children}
    </SessionContext.Provider>
  )
}

/**
 * Hook to access session context
 * 
 * @returns Session context value
 * @throws Error if used outside of SessionProvider
 */
export function useSession(): SessionContextValue {
  const context = React.useContext(SessionContext)
  
  if (!context) {
    throw new Error("useSession must be used within a SessionProvider")
  }
  
  return context
}

/**
 * Hook for session utilities
 * 
 * Provides additional utilities for working with sessions.
 */
export function useSessionUtils() {
  const session = useSession()
  
  // Get messages by type
  const getMessagesByType = React.useCallback((type: ConversationMessage["type"]) => {
    return session.session.conversationHistory.filter(msg => msg.type === type)
  }, [session.session.conversationHistory])
  
  // Get latest message
  const getLatestMessage = React.useCallback(() => {
    const history = session.session.conversationHistory
    return history.length > 0 ? history[history.length - 1] : null
  }, [session.session.conversationHistory])
  
  // Get session duration
  const getSessionDuration = React.useCallback(() => {
    if (!session.session.createdAt) return 0
    const now = new Date()
    return now.getTime() - session.session.createdAt.getTime()
  }, [session.session.createdAt])
  
  // Check if session is expired
  const isSessionExpired = React.useCallback((maxAgeMs: number = 24 * 60 * 60 * 1000) => {
    if (!session.session.lastActivity) return false
    const now = new Date()
    return now.getTime() - session.session.lastActivity.getTime() > maxAgeMs
  }, [session.session.lastActivity])
  
  // Export conversation history
  const exportHistory = React.useCallback(() => {
    return {
      sessionId: session.session.sessionId,
      userId: session.session.userId,
      createdAt: session.session.createdAt,
      messages: session.session.conversationHistory,
      messageCount: session.messageCount,
      duration: getSessionDuration(),
    }
  }, [session, getSessionDuration])
  
  return {
    getMessagesByType,
    getLatestMessage,
    getSessionDuration,
    isSessionExpired,
    exportHistory,
  }
}
