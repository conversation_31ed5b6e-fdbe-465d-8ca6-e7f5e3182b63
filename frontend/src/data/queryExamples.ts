import type { QueryExample } from "@/components/examples/QueryExamples"

/**
 * Curated query examples for each agent type
 * 
 * These examples demonstrate the specific capabilities and use cases
 * for each agent in the multi-agent system.
 */
export const queryExamples: QueryExample[] = [
  // Technical Architect Examples
  {
    id: "ta-001",
    title: "Authentication System Analysis",
    query: "How does the authentication system work in this codebase? What are the main components and security measures implemented?",
    description: "Analyze the authentication flow, security patterns, and implementation details",
    agentType: "TECHNICAL_ARCHITECT",
    category: "Security",
    tags: ["authentication", "security", "architecture", "jwt", "oauth"],
    difficulty: "intermediate",
    expectedResponse: "The system implements JWT-based authentication with OAuth2 integration. Key components include token validation middleware, user session management, and secure password hashing using bcrypt."
  },
  {
    id: "ta-002",
    title: "Database Architecture Overview",
    query: "What is the database architecture and how are the data models structured? Show me the relationships between entities.",
    description: "Understand database design, entity relationships, and data flow patterns",
    agentType: "TECHNICAL_ARCHITECT",
    category: "Database",
    tags: ["database", "models", "relationships", "schema", "orm"],
    difficulty: "intermediate",
    expectedResponse: "The system uses a relational database with well-defined entity relationships. User entities connect to Profile and Session tables, with proper foreign key constraints and indexing strategies."
  },
  {
    id: "ta-003",
    title: "API Design Patterns",
    query: "What API design patterns are used in this project? How is error handling and validation implemented across endpoints?",
    description: "Examine REST API patterns, error handling strategies, and validation approaches",
    agentType: "TECHNICAL_ARCHITECT",
    category: "API Design",
    tags: ["api", "rest", "validation", "error-handling", "patterns"],
    difficulty: "advanced",
    expectedResponse: "The API follows RESTful principles with consistent error handling using HTTP status codes. Input validation is implemented using schema validators, and responses follow a standardized format."
  },
  {
    id: "ta-004",
    title: "Frontend Architecture Analysis",
    query: "How is the frontend architecture organized? What are the main components, state management patterns, and routing strategies?",
    description: "Analyze frontend structure, component hierarchy, and architectural decisions",
    agentType: "TECHNICAL_ARCHITECT",
    category: "Frontend",
    tags: ["frontend", "components", "state-management", "routing", "react"],
    difficulty: "intermediate",
    expectedResponse: "The frontend uses React with TypeScript, implementing a component-based architecture. State management is handled through Context API and custom hooks, with React Router for navigation."
  },
  {
    id: "ta-005",
    title: "Performance Optimization Strategies",
    query: "What performance optimization techniques are implemented? How is caching, lazy loading, and resource optimization handled?",
    description: "Identify performance patterns, optimization strategies, and bottleneck prevention",
    agentType: "TECHNICAL_ARCHITECT",
    category: "Performance",
    tags: ["performance", "optimization", "caching", "lazy-loading", "bundling"],
    difficulty: "advanced",
    expectedResponse: "Performance optimizations include code splitting, lazy loading of components, Redis caching for API responses, and optimized database queries with proper indexing."
  },

  // Task Planner Examples
  {
    id: "tp-001",
    title: "Feature Implementation Breakdown",
    query: "I need to implement a user notification system. Break this down into specific tasks with estimates and dependencies.",
    description: "Plan a new feature implementation with detailed task breakdown",
    agentType: "TASK_PLANNER",
    category: "Feature Planning",
    tags: ["planning", "tasks", "estimation", "dependencies", "notifications"],
    difficulty: "intermediate",
    expectedResponse: "Task breakdown: 1) Database schema design (4h), 2) Backend API endpoints (8h), 3) Frontend notification components (6h), 4) Real-time integration (4h), 5) Testing (4h). Total: 26 hours over 2 sprints."
  },
  {
    id: "tp-002",
    title: "Bug Fix Priority Assessment",
    query: "We have several reported bugs. Help me prioritize them and create a plan to address the most critical issues first.",
    description: "Prioritize and plan bug fixes based on severity and impact",
    agentType: "TASK_PLANNER",
    category: "Bug Management",
    tags: ["bugs", "prioritization", "planning", "critical", "fixes"],
    difficulty: "beginner",
    expectedResponse: "Priority order: 1) Security vulnerabilities (immediate), 2) Data corruption issues (high), 3) Performance bottlenecks (medium), 4) UI inconsistencies (low). Estimated timeline: 2-3 days for critical fixes."
  },
  {
    id: "tp-003",
    title: "Code Refactoring Strategy",
    query: "The authentication module needs refactoring. Create a step-by-step plan to modernize it without breaking existing functionality.",
    description: "Plan a safe refactoring approach with minimal disruption",
    agentType: "TASK_PLANNER",
    category: "Refactoring",
    tags: ["refactoring", "modernization", "safety", "planning", "authentication"],
    difficulty: "advanced",
    expectedResponse: "Refactoring plan: 1) Add comprehensive tests (6h), 2) Extract interfaces (4h), 3) Implement new auth service (12h), 4) Gradual migration (8h), 5) Remove legacy code (4h). Use feature flags for safe rollout."
  },
  {
    id: "tp-004",
    title: "Sprint Planning Assistance",
    query: "Help me plan the next sprint. We have 40 story points capacity and need to balance new features with technical debt.",
    description: "Balance feature development with technical debt in sprint planning",
    agentType: "TASK_PLANNER",
    category: "Sprint Planning",
    tags: ["sprint", "planning", "capacity", "technical-debt", "features"],
    difficulty: "intermediate",
    expectedResponse: "Recommended split: 60% new features (24 points), 30% technical debt (12 points), 10% buffer (4 points). Focus on high-impact, low-effort technical debt items to maintain velocity."
  },
  {
    id: "tp-005",
    title: "Testing Strategy Development",
    query: "We need to improve our testing coverage. Create a comprehensive testing strategy with timelines and resource requirements.",
    description: "Develop a complete testing strategy and implementation plan",
    agentType: "TASK_PLANNER",
    category: "Testing",
    tags: ["testing", "strategy", "coverage", "quality", "automation"],
    difficulty: "advanced",
    expectedResponse: "Testing strategy: 1) Unit tests for core modules (16h), 2) Integration tests for APIs (12h), 3) E2E tests for critical flows (20h), 4) Performance testing setup (8h). Target: 80% coverage in 6 weeks."
  },

  // RAG Retrieval Examples
  {
    id: "rag-001",
    title: "Find Authentication Code",
    query: "Show me all the authentication-related code files and functions in the project.",
    description: "Locate specific code files and functions related to authentication",
    agentType: "RAG_RETRIEVAL",
    category: "Code Search",
    tags: ["search", "authentication", "functions", "files", "code"],
    difficulty: "beginner",
    expectedResponse: "Found authentication code in: auth/middleware.py (validateToken function), auth/service.py (login, logout methods), and auth/models.py (User, Session classes). Total: 8 files, 23 functions."
  },
  {
    id: "rag-002",
    title: "Configuration Files Lookup",
    query: "Find all configuration files and show me the database connection settings.",
    description: "Locate configuration files and extract specific settings",
    agentType: "RAG_RETRIEVAL",
    category: "Configuration",
    tags: ["config", "database", "settings", "environment", "files"],
    difficulty: "beginner",
    expectedResponse: "Configuration files found: config/database.yml, .env.example, settings/production.py. Database settings include host, port, credentials, and connection pool configuration."
  },
  {
    id: "rag-003",
    title: "API Endpoint Documentation",
    query: "List all API endpoints related to user management and show their documentation or comments.",
    description: "Find API endpoints with their documentation and usage examples",
    agentType: "RAG_RETRIEVAL",
    category: "API Documentation",
    tags: ["api", "endpoints", "documentation", "users", "management"],
    difficulty: "intermediate",
    expectedResponse: "User management endpoints: GET /api/users (list users), POST /api/users (create user), PUT /api/users/:id (update user), DELETE /api/users/:id (delete user). Each includes parameter validation and response schemas."
  },
  {
    id: "rag-004",
    title: "Error Handling Patterns",
    query: "Find examples of error handling throughout the codebase. Show me the different patterns used.",
    description: "Locate error handling code and identify common patterns",
    agentType: "RAG_RETRIEVAL",
    category: "Error Handling",
    tags: ["errors", "exceptions", "handling", "patterns", "examples"],
    difficulty: "intermediate",
    expectedResponse: "Error handling patterns found: try-catch blocks in services, custom exception classes, middleware error handlers, and standardized error response formats. 45 examples across 12 files."
  },
  {
    id: "rag-005",
    title: "Test Files and Coverage",
    query: "Show me all test files and identify which parts of the codebase have good test coverage.",
    description: "Find test files and analyze test coverage across the project",
    agentType: "RAG_RETRIEVAL",
    category: "Testing",
    tags: ["tests", "coverage", "files", "quality", "validation"],
    difficulty: "intermediate",
    expectedResponse: "Test files found: 23 unit tests, 12 integration tests, 5 E2E tests. High coverage in auth module (95%), moderate in API layer (70%), low in frontend components (45%)."
  },
  {
    id: "rag-006",
    title: "Dependencies and Imports",
    query: "What external libraries and dependencies are used in this project? Show me the import statements and package files.",
    description: "Analyze project dependencies and external library usage",
    agentType: "RAG_RETRIEVAL",
    category: "Dependencies",
    tags: ["dependencies", "libraries", "imports", "packages", "external"],
    difficulty: "beginner",
    expectedResponse: "Dependencies include: React 18.2.0, Express 4.18.0, PostgreSQL driver, JWT library, bcrypt for hashing. Found in package.json, requirements.txt, and 156 import statements across the codebase."
  },
  {
    id: "rag-007",
    title: "Database Queries and Models",
    query: "Find all database queries and model definitions. Show me how data is accessed and manipulated.",
    description: "Locate database-related code including queries and model definitions",
    agentType: "RAG_RETRIEVAL",
    category: "Database",
    tags: ["database", "queries", "models", "orm", "data"],
    difficulty: "intermediate",
    expectedResponse: "Database code found: 15 model definitions in models/, 34 query functions in services/, 8 migration files. Uses ORM for most operations with some raw SQL for complex queries."
  },
  {
    id: "rag-008",
    title: "Security Implementation Details",
    query: "Find all security-related code including validation, sanitization, and protection mechanisms.",
    description: "Locate security implementations and protection measures",
    agentType: "RAG_RETRIEVAL",
    category: "Security",
    tags: ["security", "validation", "sanitization", "protection", "safety"],
    difficulty: "advanced",
    expectedResponse: "Security implementations: input validation middleware, SQL injection prevention, XSS protection, CSRF tokens, rate limiting, password hashing, and secure session management. 28 security functions identified."
  }
]

/**
 * Get examples filtered by agent type
 */
export function getExamplesByAgent(agentType: string): QueryExample[] {
  return queryExamples.filter(example => example.agentType === agentType)
}

/**
 * Get examples filtered by category
 */
export function getExamplesByCategory(category: string): QueryExample[] {
  return queryExamples.filter(example => example.category === category)
}

/**
 * Get examples filtered by difficulty
 */
export function getExamplesByDifficulty(difficulty: string): QueryExample[] {
  return queryExamples.filter(example => example.difficulty === difficulty)
}

/**
 * Search examples by query text, title, or tags
 */
export function searchExamples(searchTerm: string): QueryExample[] {
  const term = searchTerm.toLowerCase()
  return queryExamples.filter(example => 
    example.title.toLowerCase().includes(term) ||
    example.query.toLowerCase().includes(term) ||
    example.description.toLowerCase().includes(term) ||
    example.tags.some(tag => tag.toLowerCase().includes(term))
  )
}

/**
 * Get random examples for suggestions
 */
export function getRandomExamples(count: number = 3): QueryExample[] {
  const shuffled = [...queryExamples].sort(() => 0.5 - Math.random())
  return shuffled.slice(0, count)
}
