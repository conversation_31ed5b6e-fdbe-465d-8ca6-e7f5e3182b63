/**
 * API Mocking Utilities
 * 
 * This module provides utilities for mocking API responses in tests,
 * including proper Response object creation and error simulation.
 */

/**
 * Create a mock Response object for testing
 */
export function createMockApiResponse(
  data: any,
  options: {
    status?: number
    statusText?: string
    headers?: Record<string, string>
  } = {}
): Response {
  const {
    status = 200,
    statusText = "OK",
    headers = {},
  } = options

  const response = new Response(JSON.stringify(data), {
    status,
    statusText,
    headers: {
      "Content-Type": "application/json",
      ...headers,
    },
  })

  return response
}

/**
 * Create a mock error response
 */
export function createMockErrorResponse(
  message: string,
  status: number = 500
): Response {
  return createMockApiResponse(
    { detail: message },
    { status, statusText: getStatusText(status) }
  )
}

/**
 * Get status text for common HTTP status codes
 */
function getStatusText(status: number): string {
  const statusTexts: Record<number, string> = {
    200: "OK",
    201: "Created",
    400: "Bad Request",
    401: "Unauthorized",
    403: "Forbidden",
    404: "Not Found",
    429: "Too Many Requests",
    500: "Internal Server Error",
    502: "Bad Gateway",
    503: "Service Unavailable",
    504: "Gateway Timeout",
  }

  return statusTexts[status] || "Unknown"
}

/**
 * Mock fetch implementation for testing
 */
export function createMockFetch() {
  return vi.fn()
}

/**
 * Setup fetch mock with default responses
 */
export function setupFetchMock(mockFetch: any) {
  // Default successful responses
  const defaultResponses = {
    "api/query": {
      result_markdown: "# Test Response",
      structured: { test: true },
      agent_type: "TECHNICAL_ARCHITECT",
      confidence: 0.95,
      sources: ["test.py:1-10"],
      session_id: "test-session",
    },
    "api/ingest": {
      status: "completed",
      repository: "https://github.com/test/repo",
      processed_files: 100,
      chunks_created: 500,
      embeddings_generated: 500,
      processing_time: 30.5,
    },
    "api/status": {
      api: "operational",
      agents: {
        ORCHESTRATOR: "operational",
        TECHNICAL_ARCHITECT: "operational",
      },
      vector_store: "operational",
      ingestion_pipeline: "operational",
      active_sessions: 5,
    },
    "health": {
      status: "healthy",
      service: "llm-rag-backend",
      version: "0.1.0",
      environment: "test",
      timestamp: "2024-01-01T00:00:00Z",
    },
  }

  mockFetch.mockImplementation((url: string) => {
    // Extract endpoint from URL
    const endpoint = url.replace(/^https?:\/\/[^\/]+\//, "")
    
    if (endpoint in defaultResponses) {
      return Promise.resolve(
        createMockApiResponse(defaultResponses[endpoint as keyof typeof defaultResponses])
      )
    }

    // Default to 404 for unknown endpoints
    return Promise.resolve(createMockErrorResponse("Not Found", 404))
  })

  return mockFetch
}

// Re-export vi for convenience
import { vi } from "vitest"
export { vi }
