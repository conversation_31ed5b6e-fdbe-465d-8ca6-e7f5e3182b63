/**
 * Testing Utilities
 * 
 * This module provides custom testing utilities, render functions, and helpers
 * for testing React components with proper provider setup and common patterns
 * used throughout the application.
 */

import React from "react"
import { render, type RenderOptions } from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { ThemeProvider } from "next-themes"
import { vi } from "vitest"
import type { ReactElement } from "react"

/**
 * Custom render options extending RTL's RenderOptions
 */
interface CustomRenderOptions extends Omit<RenderOptions, "wrapper"> {
  /**
   * Initial theme for ThemeProvider
   */
  initialTheme?: string
  
  /**
   * Custom QueryClient instance for testing
   */
  queryClient?: QueryClient
  
  /**
   * Whether to wrap with providers (default: true)
   */
  withProviders?: boolean
}

/**
 * Creates a new QueryClient instance for testing
 * Configured with appropriate defaults for test environment
 */
export function createTestQueryClient(): QueryClient {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // Disable retries in tests
        retry: false,
        // Disable caching in tests
        staleTime: 0,
        gcTime: 0,
      },
      mutations: {
        // Disable retries in tests
        retry: false,
      },
    },
  })
}

/**
 * Test wrapper component that provides all necessary context providers
 */
function TestWrapper({
  children,
  queryClient,
  initialTheme = "light",
}: {
  children: React.ReactNode
  queryClient?: QueryClient
  initialTheme?: string
}) {
  const testQueryClient = queryClient || createTestQueryClient()

  return (
    <QueryClientProvider client={testQueryClient}>
      <ThemeProvider
        attribute="class"
        defaultTheme={initialTheme}
        enableSystem={false}
        disableTransitionOnChange
      >
        {children}
      </ThemeProvider>
    </QueryClientProvider>
  )
}

/**
 * Custom render function that wraps components with necessary providers
 * 
 * @param ui - The React element to render
 * @param options - Custom render options
 * @returns RTL render result with additional utilities
 */
export function renderWithProviders(
  ui: ReactElement,
  {
    initialTheme = "light",
    queryClient,
    withProviders = true,
    ...renderOptions
  }: CustomRenderOptions = {}
) {
  const Wrapper = withProviders
    ? ({ children }: { children: React.ReactNode }) => (
        <TestWrapper queryClient={queryClient} initialTheme={initialTheme}>
          {children}
        </TestWrapper>
      )
    : undefined

  const result = render(ui, { wrapper: Wrapper, ...renderOptions })

  return {
    ...result,
    // Add custom utilities
    queryClient: queryClient || createTestQueryClient(),
  }
}

/**
 * Utility function to create mock API responses
 */
export function createMockApiResponse<T>(data: T, options?: {
  status?: number
  statusText?: string
  headers?: Record<string, string>
}) {
  const { status = 200, statusText = "OK", headers = {} } = options || {}
  
  return {
    ok: status >= 200 && status < 300,
    status,
    statusText,
    headers: new Headers(headers),
    json: async () => data,
    text: async () => JSON.stringify(data),
    clone: () => createMockApiResponse(data, options),
  } as Response
}

/**
 * Utility function to create mock fetch implementation
 */
export function createMockFetch(responses: Record<string, any>) {
  return vi.fn().mockImplementation((url: string) => {
    const response = responses[url]
    if (!response) {
      return Promise.reject(new Error(`No mock response for ${url}`))
    }
    return Promise.resolve(createMockApiResponse(response))
  })
}

/**
 * Utility to wait for async operations to complete
 */
export async function waitForAsync() {
  await new Promise((resolve) => setTimeout(resolve, 0))
}

/**
 * Utility to create mock user event with proper setup
 */
export { userEvent } from "@testing-library/user-event"

/**
 * Re-export commonly used testing utilities
 */
export {
  screen,
  waitFor,
  waitForElementToBeRemoved,
  within,
  fireEvent,
} from "@testing-library/react"

/**
 * Re-export the custom render as the default render
 */
export { renderWithProviders as render }

/**
 * Export the original render for cases where providers are not needed
 */
export { render as renderWithoutProviders } from "@testing-library/react"
