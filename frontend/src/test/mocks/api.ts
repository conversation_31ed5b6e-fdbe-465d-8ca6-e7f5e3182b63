/**
 * API Mocking Utilities
 * 
 * This module provides mock data and utilities for testing API interactions,
 * including mock responses for all API endpoints and helper functions for
 * setting up different test scenarios.
 */

import { vi } from "vitest"
import type {
  QueryRequest,
  QueryResponse,
  IngestionRequest,
  IngestionResponse,
  StatusResponse,
} from "@/types/api"

/**
 * Mock data for API responses
 */
export const mockApiData = {
  /**
   * Mock QueryResponse data
   */
  queryResponse: {
    result_markdown: "# Test Response\n\nThis is a test response with **markdown** formatting.",
    structured: {
      components: ["auth", "api"],
      complexity: "medium",
      files_analyzed: 5,
    },
    agent_type: "TECHNICAL_ARCHITECT",
    confidence: 0.85,
    sources: ["src/auth/jwt.py:1-50", "src/api/main.py:10-30"],
    processing_time: 2.34,
    session_id: "test-session-123",
  } as QueryResponse,

  /**
   * Mock IngestionResponse data
   */
  ingestionResponse: {
    status: "completed",
    repository: "https://github.com/test/repo",
    processed_files: 150,
    chunks_created: 1200,
    embeddings_generated: 1200,
    processing_time: 45.7,
  } as IngestionResponse,

  /**
   * Mock StatusResponse data
   */
  statusResponse: {
    api: "operational",
    agents: {
      ORCHESTRATOR: "operational",
      TECHNICAL_ARCHITECT: "operational",
      TASK_PLANNER: "operational",
      RAG_RETRIEVAL: "operational",
    },
    vector_store: "operational",
    ingestion_pipeline: "operational",
    active_sessions: 5,
  } as StatusResponse,

  /**
   * Mock error responses
   */
  errorResponses: {
    badRequest: {
      detail: "Invalid request parameters",
    },
    unauthorized: {
      detail: "Authentication required",
    },
    notFound: {
      detail: "Resource not found",
    },
    internalServerError: {
      detail: "Internal server error",
    },
    agentError: {
      detail: "Agent processing failed: timeout",
    },
    ingestionError: {
      detail: "Repository ingestion failed: invalid URL",
    },
  },
} as const

/**
 * Mock API endpoints configuration
 */
export const mockApiEndpoints = {
  query: "/api/query",
  ingest: "/api/ingest", 
  status: "/api/status",
} as const

/**
 * Creates a mock fetch function with predefined responses
 */
export function createMockApiClient(overrides?: {
  queryResponse?: Partial<QueryResponse>
  ingestionResponse?: Partial<IngestionResponse>
  statusResponse?: Partial<StatusResponse>
  shouldFail?: boolean
  errorStatus?: number
}) {
  const {
    queryResponse = {},
    ingestionResponse = {},
    statusResponse = {},
    shouldFail = false,
    errorStatus = 500,
  } = overrides || {}

  return vi.fn().mockImplementation(async (url: string, options?: RequestInit) => {
    // Simulate network delay
    await new Promise((resolve) => setTimeout(resolve, 10))

    if (shouldFail) {
      throw new Error("Network error")
    }

    const method = options?.method || "GET"
    
    // Handle different endpoints
    if (url.includes(mockApiEndpoints.query) && method === "POST") {
      return {
        ok: true,
        status: 200,
        json: async () => ({
          ...mockApiData.queryResponse,
          ...queryResponse,
        }),
      }
    }

    if (url.includes(mockApiEndpoints.ingest) && method === "POST") {
      return {
        ok: true,
        status: 200,
        json: async () => ({
          ...mockApiData.ingestionResponse,
          ...ingestionResponse,
        }),
      }
    }

    if (url.includes(mockApiEndpoints.status) && method === "GET") {
      return {
        ok: true,
        status: 200,
        json: async () => ({
          ...mockApiData.statusResponse,
          ...statusResponse,
        }),
      }
    }

    // Default error response
    return {
      ok: false,
      status: errorStatus,
      json: async () => mockApiData.errorResponses.notFound,
    }
  })
}

/**
 * Creates mock request data for testing
 */
export function createMockRequestData() {
  return {
    queryRequest: {
      query: "How does the authentication system work?",
      session_id: "test-session-123",
      user_id: "test-user-456",
      repository: "https://github.com/test/repo",
    } as QueryRequest,

    ingestionRequest: {
      repository_url: "https://github.com/test/repo",
      branch: "main",
      force_refresh: false,
    } as IngestionRequest,
  }
}

/**
 * Utility to mock successful API responses
 */
export function mockSuccessfulApiResponses() {
  const mockFetch = createMockApiClient()
  global.fetch = mockFetch
  return mockFetch
}

/**
 * Utility to mock failed API responses
 */
export function mockFailedApiResponses(errorStatus = 500) {
  const mockFetch = createMockApiClient({
    shouldFail: false,
    errorStatus,
  })
  global.fetch = mockFetch
  return mockFetch
}

/**
 * Utility to mock network errors
 */
export function mockNetworkError() {
  const mockFetch = createMockApiClient({ shouldFail: true })
  global.fetch = mockFetch
  return mockFetch
}

/**
 * Reset all API mocks
 */
export function resetApiMocks() {
  vi.clearAllMocks()
}
