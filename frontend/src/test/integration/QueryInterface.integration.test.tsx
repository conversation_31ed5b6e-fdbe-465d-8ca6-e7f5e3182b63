import { describe, it, expect, vi, beforeEach, afterEach } from "vitest"
import { render, screen, userEvent, waitFor } from "@/test/utils"
import { QueryForm } from "@/components/query/QueryForm"
import { ConversationHistory } from "@/components/conversation/ConversationHistory"
import { QueryExamples } from "@/components/examples/QueryExamples"
import { SessionProvider } from "@/providers/session-provider"
import { QueryProvider } from "@/providers/query-provider"
import { queryExamples } from "@/data/queryExamples"
import * as apiHooks from "@/hooks/api"

// Mock the API hooks
vi.mock("@/hooks/api", () => ({
  useQueryMutation: vi.fn(),
}))

// Mock successful query response
const mockQueryResponse = {
  result_markdown: "# Authentication System\n\nThe authentication system uses JWT tokens...",
  structured: {},
  agent_type: "TECHNICAL_ARCHITECT",
  confidence: 0.95,
  sources: ["src/auth/jwt.py:1-50", "src/auth/middleware.py:20-45"],
  session_id: "test-session-123",
  processing_time: 2.3,
}

const mockQueryMutation = {
  mutate: vi.fn(),
  isPending: false,
  error: null,
  data: null,
  isSuccess: false,
  isError: false,
}

describe("Query Interface Integration", () => {
  beforeEach(() => {
    vi.mocked(apiHooks.useQueryMutation).mockReturnValue(mockQueryMutation)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  const renderQueryInterface = () => {
    return render(
      <QueryProvider>
        <SessionProvider>
          <div className="space-y-6">
            <QueryForm showAdvancedOptions={true} />
            <ConversationHistory showTimestamps={true} showActions={true} />
            <QueryExamples 
              examples={queryExamples.slice(0, 5)} 
              showSearch={true} 
              showFilters={true}
              groupByAgent={true}
            />
          </div>
        </SessionProvider>
      </QueryProvider>
    )
  }

  describe("Complete Query Workflow", () => {
    it("allows user to submit query and see response in conversation", async () => {
      const user = userEvent.setup()
      
      // Mock successful submission
      const mockSuccessfulMutation = {
        ...mockQueryMutation,
        mutate: vi.fn((request, options) => {
          options?.onSuccess?.(mockQueryResponse, request)
        }),
      }
      vi.mocked(apiHooks.useQueryMutation).mockReturnValue(mockSuccessfulMutation)
      
      renderQueryInterface()
      
      // Submit a query
      const queryInput = screen.getByRole("textbox", { name: /query/i })
      const submitButton = screen.getByRole("button", { name: /submit query/i })
      
      await user.type(queryInput, "How does authentication work?")
      await user.click(submitButton)
      
      // Verify API call
      expect(mockSuccessfulMutation.mutate).toHaveBeenCalledWith(
        expect.objectContaining({
          query: "How does authentication work?",
        })
      )
      
      // Verify form reset
      await waitFor(() => {
        expect(queryInput).toHaveValue("")
      })
      
      // Verify conversation history shows the exchange
      expect(screen.getByText("How does authentication work?")).toBeInTheDocument()
      expect(screen.getByText(/Authentication System/)).toBeInTheDocument()
    })

    it("handles query submission with advanced options", async () => {
      const user = userEvent.setup()
      
      const mockSuccessfulMutation = {
        ...mockQueryMutation,
        mutate: vi.fn((request, options) => {
          options?.onSuccess?.(mockQueryResponse, request)
        }),
      }
      vi.mocked(apiHooks.useQueryMutation).mockReturnValue(mockSuccessfulMutation)
      
      renderQueryInterface()
      
      // Fill in query and advanced options
      const queryInput = screen.getByRole("textbox", { name: /query/i })
      const repoInput = screen.getByLabelText(/repository/i)
      const submitButton = screen.getByRole("button", { name: /submit query/i })
      
      await user.type(queryInput, "Analyze the authentication system")
      await user.type(repoInput, "https://github.com/owner/repo")
      await user.click(submitButton)
      
      // Verify API call includes repository
      expect(mockSuccessfulMutation.mutate).toHaveBeenCalledWith(
        expect.objectContaining({
          query: "Analyze the authentication system",
          repository: "https://github.com/owner/repo",
        })
      )
    })

    it("displays error messages when query submission fails", async () => {
      const user = userEvent.setup()
      
      const mockErrorMutation = {
        ...mockQueryMutation,
        mutate: vi.fn((request, options) => {
          options?.onError?.(new Error("Network error"), request)
        }),
        error: new Error("Network error"),
        isError: true,
      }
      vi.mocked(apiHooks.useQueryMutation).mockReturnValue(mockErrorMutation)
      
      renderQueryInterface()
      
      const queryInput = screen.getByRole("textbox", { name: /query/i })
      const submitButton = screen.getByRole("button", { name: /submit query/i })
      
      await user.type(queryInput, "Test query")
      await user.click(submitButton)
      
      // Verify error is displayed
      expect(screen.getByText("Network error")).toBeInTheDocument()
    })
  })

  describe("Example Query Integration", () => {
    it("allows user to select example and insert into form", async () => {
      const user = userEvent.setup()
      renderQueryInterface()
      
      // Find and click an example "Use Query" button
      const useQueryButtons = screen.getAllByText("Use Query")
      await user.click(useQueryButtons[0])
      
      // Verify query is inserted into form
      const queryInput = screen.getByRole("textbox", { name: /query/i })
      expect(queryInput).toHaveValue(expect.stringContaining("authentication"))
    })

    it("filters examples and allows selection", async () => {
      const user = userEvent.setup()
      renderQueryInterface()
      
      // Search for specific examples
      const searchInput = screen.getByPlaceholderText("Search examples...")
      await user.type(searchInput, "authentication")
      
      await waitFor(() => {
        // Should show filtered results
        expect(screen.getByText(/examples/)).toBeInTheDocument()
      })
      
      // Select an example
      const useQueryButtons = screen.getAllByText("Use Query")
      if (useQueryButtons.length > 0) {
        await user.click(useQueryButtons[0])
        
        const queryInput = screen.getByRole("textbox", { name: /query/i })
        expect(queryInput.value.length).toBeGreaterThan(0)
      }
    })

    it("switches between agent types in examples", async () => {
      const user = userEvent.setup()
      renderQueryInterface()
      
      // Click on Task Planner tab
      const taskPlannerTab = screen.queryByText(/Task Planner/)
      if (taskPlannerTab) {
        await user.click(taskPlannerTab)
        
        // Should show task planner examples
        await waitFor(() => {
          expect(screen.getByText(/planning/i)).toBeInTheDocument()
        })
      }
    })
  })

  describe("Conversation History Integration", () => {
    it("displays conversation history with proper formatting", async () => {
      const user = userEvent.setup()
      
      // Mock session with existing conversation
      const mockSessionWithHistory = {
        session: {
          sessionId: "test-session-123",
          userId: "test-user-456",
          conversationHistory: [
            {
              type: "user" as const,
              content: "Previous question",
              metadata: { timestamp: new Date() },
            },
            {
              type: "assistant" as const,
              content: "Previous answer",
              metadata: { 
                timestamp: new Date(),
                agent_type: "TECHNICAL_ARCHITECT",
                confidence: 0.9,
              },
            },
          ],
          isActive: true,
          createdAt: new Date(),
          lastActivity: new Date(),
          metadata: {},
        },
        isActive: true,
        messageCount: 2,
        lastActivity: new Date(),
        createSession: vi.fn(),
        endSession: vi.fn(),
        updateSession: vi.fn(),
        addMessage: vi.fn(),
        updateMessage: vi.fn(),
        removeMessage: vi.fn(),
        clearHistory: vi.fn(),
        setUserId: vi.fn(),
        setMetadata: vi.fn(),
      }
      
      render(
        <QueryProvider>
          <SessionProvider>
            <div className="space-y-6">
              <QueryForm />
              <ConversationHistory showTimestamps={true} />
            </div>
          </SessionProvider>
        </QueryProvider>,
        {
          providerProps: {
            sessionProvider: { value: mockSessionWithHistory },
          },
        }
      )
      
      // Verify existing conversation is displayed
      expect(screen.getByText("Previous question")).toBeInTheDocument()
      expect(screen.getByText("Previous answer")).toBeInTheDocument()
      expect(screen.getByText("2 messages in this session")).toBeInTheDocument()
    })

    it("shows empty conversation state initially", () => {
      renderQueryInterface()
      
      expect(screen.getByText("No conversation yet")).toBeInTheDocument()
      expect(screen.getByText(/Start by submitting a query/)).toBeInTheDocument()
    })
  })

  describe("Form Validation Integration", () => {
    it("prevents submission with invalid data", async () => {
      const user = userEvent.setup()
      renderQueryInterface()
      
      // Try to submit empty form
      const submitButton = screen.getByRole("button", { name: /submit query/i })
      await user.click(submitButton)
      
      // Should not call API
      expect(mockQueryMutation.mutate).not.toHaveBeenCalled()
    })

    it("validates repository URL in advanced options", async () => {
      const user = userEvent.setup()
      renderQueryInterface()
      
      const queryInput = screen.getByRole("textbox", { name: /query/i })
      const repoInput = screen.getByLabelText(/repository/i)
      
      await user.type(queryInput, "Valid query")
      await user.type(repoInput, "invalid-url")
      await user.tab()
      
      await waitFor(() => {
        expect(screen.getByText(/must be a valid url/i)).toBeInTheDocument()
      })
      
      // Submit should be disabled or show error
      const submitButton = screen.getByRole("button", { name: /submit query/i })
      await user.click(submitButton)
      
      expect(mockQueryMutation.mutate).not.toHaveBeenCalled()
    })
  })

  describe("Loading States Integration", () => {
    it("shows loading state during query submission", async () => {
      const user = userEvent.setup()
      
      const mockPendingMutation = {
        ...mockQueryMutation,
        isPending: true,
      }
      vi.mocked(apiHooks.useQueryMutation).mockReturnValue(mockPendingMutation)
      
      renderQueryInterface()
      
      // Should show loading state
      expect(screen.getByText("Processing...")).toBeInTheDocument()
      expect(screen.getByRole("button", { name: /processing/i })).toBeDisabled()
    })
  })

  describe("Accessibility Integration", () => {
    it("maintains proper focus management throughout workflow", async () => {
      const user = userEvent.setup()
      renderQueryInterface()
      
      // Tab through the interface
      await user.tab() // Query input
      expect(screen.getByRole("textbox", { name: /query/i })).toHaveFocus()
      
      await user.tab() // Repository input (if advanced options shown)
      await user.tab() // Submit button
      expect(screen.getByRole("button", { name: /submit query/i })).toHaveFocus()
    })

    it("provides proper ARIA labels and descriptions", () => {
      renderQueryInterface()
      
      const queryInput = screen.getByRole("textbox", { name: /query/i })
      expect(queryInput).toHaveAttribute("aria-describedby")
      
      const characterCount = screen.getByText("0/5000")
      expect(characterCount).toHaveAttribute("aria-live", "polite")
    })

    it("announces form validation errors", async () => {
      const user = userEvent.setup()
      renderQueryInterface()
      
      const queryInput = screen.getByRole("textbox", { name: /query/i })
      const submitButton = screen.getByRole("button", { name: /submit query/i })
      
      await user.click(queryInput)
      await user.tab()
      await user.click(submitButton)
      
      await waitFor(() => {
        const errorMessage = screen.queryByText(/query is required/i)
        if (errorMessage) {
          expect(errorMessage).toBeInTheDocument()
          expect(queryInput).toHaveAttribute("aria-invalid", "true")
        }
      })
    })
  })
})
