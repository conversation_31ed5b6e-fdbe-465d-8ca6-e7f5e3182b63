/**
 * Repository Management Integration Tests
 * 
 * End-to-end integration tests for the repository management functionality
 * including page interactions, API calls, and user workflows.
 */

import { describe, it, expect, vi, beforeEach, afterEach } from "vitest"
import { render, screen, userEvent, waitFor } from "@/test/utils"
import RepositoriesPage from "@/app/repositories/page"
import * as apiHooks from "@/hooks/api"
import type { 
  RepositoryListResponse, 
  RepositoryMetadata, 
  RepositoryStatsResponse,
  IngestionProgress 
} from "@/types/api"

// Mock all API hooks
vi.mock("@/hooks/api", () => ({
  useIngestionMutation: vi.fn(),
  useActiveIngestions: vi.fn(),
  useRepositoryStats: vi.fn(),
  useRepositoryList: vi.fn(),
}))

// Mock the toast hook
vi.mock("@/hooks/useToast", () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}))

// Mock the RepositorySelector component
vi.mock("@/components/query/RepositorySelector", () => ({
  RepositorySelector: ({ onChange }: { onChange: (data: any) => void }) => (
    <div data-testid="repository-selector">
      <button 
        onClick={() => onChange({
          url: "https://github.com/test/repo",
          branch: "main",
          force_refresh: false,
        })}
      >
        Submit Repository
      </button>
    </div>
  ),
}))

describe("Repository Management Integration", () => {
  const mockRepositories: RepositoryMetadata[] = [
    {
      id: "repo-1",
      url: new URL("https://github.com/test/repo1"),
      name: "repo1",
      owner: "test",
      branch: "main",
      commit_sha: "abc123",
      is_private: false,
      description: "Test repository 1",
      language: "TypeScript",
      size: 1024,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-02T00:00:00Z",
      ingested_at: "2024-01-02T00:00:00Z",
      status: "completed",
      processed_files: 50,
      chunks_created: 200,
      embeddings_generated: 200,
      processing_time: 120,
    },
    {
      id: "repo-2",
      url: new URL("https://github.com/test/repo2"),
      name: "repo2",
      owner: "test",
      branch: "develop",
      commit_sha: "def456",
      is_private: true,
      description: "Test repository 2",
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-03T00:00:00Z",
      ingested_at: "2024-01-03T00:00:00Z",
      status: "processing",
      processed_files: 25,
      chunks_created: 100,
      embeddings_generated: 100,
      processing_time: 60,
    },
  ]

  const mockRepositoryListResponse: RepositoryListResponse = {
    repositories: mockRepositories,
    total_count: 2,
    page: 1,
    page_size: 20,
    total_pages: 1,
  }

  const mockStats: RepositoryStatsResponse = {
    total_repositories: 2,
    total_files_processed: 75,
    total_embeddings: 300,
    storage_usage_mb: 150.5,
    repositories_by_status: {
      completed: 1,
      processing: 1,
      failed: 0,
      pending: 0,
      cancelled: 0,
    },
  }

  const mockActiveIngestions: IngestionProgress[] = [
    {
      repository_id: "repo-2",
      status: "processing",
      stage: "chunking_content",
      current_step: "Processing TypeScript files",
      progress_percentage: 65.5,
      elapsed_time: 120,
      estimated_completion: new Date(Date.now() + 60000).toISOString(),
      files_discovered: 50,
      files_processed: 32,
      files_filtered: 3,
      chunks_created: 128,
      embeddings_generated: 100,
      errors: [],
      warnings: [],
    },
  ]

  const mockUseIngestionMutation = vi.mocked(apiHooks.useIngestionMutation)
  const mockUseActiveIngestions = vi.mocked(apiHooks.useActiveIngestions)
  const mockUseRepositoryStats = vi.mocked(apiHooks.useRepositoryStats)
  const mockUseRepositoryList = vi.mocked(apiHooks.useRepositoryList)

  beforeEach(() => {
    vi.clearAllMocks()
    
    // Setup default mocks
    mockUseIngestionMutation.mockReturnValue({
      mutate: vi.fn(),
      isPending: false,
      isError: false,
      error: null,
    } as any)

    mockUseActiveIngestions.mockReturnValue({
      data: mockActiveIngestions,
    } as any)

    mockUseRepositoryStats.mockReturnValue({
      data: mockStats,
    } as any)

    mockUseRepositoryList.mockReturnValue({
      data: mockRepositoryListResponse,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    } as any)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe("Page Rendering", () => {
    it("should render the repository management page", () => {
      render(<RepositoriesPage />)
      
      expect(screen.getByText("Repository Management")).toBeInTheDocument()
      expect(screen.getByText("Manage your ingested repositories, track progress, and perform bulk operations")).toBeInTheDocument()
    })

    it("should display statistics cards", () => {
      render(<RepositoriesPage />)
      
      expect(screen.getByText("Total Repositories")).toBeInTheDocument()
      expect(screen.getByText("2")).toBeInTheDocument()
      expect(screen.getByText("Files Processed")).toBeInTheDocument()
      expect(screen.getByText("75")).toBeInTheDocument()
      expect(screen.getByText("Embeddings Generated")).toBeInTheDocument()
      expect(screen.getByText("300")).toBeInTheDocument()
      expect(screen.getByText("Storage Used")).toBeInTheDocument()
      expect(screen.getByText("151 MB")).toBeInTheDocument()
    })

    it("should show tab navigation with badges", () => {
      render(<RepositoriesPage />)
      
      expect(screen.getByText("Repositories")).toBeInTheDocument()
      expect(screen.getByText("Active Ingestions")).toBeInTheDocument()
      expect(screen.getByText("Bulk Actions")).toBeInTheDocument()
      
      // Should show repository count badge
      expect(screen.getByText("2")).toBeInTheDocument()
      // Should show active ingestions badge
      expect(screen.getByText("1")).toBeInTheDocument()
    })
  })

  describe("Repository Addition", () => {
    it("should open add repository dialog", async () => {
      const user = userEvent.setup()
      render(<RepositoriesPage />)
      
      const addButton = screen.getByText("Add Repository")
      await user.click(addButton)
      
      expect(screen.getByText("Add New Repository")).toBeInTheDocument()
      expect(screen.getByTestId("repository-selector")).toBeInTheDocument()
    })

    it("should handle repository submission", async () => {
      const mockMutate = vi.fn()
      mockUseIngestionMutation.mockReturnValue({
        mutate: mockMutate,
        isPending: false,
        isError: false,
        error: null,
      } as any)

      const user = userEvent.setup()
      render(<RepositoriesPage />)
      
      const addButton = screen.getByText("Add Repository")
      await user.click(addButton)
      
      const submitButton = screen.getByText("Submit Repository")
      await user.click(submitButton)
      
      expect(mockMutate).toHaveBeenCalledWith({
        repository_url: "https://github.com/test/repo",
        branch: "main",
        force_refresh: false,
      })
    })

    it("should handle successful repository addition", async () => {
      const mockMutate = vi.fn()
      mockUseIngestionMutation.mockImplementation((options) => {
        // Simulate successful mutation
        if (options?.onSuccess) {
          options.onSuccess({
            repository: "test/repo",
            status: "started",
          }, {
            repository_url: "https://github.com/test/repo",
            branch: "main",
            force_refresh: false,
          }, undefined)
        }
        
        return {
          mutate: mockMutate,
          isPending: false,
          isError: false,
          error: null,
        } as any
      })

      const user = userEvent.setup()
      render(<RepositoriesPage />)
      
      const addButton = screen.getByText("Add Repository")
      await user.click(addButton)
      
      const submitButton = screen.getByText("Submit Repository")
      await user.click(submitButton)
      
      // Should switch to progress tab
      await waitFor(() => {
        expect(screen.getByRole("tab", { selected: true })).toHaveTextContent("Active Ingestions")
      })
    })
  })

  describe("Tab Navigation", () => {
    it("should switch between tabs", async () => {
      const user = userEvent.setup()
      render(<RepositoriesPage />)
      
      // Start on repositories tab
      expect(screen.getByText("repo1")).toBeInTheDocument()
      
      // Switch to active ingestions tab
      const progressTab = screen.getByText("Active Ingestions")
      await user.click(progressTab)
      
      expect(screen.getByText("Chunking Content")).toBeInTheDocument()
      
      // Switch to bulk actions tab
      const actionsTab = screen.getByText("Bulk Actions")
      await user.click(actionsTab)
      
      expect(screen.getByText("No repositories selected")).toBeInTheDocument()
    })

    it("should show repository list in repositories tab", async () => {
      render(<RepositoriesPage />)
      
      expect(screen.getByText("test/repo1")).toBeInTheDocument()
      expect(screen.getByText("test/repo2")).toBeInTheDocument()
      expect(screen.getByText("Test repository 1")).toBeInTheDocument()
      expect(screen.getByText("Test repository 2")).toBeInTheDocument()
    })

    it("should show active ingestions in progress tab", async () => {
      const user = userEvent.setup()
      render(<RepositoriesPage />)
      
      const progressTab = screen.getByText("Active Ingestions")
      await user.click(progressTab)
      
      expect(screen.getByText("Ingestion Progress")).toBeInTheDocument()
      expect(screen.getByText("Processing TypeScript files")).toBeInTheDocument()
      expect(screen.getByText("66%")).toBeInTheDocument() // Rounded from 65.5%
    })

    it("should show empty state when no active ingestions", async () => {
      mockUseActiveIngestions.mockReturnValue({
        data: [],
      } as any)

      const user = userEvent.setup()
      render(<RepositoriesPage />)
      
      const progressTab = screen.getByText("Active Ingestions")
      await user.click(progressTab)
      
      expect(screen.getByText("No active ingestions")).toBeInTheDocument()
      expect(screen.getByText("Add a new repository to start ingesting content")).toBeInTheDocument()
    })
  })

  describe("Repository Selection and Bulk Actions", () => {
    it("should handle repository selection", async () => {
      const user = userEvent.setup()
      render(<RepositoriesPage />)
      
      // Select first repository
      const checkboxes = screen.getAllByRole("checkbox")
      const firstRepoCheckbox = checkboxes[1] // Skip select all
      await user.click(firstRepoCheckbox)
      
      // Switch to bulk actions tab
      const actionsTab = screen.getByText("Bulk Actions")
      await user.click(actionsTab)
      
      expect(screen.getByText("1 repositories selected")).toBeInTheDocument()
    })

    it("should show bulk operations when repositories are selected", async () => {
      const user = userEvent.setup()
      render(<RepositoriesPage />)
      
      // Select all repositories
      const checkboxes = screen.getAllByRole("checkbox")
      const selectAllCheckbox = checkboxes[0]
      await user.click(selectAllCheckbox)
      
      // Switch to bulk actions tab
      const actionsTab = screen.getByText("Bulk Actions")
      await user.click(actionsTab)
      
      expect(screen.getByText("Re-ingest All (2)")).toBeInTheDocument()
      expect(screen.getByText("Delete All (2)")).toBeInTheDocument()
    })
  })

  describe("Error Handling", () => {
    it("should handle repository list loading errors", () => {
      mockUseRepositoryList.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: new Error("Failed to load"),
        refetch: vi.fn(),
      } as any)

      render(<RepositoriesPage />)
      
      expect(screen.getByText("Failed to load repositories")).toBeInTheDocument()
      expect(screen.getByText("Try Again")).toBeInTheDocument()
    })

    it("should handle ingestion errors", async () => {
      const mockMutate = vi.fn()
      mockUseIngestionMutation.mockImplementation((options) => {
        // Simulate error
        if (options?.onError) {
          options.onError(new Error("Ingestion failed"), {
            repository_url: "https://github.com/test/repo",
            branch: "main",
            force_refresh: false,
          }, undefined)
        }
        
        return {
          mutate: mockMutate,
          isPending: false,
          isError: true,
          error: new Error("Ingestion failed"),
        } as any
      })

      const user = userEvent.setup()
      render(<RepositoriesPage />)
      
      const addButton = screen.getByText("Add Repository")
      await user.click(addButton)
      
      const submitButton = screen.getByText("Submit Repository")
      await user.click(submitButton)
      
      // Error should be handled (toast would be shown in real app)
      expect(mockMutate).toHaveBeenCalled()
    })
  })

  describe("Real-time Updates", () => {
    it("should show loading states appropriately", () => {
      mockUseRepositoryList.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
        refetch: vi.fn(),
      } as any)

      render(<RepositoriesPage />)
      
      expect(screen.getByText("Loading repositories...")).toBeInTheDocument()
    })

    it("should update when data changes", () => {
      const { rerender } = render(<RepositoriesPage />)
      
      // Initial state
      expect(screen.getByText("2")).toBeInTheDocument() // Total repositories
      
      // Update stats
      mockUseRepositoryStats.mockReturnValue({
        data: { ...mockStats, total_repositories: 3 },
      } as any)
      
      rerender(<RepositoriesPage />)
      
      expect(screen.getByText("3")).toBeInTheDocument()
    })
  })

  describe("Accessibility", () => {
    it("should have proper heading structure", () => {
      render(<RepositoriesPage />)
      
      expect(screen.getByRole("heading", { level: 1, name: "Repository Management" })).toBeInTheDocument()
    })

    it("should have accessible tab navigation", () => {
      render(<RepositoriesPage />)
      
      expect(screen.getByRole("tablist")).toBeInTheDocument()
      expect(screen.getAllByRole("tab")).toHaveLength(3)
    })

    it("should support keyboard navigation", async () => {
      const user = userEvent.setup()
      render(<RepositoriesPage />)
      
      const addButton = screen.getByText("Add Repository")
      addButton.focus()
      
      await user.keyboard("{Enter}")
      
      expect(screen.getByText("Add New Repository")).toBeInTheDocument()
    })
  })
})
