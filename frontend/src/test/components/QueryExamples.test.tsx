import { describe, it, expect, vi, beforeEach } from "vitest"
import { render, screen, userEvent, waitFor } from "@/test/utils"
import { QueryExamples } from "@/components/examples/QueryExamples"
import type { QueryExample } from "@/components/examples/QueryExamples"

const mockExamples: QueryExample[] = [
  {
    id: "ta-001",
    title: "Authentication Analysis",
    query: "How does the authentication system work?",
    description: "Analyze the authentication flow and security measures",
    agentType: "TECHNICAL_ARCHITECT",
    category: "Security",
    tags: ["auth", "security", "jwt"],
    difficulty: "intermediate",
  },
  {
    id: "tp-001",
    title: "Feature Planning",
    query: "Break down the user notification system implementation",
    description: "Plan a new feature with task breakdown",
    agentType: "TASK_PLANNER",
    category: "Planning",
    tags: ["planning", "tasks", "notifications"],
    difficulty: "beginner",
  },
  {
    id: "rag-001",
    title: "Code Search",
    query: "Find all authentication-related code files",
    description: "Locate specific code files and functions",
    agentType: "RAG_RETRIEVAL",
    category: "Search",
    tags: ["search", "code", "files"],
    difficulty: "beginner",
  },
]

describe("QueryExamples", () => {
  const renderQueryExamples = (props = {}) => {
    return render(
      <QueryExamples
        examples={mockExamples}
        {...props}
      />
    )
  }

  describe("Component Rendering", () => {
    it("renders query examples with all examples", () => {
      renderQueryExamples()
      
      expect(screen.getByText("Query Examples")).toBeInTheDocument()
      expect(screen.getByText("3 examples")).toBeInTheDocument()
      expect(screen.getByText("Authentication Analysis")).toBeInTheDocument()
      expect(screen.getByText("Feature Planning")).toBeInTheDocument()
      expect(screen.getByText("Code Search")).toBeInTheDocument()
    })

    it("shows search input when enabled", () => {
      renderQueryExamples({ showSearch: true })
      
      expect(screen.getByPlaceholderText("Search examples...")).toBeInTheDocument()
    })

    it("hides search input when disabled", () => {
      renderQueryExamples({ showSearch: false })
      
      expect(screen.queryByPlaceholderText("Search examples...")).not.toBeInTheDocument()
    })

    it("shows filter controls when enabled", () => {
      renderQueryExamples({ showFilters: true })
      
      expect(screen.getByText("All Agents")).toBeInTheDocument()
      expect(screen.getByText("All Categories")).toBeInTheDocument()
      expect(screen.getByText("All Levels")).toBeInTheDocument()
    })

    it("hides filter controls when disabled", () => {
      renderQueryExamples({ showFilters: false })
      
      expect(screen.queryByText("All Agents")).not.toBeInTheDocument()
    })
  })

  describe("View Modes", () => {
    it("defaults to grid view", () => {
      renderQueryExamples()
      
      const gridButton = screen.getByLabelText("Grid view")
      expect(gridButton).toHaveAttribute("data-state", "on")
    })

    it("switches to list view when selected", async () => {
      const user = userEvent.setup()
      renderQueryExamples()
      
      const listButton = screen.getByLabelText("List view")
      await user.click(listButton)
      
      expect(listButton).toHaveAttribute("data-state", "on")
    })

    it("uses default view mode from props", () => {
      renderQueryExamples({ defaultViewMode: "list" })
      
      const listButton = screen.getByLabelText("List view")
      expect(listButton).toHaveAttribute("data-state", "on")
    })
  })

  describe("Search Functionality", () => {
    it("filters examples by search query", async () => {
      const user = userEvent.setup()
      renderQueryExamples({ showSearch: true })
      
      const searchInput = screen.getByPlaceholderText("Search examples...")
      await user.type(searchInput, "authentication")
      
      await waitFor(() => {
        expect(screen.getByText("Authentication Analysis")).toBeInTheDocument()
        expect(screen.queryByText("Feature Planning")).not.toBeInTheDocument()
        expect(screen.getByText("1 examples")).toBeInTheDocument()
      })
    })

    it("searches in title, description, and tags", async () => {
      const user = userEvent.setup()
      renderQueryExamples({ showSearch: true })
      
      const searchInput = screen.getByPlaceholderText("Search examples...")
      
      // Search by tag
      await user.clear(searchInput)
      await user.type(searchInput, "jwt")
      
      await waitFor(() => {
        expect(screen.getByText("Authentication Analysis")).toBeInTheDocument()
        expect(screen.queryByText("Feature Planning")).not.toBeInTheDocument()
      })
      
      // Search by description
      await user.clear(searchInput)
      await user.type(searchInput, "task breakdown")
      
      await waitFor(() => {
        expect(screen.getByText("Feature Planning")).toBeInTheDocument()
        expect(screen.queryByText("Authentication Analysis")).not.toBeInTheDocument()
      })
    })

    it("shows no results message when search yields no matches", async () => {
      const user = userEvent.setup()
      renderQueryExamples({ showSearch: true })
      
      const searchInput = screen.getByPlaceholderText("Search examples...")
      await user.type(searchInput, "nonexistent")
      
      await waitFor(() => {
        expect(screen.getByText("No examples found")).toBeInTheDocument()
        expect(screen.getByText("Try adjusting your search or filter criteria")).toBeInTheDocument()
      })
    })
  })

  describe("Filter Functionality", () => {
    it("filters examples by agent type", async () => {
      const user = userEvent.setup()
      renderQueryExamples({ showFilters: true })
      
      const agentSelect = screen.getByDisplayValue("All Agents")
      await user.click(agentSelect)
      
      const techArchitectOption = screen.getByText("Technical Architect")
      await user.click(techArchitectOption)
      
      await waitFor(() => {
        expect(screen.getByText("Authentication Analysis")).toBeInTheDocument()
        expect(screen.queryByText("Feature Planning")).not.toBeInTheDocument()
        expect(screen.getByText("1 examples")).toBeInTheDocument()
      })
    })

    it("filters examples by category", async () => {
      const user = userEvent.setup()
      renderQueryExamples({ showFilters: true })
      
      const categorySelect = screen.getByDisplayValue("All Categories")
      await user.click(categorySelect)
      
      const securityOption = screen.getByText("Security")
      await user.click(securityOption)
      
      await waitFor(() => {
        expect(screen.getByText("Authentication Analysis")).toBeInTheDocument()
        expect(screen.queryByText("Feature Planning")).not.toBeInTheDocument()
      })
    })

    it("filters examples by difficulty", async () => {
      const user = userEvent.setup()
      renderQueryExamples({ showFilters: true })
      
      const difficultySelect = screen.getByDisplayValue("All Levels")
      await user.click(difficultySelect)
      
      const beginnerOption = screen.getByText("Beginner")
      await user.click(beginnerOption)
      
      await waitFor(() => {
        expect(screen.getByText("Feature Planning")).toBeInTheDocument()
        expect(screen.getByText("Code Search")).toBeInTheDocument()
        expect(screen.queryByText("Authentication Analysis")).not.toBeInTheDocument()
        expect(screen.getByText("2 examples")).toBeInTheDocument()
      })
    })

    it("clears all filters when clear button is clicked", async () => {
      const user = userEvent.setup()
      renderQueryExamples({ showFilters: true })
      
      // Apply a filter first
      const agentSelect = screen.getByDisplayValue("All Agents")
      await user.click(agentSelect)
      await user.click(screen.getByText("Technical Architect"))
      
      // Clear filters
      const clearButton = screen.getByText("Clear Filters")
      await user.click(clearButton)
      
      await waitFor(() => {
        expect(screen.getByText("3 examples")).toBeInTheDocument()
        expect(screen.getByDisplayValue("All Agents")).toBeInTheDocument()
      })
    })
  })

  describe("Agent Grouping", () => {
    it("groups examples by agent type when enabled", () => {
      renderQueryExamples({ groupByAgent: true })
      
      expect(screen.getByText("Technical Architect (1)")).toBeInTheDocument()
      expect(screen.getByText("Task Planner (1)")).toBeInTheDocument()
      expect(screen.getByText("RAG Retrieval (1)")).toBeInTheDocument()
    })

    it("shows all examples together when grouping disabled", () => {
      renderQueryExamples({ groupByAgent: false })
      
      // Should not show agent tabs
      expect(screen.queryByText("Technical Architect (1)")).not.toBeInTheDocument()
      
      // Should show all examples in one view
      expect(screen.getByText("Authentication Analysis")).toBeInTheDocument()
      expect(screen.getByText("Feature Planning")).toBeInTheDocument()
      expect(screen.getByText("Code Search")).toBeInTheDocument()
    })

    it("switches between agent tabs", async () => {
      const user = userEvent.setup()
      renderQueryExamples({ groupByAgent: true })
      
      const taskPlannerTab = screen.getByText("Task Planner (1)")
      await user.click(taskPlannerTab)
      
      await waitFor(() => {
        expect(screen.getByText("Feature Planning")).toBeInTheDocument()
        expect(screen.queryByText("Authentication Analysis")).not.toBeInTheDocument()
      })
    })
  })

  describe("Example Interaction", () => {
    it("calls onQueryInsert when Use Query button is clicked", async () => {
      const user = userEvent.setup()
      const onQueryInsert = vi.fn()
      renderQueryExamples({ onQueryInsert })
      
      const useQueryButtons = screen.getAllByText("Use Query")
      await user.click(useQueryButtons[0])
      
      expect(onQueryInsert).toHaveBeenCalledWith("How does the authentication system work?")
    })

    it("calls onExampleSelect when example is clicked", async () => {
      const user = userEvent.setup()
      const onExampleSelect = vi.fn()
      renderQueryExamples({ onExampleSelect })
      
      const exampleCard = screen.getByText("Authentication Analysis").closest("div")
      if (exampleCard) {
        await user.click(exampleCard)
        expect(onExampleSelect).toHaveBeenCalledWith(mockExamples[0])
      }
    })
  })

  describe("Empty State", () => {
    it("shows empty state when no examples provided", () => {
      renderQueryExamples({ examples: [] })
      
      expect(screen.getByText("No examples found")).toBeInTheDocument()
      expect(screen.getByText("Try adjusting your search or filter criteria")).toBeInTheDocument()
    })

    it("shows empty state when all examples filtered out", async () => {
      const user = userEvent.setup()
      renderQueryExamples({ showSearch: true })
      
      const searchInput = screen.getByPlaceholderText("Search examples...")
      await user.type(searchInput, "nonexistent")
      
      await waitFor(() => {
        expect(screen.getByText("No examples found")).toBeInTheDocument()
      })
    })
  })

  describe("Accessibility", () => {
    it("has proper ARIA labels and roles", () => {
      renderQueryExamples()
      
      expect(screen.getByRole("heading", { name: /query examples/i })).toBeInTheDocument()
      
      const gridButton = screen.getByLabelText("Grid view")
      expect(gridButton).toHaveAttribute("role", "button")
      
      const listButton = screen.getByLabelText("List view")
      expect(listButton).toHaveAttribute("role", "button")
    })

    it("supports keyboard navigation", async () => {
      const user = userEvent.setup()
      renderQueryExamples({ showSearch: true, showFilters: true })
      
      // Tab through interactive elements
      await user.tab() // Search input
      expect(screen.getByPlaceholderText("Search examples...")).toHaveFocus()
      
      await user.tab() // Agent filter
      expect(screen.getByDisplayValue("All Agents")).toHaveFocus()
      
      await user.tab() // Category filter
      expect(screen.getByDisplayValue("All Categories")).toHaveFocus()
    })

    it("announces filter changes to screen readers", async () => {
      const user = userEvent.setup()
      renderQueryExamples({ showFilters: true })
      
      const agentSelect = screen.getByDisplayValue("All Agents")
      await user.click(agentSelect)
      await user.click(screen.getByText("Technical Architect"))
      
      await waitFor(() => {
        // The example count should update and be announced
        expect(screen.getByText("1 examples")).toBeInTheDocument()
      })
    })
  })
})
