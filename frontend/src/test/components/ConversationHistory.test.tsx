import { describe, it, expect, vi, beforeEach } from "vitest"
import { render, screen, userEvent } from "@/test/utils"
import { ConversationHistory } from "@/components/conversation/ConversationHistory"
import { SessionProvider } from "@/providers/session-provider"
import type { ConversationMessage } from "@/types/api"

// Mock session with conversation history
const mockMessages: ConversationMessage[] = [
  {
    type: "user",
    content: "How does authentication work?",
    metadata: {
      timestamp: new Date("2024-01-01T10:00:00Z"),
    },
  },
  {
    type: "assistant",
    content: "# Authentication System\n\nThe system uses JWT tokens for authentication...",
    metadata: {
      timestamp: new Date("2024-01-01T10:00:30Z"),
      agent_type: "TECHNICAL_ARCHITECT",
      confidence: 0.95,
      sources: ["src/auth/jwt.py:1-50"],
      processing_time: 2.3,
    },
  },
  {
    type: "user",
    content: "Can you show me the login function?",
    metadata: {
      timestamp: new Date("2024-01-01T10:01:00Z"),
    },
  },
]

const mockSession = {
  session: {
    sessionId: "test-session-123",
    userId: "test-user-456",
    conversationHistory: mockMessages,
    isActive: true,
    createdAt: new Date("2024-01-01T09:00:00Z"),
    lastActivity: new Date("2024-01-01T10:01:00Z"),
    metadata: {},
  },
  isActive: true,
  messageCount: mockMessages.length,
  lastActivity: new Date("2024-01-01T10:01:00Z"),
  createSession: vi.fn(),
  endSession: vi.fn(),
  updateSession: vi.fn(),
  addMessage: vi.fn(),
  updateMessage: vi.fn(),
  removeMessage: vi.fn(),
  clearHistory: vi.fn(),
  setUserId: vi.fn(),
  setMetadata: vi.fn(),
}

// Mock empty session
const mockEmptySession = {
  ...mockSession,
  session: {
    ...mockSession.session,
    conversationHistory: [],
  },
  messageCount: 0,
}

describe("ConversationHistory", () => {
  const renderConversationHistory = (props = {}, session = mockSession) => {
    return render(
      <SessionProvider>
        <ConversationHistory {...props} />
      </SessionProvider>,
      {
        providerProps: {
          sessionProvider: { value: session },
        },
      }
    )
  }

  describe("Component Rendering", () => {
    it("renders conversation history with messages", () => {
      renderConversationHistory()
      
      expect(screen.getByText("Conversation History")).toBeInTheDocument()
      expect(screen.getByText("3 messages in this session")).toBeInTheDocument()
      expect(screen.getByText("How does authentication work?")).toBeInTheDocument()
      expect(screen.getByText(/Authentication System/)).toBeInTheDocument()
    })

    it("shows session information when enabled", () => {
      renderConversationHistory({ showSessionInfo: true })
      
      expect(screen.getByText(/Session: .*123/)).toBeInTheDocument()
    })

    it("hides session information when disabled", () => {
      renderConversationHistory({ showSessionInfo: false })
      
      expect(screen.queryByText(/Session:/)).not.toBeInTheDocument()
    })

    it("shows empty state when no messages", () => {
      renderConversationHistory({}, mockEmptySession)
      
      expect(screen.getByText("No conversation yet")).toBeInTheDocument()
      expect(screen.getByText(/Start by submitting a query/)).toBeInTheDocument()
    })
  })

  describe("Message Display", () => {
    it("displays user messages with correct styling", () => {
      renderConversationHistory()
      
      const userMessage = screen.getByText("How does authentication work?")
      expect(userMessage).toBeInTheDocument()
      
      // Check for user badge
      expect(screen.getByText("You")).toBeInTheDocument()
    })

    it("displays assistant messages with agent type", () => {
      renderConversationHistory()
      
      expect(screen.getByText("Assistant")).toBeInTheDocument()
      expect(screen.getByText("TECHNICAL_ARCHITECT")).toBeInTheDocument()
    })

    it("shows timestamps when enabled", () => {
      renderConversationHistory({ showTimestamps: true })
      
      // Should show relative timestamps
      expect(screen.getByText(/ago/)).toBeInTheDocument()
    })

    it("hides timestamps when disabled", () => {
      renderConversationHistory({ showTimestamps: false })
      
      expect(screen.queryByText(/ago/)).not.toBeInTheDocument()
    })

    it("displays confidence scores for assistant messages", () => {
      renderConversationHistory()
      
      expect(screen.getByText("Confidence: 95%")).toBeInTheDocument()
    })
  })

  describe("Message Grouping", () => {
    it("groups messages by date", () => {
      const messagesFromDifferentDays = [
        {
          type: "user" as const,
          content: "Yesterday's message",
          metadata: {
            timestamp: new Date("2024-01-01T10:00:00Z"),
          },
        },
        {
          type: "user" as const,
          content: "Today's message",
          metadata: {
            timestamp: new Date(),
          },
        },
      ]

      const sessionWithMultipleDays = {
        ...mockSession,
        session: {
          ...mockSession.session,
          conversationHistory: messagesFromDifferentDays,
        },
        messageCount: messagesFromDifferentDays.length,
      }

      renderConversationHistory({}, sessionWithMultipleDays)
      
      expect(screen.getByText("Today")).toBeInTheDocument()
    })
  })

  describe("Scroll Management", () => {
    it("shows scroll buttons when content overflows", () => {
      renderConversationHistory({ maxHeight: 200 })
      
      // Note: This test might need adjustment based on actual content height
      // In a real scenario, we'd need more messages to trigger overflow
    })

    it("handles scroll to bottom action", async () => {
      const user = userEvent.setup()
      renderConversationHistory({ maxHeight: 200 })
      
      // If scroll buttons are visible, test clicking them
      const scrollButton = screen.queryByLabelText("Scroll to bottom")
      if (scrollButton) {
        await user.click(scrollButton)
        // Verify scroll behavior (would need to mock scroll methods)
      }
    })

    it("handles scroll to top action", async () => {
      const user = userEvent.setup()
      renderConversationHistory({ maxHeight: 200 })
      
      const scrollButton = screen.queryByLabelText("Scroll to top")
      if (scrollButton) {
        await user.click(scrollButton)
        // Verify scroll behavior
      }
    })
  })

  describe("Message Actions", () => {
    it("shows message actions when enabled", () => {
      renderConversationHistory({ showActions: true })
      
      // Actions are shown on hover, so we need to simulate hover
      const messageElements = screen.getAllByText(/How does authentication work?|Authentication System/)
      expect(messageElements.length).toBeGreaterThan(0)
    })

    it("hides message actions when disabled", () => {
      renderConversationHistory({ showActions: false })
      
      // Verify no action buttons are visible
      expect(screen.queryByLabelText("Message actions")).not.toBeInTheDocument()
    })

    it("calls onMessageSelect when message is clicked", async () => {
      const user = userEvent.setup()
      const onMessageSelect = vi.fn()
      renderConversationHistory({ onMessageSelect })
      
      const messageElement = screen.getByText("How does authentication work?").closest("div")
      if (messageElement) {
        await user.click(messageElement)
        expect(onMessageSelect).toHaveBeenCalledWith(
          expect.objectContaining({
            content: "How does authentication work?",
          }),
          0
        )
      }
    })
  })

  describe("Custom Message Renderer", () => {
    it("uses custom message renderer when provided", () => {
      const customRenderer = vi.fn((message, index) => (
        <div data-testid={`custom-message-${index}`}>
          Custom: {message.content}
        </div>
      ))
      
      renderConversationHistory({ messageRenderer: customRenderer })
      
      expect(customRenderer).toHaveBeenCalled()
      expect(screen.getByTestId("custom-message-0")).toBeInTheDocument()
      expect(screen.getByText("Custom: How does authentication work?")).toBeInTheDocument()
    })
  })

  describe("Accessibility", () => {
    it("has proper ARIA labels and roles", () => {
      renderConversationHistory()
      
      // Check for proper heading structure
      expect(screen.getByRole("heading", { name: /conversation history/i })).toBeInTheDocument()
      
      // Check for live regions for dynamic content
      const characterCount = screen.queryByText(/messages in this session/)
      if (characterCount) {
        expect(characterCount).toBeInTheDocument()
      }
    })

    it("supports keyboard navigation for scroll buttons", async () => {
      const user = userEvent.setup()
      renderConversationHistory({ maxHeight: 200 })
      
      // Tab to scroll buttons if they exist
      await user.tab()
      
      const scrollButton = screen.queryByLabelText(/scroll to/i)
      if (scrollButton) {
        expect(scrollButton).toHaveFocus()
        
        // Test Enter key activation
        await user.keyboard("{Enter}")
      }
    })

    it("announces new messages to screen readers", () => {
      renderConversationHistory()
      
      // The conversation history should have appropriate live regions
      // for announcing new messages (this would be tested with actual message additions)
    })
  })

  describe("Performance", () => {
    it("handles large number of messages efficiently", () => {
      const manyMessages = Array.from({ length: 100 }, (_, i) => ({
        type: "user" as const,
        content: `Message ${i + 1}`,
        metadata: {
          timestamp: new Date(),
        },
      }))

      const sessionWithManyMessages = {
        ...mockSession,
        session: {
          ...mockSession.session,
          conversationHistory: manyMessages,
        },
        messageCount: manyMessages.length,
      }

      const { container } = renderConversationHistory({}, sessionWithManyMessages)
      
      // Verify component renders without performance issues
      expect(container).toBeInTheDocument()
      expect(screen.getByText("100 messages in this session")).toBeInTheDocument()
    })
  })
})
