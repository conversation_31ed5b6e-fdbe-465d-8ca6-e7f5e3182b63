import { describe, it, expect, vi, beforeEach, afterEach } from "vitest"
import { render, screen, userEvent, waitFor } from "@/test/utils"
import { QueryForm } from "@/components/query/QueryForm"
import { SessionProvider } from "@/providers/session-provider"
import { QueryProvider } from "@/providers/query-provider"
import * as apiHooks from "@/hooks/api"

// Mock the API hooks
vi.mock("@/hooks/api", () => ({
  useQueryMutation: vi.fn(),
}))

// Mock the session provider
const mockSession = {
  session: {
    sessionId: "test-session-123",
    userId: "test-user-456",
    conversationHistory: [],
    isActive: true,
    createdAt: new Date(),
    lastActivity: new Date(),
    metadata: {},
  },
  isActive: true,
  messageCount: 0,
  lastActivity: new Date(),
  createSession: vi.fn(),
  endSession: vi.fn(),
  updateSession: vi.fn(),
  addMessage: vi.fn(),
  updateMessage: vi.fn(),
  removeMessage: vi.fn(),
  clearHistory: vi.fn(),
  setUserId: vi.fn(),
  setMetadata: vi.fn(),
}

// Mock the query mutation
const mockQueryMutation = {
  mutate: vi.fn(),
  isPending: false,
  error: null,
  data: null,
  isSuccess: false,
  isError: false,
}

describe("QueryForm", () => {
  beforeEach(() => {
    vi.mocked(apiHooks.useQueryMutation).mockReturnValue(mockQueryMutation)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  const renderQueryForm = (props = {}) => {
    return render(
      <QueryProvider>
        <SessionProvider>
          <QueryForm {...props} />
        </SessionProvider>
      </QueryProvider>
    )
  }

  describe("Form Rendering", () => {
    it("renders the query form with all required elements", () => {
      renderQueryForm()
      
      expect(screen.getByRole("textbox", { name: /query/i })).toBeInTheDocument()
      expect(screen.getByRole("button", { name: /submit query/i })).toBeInTheDocument()
      expect(screen.getByText("Submit Query")).toBeInTheDocument()
      expect(screen.getByText(/ask questions about your codebase/i)).toBeInTheDocument()
    })

    it("shows character count for query input", () => {
      renderQueryForm()
      
      expect(screen.getByText("0/5000")).toBeInTheDocument()
    })

    it("shows advanced options when enabled", () => {
      renderQueryForm({ showAdvancedOptions: true })
      
      expect(screen.getByText("Advanced Options")).toBeInTheDocument()
      expect(screen.getByLabelText(/repository/i)).toBeInTheDocument()
    })
  })

  describe("Form Validation", () => {
    it("prevents submission with empty query", async () => {
      const user = userEvent.setup()
      renderQueryForm()
      
      const submitButton = screen.getByRole("button", { name: /submit query/i })
      await user.click(submitButton)
      
      expect(mockQueryMutation.mutate).not.toHaveBeenCalled()
    })

    it("shows validation error for empty query", async () => {
      const user = userEvent.setup()
      renderQueryForm()
      
      const queryInput = screen.getByRole("textbox", { name: /query/i })
      const submitButton = screen.getByRole("button", { name: /submit query/i })
      
      await user.click(queryInput)
      await user.tab() // Trigger blur
      await user.click(submitButton)
      
      await waitFor(() => {
        expect(screen.getByText(/query is required/i)).toBeInTheDocument()
      })
    })

    it("validates query length limits", async () => {
      const user = userEvent.setup()
      renderQueryForm()
      
      const queryInput = screen.getByRole("textbox", { name: /query/i })
      const longQuery = "a".repeat(5001)
      
      await user.type(queryInput, longQuery)
      
      await waitFor(() => {
        expect(screen.getByText(/5000\/5000/)).toBeInTheDocument()
      })
      
      const submitButton = screen.getByRole("button", { name: /submit query/i })
      expect(submitButton).toBeDisabled()
    })

    it("validates repository URL format", async () => {
      const user = userEvent.setup()
      renderQueryForm({ showAdvancedOptions: true })
      
      const repoInput = screen.getByLabelText(/repository/i)
      await user.type(repoInput, "invalid-url")
      await user.tab()
      
      await waitFor(() => {
        expect(screen.getByText(/must be a valid url/i)).toBeInTheDocument()
      })
    })

    it("accepts valid repository URL", async () => {
      const user = userEvent.setup()
      renderQueryForm({ showAdvancedOptions: true })
      
      const repoInput = screen.getByLabelText(/repository/i)
      await user.type(repoInput, "https://github.com/owner/repo")
      await user.tab()
      
      await waitFor(() => {
        expect(screen.queryByText(/must be a valid url/i)).not.toBeInTheDocument()
      })
    })
  })

  describe("Form Submission", () => {
    it("submits form with valid data", async () => {
      const user = userEvent.setup()
      const onQuerySuccess = vi.fn()
      renderQueryForm({ onQuerySuccess })
      
      const queryInput = screen.getByRole("textbox", { name: /query/i })
      const submitButton = screen.getByRole("button", { name: /submit query/i })
      
      await user.type(queryInput, "How does authentication work?")
      await user.click(submitButton)
      
      expect(mockQueryMutation.mutate).toHaveBeenCalledWith({
        query: "How does authentication work?",
        session_id: "test-session-123",
        user_id: "test-user-456",
        repository: undefined,
        context_filters: undefined,
      })
    })

    it("includes repository in submission when provided", async () => {
      const user = userEvent.setup()
      renderQueryForm({ showAdvancedOptions: true })
      
      const queryInput = screen.getByRole("textbox", { name: /query/i })
      const repoInput = screen.getByLabelText(/repository/i)
      const submitButton = screen.getByRole("button", { name: /submit query/i })
      
      await user.type(queryInput, "How does authentication work?")
      await user.type(repoInput, "https://github.com/owner/repo")
      await user.click(submitButton)
      
      expect(mockQueryMutation.mutate).toHaveBeenCalledWith(
        expect.objectContaining({
          repository: "https://github.com/owner/repo",
        })
      )
    })

    it("shows loading state during submission", async () => {
      const user = userEvent.setup()
      vi.mocked(apiHooks.useQueryMutation).mockReturnValue({
        ...mockQueryMutation,
        isPending: true,
      })
      
      renderQueryForm()
      
      expect(screen.getByText("Processing...")).toBeInTheDocument()
      expect(screen.getByRole("button", { name: /processing/i })).toBeDisabled()
    })

    it("resets form after successful submission", async () => {
      const user = userEvent.setup()
      const mockSuccessfulMutation = {
        ...mockQueryMutation,
        mutate: vi.fn((request, options) => {
          // Simulate successful submission
          options?.onSuccess?.({
            result_markdown: "Test response",
            structured: {},
            agent_type: "TECHNICAL_ARCHITECT",
            confidence: 0.95,
            sources: [],
            session_id: "test-session-123",
          }, request)
        }),
      }
      
      vi.mocked(apiHooks.useQueryMutation).mockReturnValue(mockSuccessfulMutation)
      
      renderQueryForm()
      
      const queryInput = screen.getByRole("textbox", { name: /query/i })
      await user.type(queryInput, "Test query")
      
      const submitButton = screen.getByRole("button", { name: /submit query/i })
      await user.click(submitButton)
      
      await waitFor(() => {
        expect(queryInput).toHaveValue("")
      })
    })
  })

  describe("Character Count", () => {
    it("updates character count as user types", async () => {
      const user = userEvent.setup()
      renderQueryForm()
      
      const queryInput = screen.getByRole("textbox", { name: /query/i })
      await user.type(queryInput, "Hello")
      
      expect(screen.getByText("5/5000")).toBeInTheDocument()
    })

    it("shows warning color when approaching limit", async () => {
      const user = userEvent.setup()
      renderQueryForm()
      
      const queryInput = screen.getByRole("textbox", { name: /query/i })
      const nearLimitQuery = "a".repeat(4600)
      
      await user.type(queryInput, nearLimitQuery)
      
      const characterCount = screen.getByText("4600/5000")
      expect(characterCount).toHaveClass("text-orange-500")
    })

    it("shows error color when at limit", async () => {
      const user = userEvent.setup()
      renderQueryForm()
      
      const queryInput = screen.getByRole("textbox", { name: /query/i })
      const atLimitQuery = "a".repeat(5000)
      
      await user.type(queryInput, atLimitQuery)
      
      const characterCount = screen.getByText("5000/5000")
      expect(characterCount).toHaveClass("text-destructive")
    })
  })

  describe("Error Handling", () => {
    it("displays error message when submission fails", () => {
      const mockErrorMutation = {
        ...mockQueryMutation,
        error: new Error("Network error"),
        isError: true,
      }
      
      vi.mocked(apiHooks.useQueryMutation).mockReturnValue(mockErrorMutation)
      
      renderQueryForm()
      
      expect(screen.getByText("Network error")).toBeInTheDocument()
    })

    it("calls onQueryError callback when submission fails", async () => {
      const user = userEvent.setup()
      const onQueryError = vi.fn()
      const error = new Error("Test error")
      
      const mockErrorMutation = {
        ...mockQueryMutation,
        mutate: vi.fn((request, options) => {
          options?.onError?.(error, request)
        }),
      }
      
      vi.mocked(apiHooks.useQueryMutation).mockReturnValue(mockErrorMutation)
      
      renderQueryForm({ onQueryError })
      
      const queryInput = screen.getByRole("textbox", { name: /query/i })
      const submitButton = screen.getByRole("button", { name: /submit query/i })
      
      await user.type(queryInput, "Test query")
      await user.click(submitButton)
      
      expect(onQueryError).toHaveBeenCalledWith(error)
    })
  })

  describe("Accessibility", () => {
    it("has proper ARIA labels and descriptions", () => {
      renderQueryForm()
      
      const queryInput = screen.getByRole("textbox", { name: /query/i })
      expect(queryInput).toHaveAttribute("aria-describedby")
      
      const characterCount = screen.getByText("0/5000")
      expect(characterCount).toHaveAttribute("aria-live", "polite")
    })

    it("supports keyboard navigation", async () => {
      const user = userEvent.setup()
      renderQueryForm({ showAdvancedOptions: true })
      
      // Tab through form elements
      await user.tab()
      expect(screen.getByRole("textbox", { name: /query/i })).toHaveFocus()
      
      await user.tab()
      expect(screen.getByLabelText(/repository/i)).toHaveFocus()
      
      await user.tab()
      expect(screen.getByRole("button", { name: /submit query/i })).toHaveFocus()
    })

    it("announces form validation errors to screen readers", async () => {
      const user = userEvent.setup()
      renderQueryForm()
      
      const queryInput = screen.getByRole("textbox", { name: /query/i })
      const submitButton = screen.getByRole("button", { name: /submit query/i })
      
      await user.click(queryInput)
      await user.tab()
      await user.click(submitButton)
      
      await waitFor(() => {
        const errorMessage = screen.getByText(/query is required/i)
        expect(errorMessage).toBeInTheDocument()
        expect(queryInput).toHaveAttribute("aria-invalid", "true")
      })
    })
  })
})
