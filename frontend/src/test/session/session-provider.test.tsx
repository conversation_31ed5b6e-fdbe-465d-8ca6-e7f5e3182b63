/**
 * Session Provider Tests
 * 
 * Comprehensive tests for session management including state persistence,
 * conversation tracking, and session utilities.
 */

import React from "react"
import { describe, it, expect, vi, beforeEach, afterEach } from "vitest"
import { renderHook, act } from "@testing-library/react"

import { SessionProvider, useSession } from "@/providers/session-provider"
import type { ConversationMessage } from "@/lib/api/types"

// Mock useErrorHandler
vi.mock("@/hooks/useErrorHandler", () => ({
  useErrorHandler: () => ({
    handleError: vi.fn(),
  }),
}))

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

// Use vi.stubGlobal for better mocking
vi.stubGlobal('localStorage', localStorageMock)

describe("SessionProvider", () => {
  let wrapper: React.ComponentType<{ children: React.ReactNode }>

  beforeEach(() => {
    wrapper = ({ children }) => (
      <SessionProvider enablePersistence={false}>
        {children}
      </SessionProvider>
    )
    
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
    vi.unstubAllGlobals()
    // Re-stub localStorage for next test
    vi.stubGlobal('localStorage', localStorageMock)
  })

  describe("session creation", () => {
    it("should create a new session", async () => {
      const { result } = renderHook(() => useSession(), { wrapper })

      expect(result.current.session.sessionId).toBeNull()
      expect(result.current.isActive).toBe(false)

      await act(async () => {
        await result.current.createSession("test-user")
      })

      expect(result.current.session.sessionId).toBeTruthy()
      expect(result.current.session.userId).toBe("test-user")
      expect(result.current.isActive).toBe(true)
      expect(result.current.session.createdAt).toBeInstanceOf(Date)
    })

    it("should create session without user ID", async () => {
      const { result } = renderHook(() => useSession(), { wrapper })

      await act(async () => {
        await result.current.createSession()
      })

      expect(result.current.session.sessionId).toBeTruthy()
      expect(result.current.session.userId).toBeNull()
      expect(result.current.isActive).toBe(true)
    })
  })

  describe("session termination", () => {
    it("should end session and clear state", async () => {
      const { result } = renderHook(() => useSession(), { wrapper })

      // Create session first
      await act(async () => {
        await result.current.createSession("test-user")
      })

      expect(result.current.isActive).toBe(true)

      // End session
      act(() => {
        result.current.endSession()
      })

      expect(result.current.session.sessionId).toBeNull()
      expect(result.current.session.userId).toBeNull()
      expect(result.current.isActive).toBe(false)
      expect(result.current.session.conversationHistory).toHaveLength(0)
    })
  })

  describe("conversation management", () => {
    it("should add messages to conversation", async () => {
      const { result } = renderHook(() => useSession(), { wrapper })

      await act(async () => {
        await result.current.createSession()
      })

      const message: Omit<ConversationMessage, "id" | "timestamp"> = {
        type: "user",
        content: "Hello, world!",
        metadata: { test: true },
      }

      act(() => {
        result.current.addMessage(message)
      })

      expect(result.current.session.conversationHistory).toHaveLength(1)
      expect(result.current.session.conversationHistory[0]).toMatchObject({
        type: "user",
        content: "Hello, world!",
        metadata: { test: true },
      })
      expect(result.current.session.conversationHistory[0].id).toBeTruthy()
      expect(result.current.session.conversationHistory[0].timestamp).toBeInstanceOf(Date)
    })

    it("should update existing messages", async () => {
      const { result } = renderHook(() => useSession(), { wrapper })

      await act(async () => {
        await result.current.createSession()
      })

      // Add a message
      act(() => {
        result.current.addMessage({
          type: "user",
          content: "Original content",
        })
      })

      const messageId = result.current.session.conversationHistory[0].id

      // Update the message
      act(() => {
        result.current.updateMessage(messageId, {
          content: "Updated content",
          metadata: { updated: true },
        })
      })

      expect(result.current.session.conversationHistory[0].content).toBe("Updated content")
      expect(result.current.session.conversationHistory[0].metadata).toEqual({ updated: true })
    })

    it("should remove messages", async () => {
      const { result } = renderHook(() => useSession(), { wrapper })

      await act(async () => {
        await result.current.createSession()
      })

      // Add two messages
      act(() => {
        result.current.addMessage({ type: "user", content: "Message 1" })
        result.current.addMessage({ type: "user", content: "Message 2" })
      })

      expect(result.current.session.conversationHistory).toHaveLength(2)

      const messageId = result.current.session.conversationHistory[0].id

      // Remove first message
      act(() => {
        result.current.removeMessage(messageId)
      })

      expect(result.current.session.conversationHistory).toHaveLength(1)
      expect(result.current.session.conversationHistory[0].content).toBe("Message 2")
    })

    it("should clear conversation history", async () => {
      const { result } = renderHook(() => useSession(), { wrapper })

      await act(async () => {
        await result.current.createSession()
      })

      // Add messages
      act(() => {
        result.current.addMessage({ type: "user", content: "Message 1" })
        result.current.addMessage({ type: "user", content: "Message 2" })
      })

      expect(result.current.session.conversationHistory).toHaveLength(2)

      // Clear history
      act(() => {
        result.current.clearHistory()
      })

      expect(result.current.session.conversationHistory).toHaveLength(0)
    })

    it("should respect max history size", async () => {
      const limitedWrapper = ({ children }: { children: React.ReactNode }) => (
        <SessionProvider enablePersistence={false} maxHistorySize={2}>
          {children}
        </SessionProvider>
      )

      const { result } = renderHook(() => useSession(), { wrapper: limitedWrapper })

      await act(async () => {
        await result.current.createSession()
      })

      // Add 3 messages (exceeds limit of 2)
      act(() => {
        result.current.addMessage({ type: "user", content: "Message 1" })
        result.current.addMessage({ type: "user", content: "Message 2" })
        result.current.addMessage({ type: "user", content: "Message 3" })
      })

      // Should only keep the last 2 messages
      expect(result.current.session.conversationHistory).toHaveLength(2)
      expect(result.current.session.conversationHistory[0].content).toBe("Message 2")
      expect(result.current.session.conversationHistory[1].content).toBe("Message 3")
    })
  })

  describe("session updates", () => {
    it("should update session properties", async () => {
      const { result } = renderHook(() => useSession(), { wrapper })

      await act(async () => {
        await result.current.createSession()
      })

      act(() => {
        result.current.updateSession({
          metadata: { theme: "dark", language: "en" },
        })
      })

      expect(result.current.session.metadata).toEqual({
        theme: "dark",
        language: "en",
      })
    })

    it("should set user ID", async () => {
      const { result } = renderHook(() => useSession(), { wrapper })

      await act(async () => {
        await result.current.createSession()
      })

      act(() => {
        result.current.setUserId("new-user-id")
      })

      expect(result.current.session.userId).toBe("new-user-id")
    })

    it("should set metadata", async () => {
      const { result } = renderHook(() => useSession(), { wrapper })

      await act(async () => {
        await result.current.createSession()
      })

      act(() => {
        result.current.setMetadata({ key1: "value1" })
        result.current.setMetadata({ key2: "value2" })
      })

      expect(result.current.session.metadata).toEqual({
        key1: "value1",
        key2: "value2",
      })
    })
  })

  describe("computed properties", () => {
    it("should calculate message count correctly", async () => {
      const { result } = renderHook(() => useSession(), { wrapper })

      await act(async () => {
        await result.current.createSession()
      })

      expect(result.current.messageCount).toBe(0)

      act(() => {
        result.current.addMessage({ type: "user", content: "Message 1" })
        result.current.addMessage({ type: "assistant", content: "Response 1" })
      })

      expect(result.current.messageCount).toBe(2)
    })

    it("should track last activity", async () => {
      const { result } = renderHook(() => useSession(), { wrapper })

      await act(async () => {
        await result.current.createSession()
      })

      const initialActivity = result.current.lastActivity

      // Wait a bit and add a message
      await new Promise(resolve => setTimeout(resolve, 10))

      act(() => {
        result.current.addMessage({ type: "user", content: "Message" })
      })

      expect(result.current.lastActivity).not.toEqual(initialActivity)
      expect(result.current.lastActivity).toBeInstanceOf(Date)
    })
  })

  describe("persistence", () => {
    it("should save to localStorage when persistence is enabled", async () => {
      const persistentWrapper = ({ children }: { children: React.ReactNode }) => (
        <SessionProvider enablePersistence={true}>
          {children}
        </SessionProvider>
      )

      const { result } = renderHook(() => useSession(), { wrapper: persistentWrapper })

      await act(async () => {
        await result.current.createSession("test-user")
      })

      // Wait for effects to run
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 0))
      })

      // Should have called setItem to save session
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        "session_state",
        expect.any(String)
      )
    })

    it("should load from localStorage on mount", () => {
      const mockSessionData = {
        sessionId: "test-session",
        userId: "test-user",
        conversationHistory: [],
        isActive: true,
        createdAt: new Date().toISOString(),
        lastActivity: new Date().toISOString(),
        metadata: {},
      }

      localStorageMock.getItem.mockReturnValueOnce(JSON.stringify(mockSessionData))

      const persistentWrapper = ({ children }: { children: React.ReactNode }) => (
        <SessionProvider enablePersistence={true}>
          {children}
        </SessionProvider>
      )

      const { result } = renderHook(() => useSession(), { wrapper: persistentWrapper })

      expect(result.current.session.sessionId).toBe("test-session")
      expect(result.current.session.userId).toBe("test-user")
    })

    it("should handle corrupted localStorage data", () => {
      localStorageMock.getItem.mockReturnValueOnce("invalid-json")

      const persistentWrapper = ({ children }: { children: React.ReactNode }) => (
        <SessionProvider enablePersistence={true}>
          {children}
        </SessionProvider>
      )

      // Should not throw and should clear corrupted data
      const { result } = renderHook(() => useSession(), { wrapper: persistentWrapper })

      expect(result.current.session.sessionId).toBeNull()
      expect(localStorageMock.removeItem).toHaveBeenCalledWith("session_state")
    })
  })

  describe("error handling", () => {
    it("should handle errors gracefully", async () => {
      const { result } = renderHook(() => useSession(), { wrapper })

      // Should not throw when creating session
      await act(async () => {
        await result.current.createSession()
      })

      expect(result.current.session.sessionId).toBeTruthy()
    })
  })
})
