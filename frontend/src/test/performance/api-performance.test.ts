/**
 * API Performance Tests
 * 
 * Tests to verify that API integration meets performance requirements
 * including the <3s response time requirement and proper caching behavior.
 */

import { describe, it, expect, vi, beforeEach, afterEach } from "vitest"

import { ApiClient, createApiClient } from "@/lib/api/client"
import type { QueryRequest, IngestionRequest } from "@/types/api"
import { createMockApiResponse } from "@/test/utils/api-mocks"

// Mock fetch with timing control
const mockFetch = vi.fn()

describe("API Performance Tests", () => {
  let client: ApiClient

  beforeEach(() => {
    // Properly stub global fetch for each test
    vi.stubGlobal('fetch', mockFetch)

    client = createApiClient({
      baseUrl: "http://localhost:8000",
      timeout: 5000,
      retries: 1,
      enableLogging: false,
    })

    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
    vi.unstubAllGlobals()
  })

  describe("response time requirements", () => {
    it("should complete query requests within 3 seconds", async () => {
      const mockResponse = {
        result_markdown: "# Test Response",
        structured: { test: true },
        agent_type: "TECHNICAL_ARCHITECT",
        confidence: 0.95,
        sources: ["test.py:1-10"],
        session_id: "test-session",
      }

      // Mock a response that takes 2 seconds
      mockFetch.mockImplementationOnce(() => 
        new Promise(resolve => 
          setTimeout(() => resolve(createMockApiResponse(mockResponse)), 2000)
        )
      )

      const startTime = performance.now()
      
      const request: QueryRequest = {
        query: "Test query for performance",
        session_id: "test-session",
      }

      const result = await client.query(request)
      
      const endTime = performance.now()
      const duration = endTime - startTime

      expect(duration).toBeLessThan(3000) // Less than 3 seconds
      expect(result).toEqual(mockResponse)
    })

    it("should complete status requests within 1 second", async () => {
      const mockResponse = {
        api: "operational",
        agents: {
          ORCHESTRATOR: "operational",
          TECHNICAL_ARCHITECT: "operational",
        },
        vector_store: "operational",
        ingestion_pipeline: "operational",
        active_sessions: 5,
      }

      // Mock a fast response (500ms)
      mockFetch.mockImplementationOnce(() => 
        new Promise(resolve => 
          setTimeout(() => resolve(createMockApiResponse(mockResponse)), 500)
        )
      )

      const startTime = performance.now()
      
      const result = await client.getStatus()
      
      const endTime = performance.now()
      const duration = endTime - startTime

      expect(duration).toBeLessThan(1000) // Less than 1 second
      expect(result).toEqual(mockResponse)
    })

    it("should complete health checks within 500ms", async () => {
      const mockResponse = {
        status: "healthy",
        service: "llm-rag-backend",
        version: "0.1.0",
        environment: "test",
        timestamp: "2024-01-01T00:00:00Z",
      }

      // Mock a very fast response (200ms)
      mockFetch.mockImplementationOnce(() => 
        new Promise(resolve => 
          setTimeout(() => resolve(createMockApiResponse(mockResponse)), 200)
        )
      )

      const startTime = performance.now()
      
      const result = await client.getHealth()
      
      const endTime = performance.now()
      const duration = endTime - startTime

      expect(duration).toBeLessThan(500) // Less than 500ms
      expect(result).toEqual(mockResponse)
    })

    it("should timeout requests that exceed configured timeout", async () => {
      // Create client with short timeout
      const fastClient = createApiClient({ timeout: 100, enableRetry: false })

      // Mock a slow response that respects abort signal
      mockFetch.mockImplementationOnce((url, options) =>
        new Promise((resolve, reject) => {
          const timeoutId = setTimeout(() => resolve(createMockApiResponse({})), 2000)

          // Listen for abort signal
          if (options?.signal) {
            options.signal.addEventListener('abort', () => {
              clearTimeout(timeoutId)
              reject(new Error('Request aborted'))
            })
          }
        })
      )

      const startTime = performance.now()

      await expect(fastClient.getHealth()).rejects.toThrow()

      const endTime = performance.now()
      const duration = endTime - startTime

      // Should timeout around 100ms, not wait for the full 2 seconds
      expect(duration).toBeLessThan(500)
    })
  })

  describe("concurrent request handling", () => {
    it("should handle multiple concurrent requests efficiently", async () => {
      const mockResponse = {
        status: "healthy",
        service: "llm-rag-backend",
        version: "0.1.0",
        environment: "test",
        timestamp: "2024-01-01T00:00:00Z",
      }

      // Mock responses with 300ms delay each
      mockFetch.mockImplementation(() => 
        new Promise(resolve => 
          setTimeout(() => resolve(createMockApiResponse(mockResponse)), 300)
        )
      )

      const startTime = performance.now()
      
      // Make 5 concurrent requests
      const promises = Array.from({ length: 5 }, () => client.getHealth())
      const results = await Promise.all(promises)
      
      const endTime = performance.now()
      const duration = endTime - startTime

      // Should complete in roughly 300ms (concurrent), not 1500ms (sequential)
      expect(duration).toBeLessThan(800) // Allow some overhead
      expect(results).toHaveLength(5)
      expect(mockFetch).toHaveBeenCalledTimes(5)
    })

    it("should handle mixed request types concurrently", async () => {
      const healthResponse = {
        status: "healthy",
        service: "llm-rag-backend",
        version: "0.1.0",
        environment: "test",
        timestamp: "2024-01-01T00:00:00Z",
      }

      const statusResponse = {
        api: "operational",
        agents: {},
        vector_store: "operational",
        ingestion_pipeline: "operational",
        active_sessions: 0,
      }

      // Mock different response times for different endpoints
      mockFetch.mockImplementation((url: string) => {
        const delay = url.includes("health") ? 200 : 400
        const response = url.includes("health") ? healthResponse : statusResponse
        
        return new Promise(resolve => 
          setTimeout(() => resolve(createMockApiResponse(response)), delay)
        )
      })

      const startTime = performance.now()
      
      // Make concurrent requests to different endpoints
      const [health, status] = await Promise.all([
        client.getHealth(),
        client.getStatus(),
      ])
      
      const endTime = performance.now()
      const duration = endTime - startTime

      // Should complete in roughly 400ms (slowest request), not 600ms (sequential)
      expect(duration).toBeLessThan(600)
      expect(health).toEqual(healthResponse)
      expect(status).toEqual(statusResponse)
    })
  })

  describe("retry performance", () => {
    it("should retry efficiently without excessive delays", async () => {
      const successResponse = {
        status: "healthy",
        service: "llm-rag-backend",
        version: "0.1.0",
        environment: "test",
        timestamp: "2024-01-01T00:00:00Z",
      }

      // First call fails, second succeeds
      mockFetch
        .mockResolvedValueOnce(createMockApiResponse(
          { detail: "Server error" },
          { status: 500 }
        ))
        .mockResolvedValueOnce(createMockApiResponse(successResponse))

      const startTime = performance.now()
      
      const result = await client.getHealth()
      
      const endTime = performance.now()
      const duration = endTime - startTime

      // Should complete quickly with minimal retry delay
      expect(duration).toBeLessThan(2000) // Allow for retry delay
      expect(result).toEqual(successResponse)
      expect(mockFetch).toHaveBeenCalledTimes(2)
    })

    it("should fail fast on non-retryable errors", async () => {
      // Mock a 400 error (client error, should not retry)
      mockFetch.mockResolvedValueOnce(createMockApiResponse(
        { detail: "Bad request" },
        { status: 400 }
      ))

      const startTime = performance.now()
      
      await expect(client.getHealth()).rejects.toThrow()
      
      const endTime = performance.now()
      const duration = endTime - startTime

      // Should fail quickly without retries
      expect(duration).toBeLessThan(500)
      expect(mockFetch).toHaveBeenCalledTimes(1)
    })
  })

  describe("memory and resource usage", () => {
    it("should not leak memory with many requests", async () => {
      const mockResponse = { status: "healthy", service: "test", version: "1.0.0", environment: "test", timestamp: "2024-01-01T00:00:00Z" }

      // Create separate mock responses for each request to avoid body reuse
      mockFetch.mockImplementation(() => createMockApiResponse(mockResponse))

      // Measure initial memory (simplified)
      const initialMemory = performance.memory?.usedJSHeapSize || 0

      // Make many requests
      const promises = Array.from({ length: 100 }, () => client.getHealth())
      await Promise.all(promises)

      // Force garbage collection if available
      if (global.gc) {
        global.gc()
      }

      // Measure final memory
      const finalMemory = performance.memory?.usedJSHeapSize || 0
      const memoryIncrease = finalMemory - initialMemory

      // Memory increase should be reasonable (less than 10MB)
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024)
    })

    it("should handle request cancellation properly", async () => {
      // Create client with very short timeout to simulate cancellation
      const fastClient = createApiClient({ timeout: 50, enableRetry: false })

      // Mock a slow response that respects abort signal
      mockFetch.mockImplementationOnce((url, options) =>
        new Promise((resolve, reject) => {
          const timeoutId = setTimeout(() => resolve(createMockApiResponse({})), 5000)

          // Listen for abort signal
          if (options?.signal) {
            options.signal.addEventListener('abort', () => {
              clearTimeout(timeoutId)
              reject(new Error('Request aborted'))
            })
          }
        })
      )

      const startTime = performance.now()

      await expect(fastClient.getHealth()).rejects.toThrow()

      const endTime = performance.now()
      const duration = endTime - startTime

      // Should cancel quickly (around 50ms timeout)
      expect(duration).toBeLessThan(200)
    })
  })

  describe("caching performance", () => {
    it("should benefit from response caching", async () => {
      // This test would be more meaningful with actual React Query caching
      // For now, we test that the client itself doesn't add unnecessary overhead

      const mockResponse = { status: "healthy", service: "test", version: "1.0.0", environment: "test", timestamp: "2024-01-01T00:00:00Z" }

      // Create separate mock responses for each request to avoid body reuse
      mockFetch
        .mockResolvedValueOnce(createMockApiResponse(mockResponse))
        .mockResolvedValueOnce(createMockApiResponse(mockResponse))

      // First request
      const startTime1 = performance.now()
      await client.getHealth()
      const endTime1 = performance.now()
      const duration1 = endTime1 - startTime1

      // Second request (would be cached in real scenario)
      const startTime2 = performance.now()
      await client.getHealth()
      const endTime2 = performance.now()
      const duration2 = endTime2 - startTime2

      // Both requests should be reasonably fast
      expect(duration1).toBeLessThan(1000)
      expect(duration2).toBeLessThan(1000)
      expect(mockFetch).toHaveBeenCalledTimes(2)
    })
  })

  describe("large payload handling", () => {
    it("should handle large query responses efficiently", async () => {
      // Create a large mock response
      const largeContent = "# Large Response\n" + "This is a large response. ".repeat(1000)
      const mockResponse = {
        result_markdown: largeContent,
        structured: { large: true, size: largeContent.length },
        agent_type: "TECHNICAL_ARCHITECT",
        confidence: 0.95,
        sources: Array.from({ length: 50 }, (_, i) => `file${i}.py:1-100`),
        session_id: "test-session",
      }

      mockFetch.mockResolvedValueOnce(createMockApiResponse(mockResponse))

      const startTime = performance.now()
      
      const request: QueryRequest = {
        query: "Large query test",
        session_id: "test-session",
      }

      const result = await client.query(request)
      
      const endTime = performance.now()
      const duration = endTime - startTime

      // Should handle large responses within reasonable time
      expect(duration).toBeLessThan(1000)
      expect(result.result_markdown).toBe(largeContent)
      expect(result.sources).toHaveLength(50)
    })
  })
})
