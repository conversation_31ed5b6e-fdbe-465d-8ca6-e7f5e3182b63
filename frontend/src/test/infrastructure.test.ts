/**
 * Infrastructure Test Suite
 * 
 * Tests to validate that our core infrastructure components are working correctly.
 * This includes API types, error handling, and testing utilities.
 */

import { describe, it, expect, vi } from "vitest"
import { z } from "zod"

// Test API Types
import {
  AgentType,
  ApiStatus,
  IngestionStatus,
  type QueryRequest,
  type QueryResponse,
  type IngestionRequest,
  type IngestionResponse,
  type StatusResponse,
} from "@/types/api"

// Test Error Types
import {
  ErrorCategory,
  ErrorSeverity,
  HttpStatusCode,
  type ApiError,
  type BaseError,
} from "@/types/errors"

// Test Schemas
import {
  QueryRequestSchema,
  QueryResponseSchema,
  IngestionRequestSchema,
  IngestionResponseSchema,
  StatusResponseSchema,
} from "@/types/api/schemas"

// Test Error Utils
import {
  createBaseError,
  createApiError,
  formatErrorForUser,
  isRetryableError,
} from "@/utils/errorUtils"

// Test API Mocks
import {
  mockApiData,
  createMockApiClient,
  mockSuccessfulApiResponses,
} from "@/test/mocks/api"

describe("Core Infrastructure", () => {
  describe("API Types", () => {
    it("should export all required enums", () => {
      expect(AgentType.ORCHESTRATOR).toBe("ORCHESTRATOR")
      expect(AgentType.TECHNICAL_ARCHITECT).toBe("TECHNICAL_ARCHITECT")
      expect(AgentType.TASK_PLANNER).toBe("TASK_PLANNER")
      expect(AgentType.RAG_RETRIEVAL).toBe("RAG_RETRIEVAL")

      expect(ApiStatus.OPERATIONAL).toBe("operational")
      expect(ApiStatus.DEGRADED).toBe("degraded")
      expect(ApiStatus.DOWN).toBe("down")

      expect(IngestionStatus.COMPLETED).toBe("completed")
      expect(IngestionStatus.FAILED).toBe("failed")
    })

    it("should validate QueryRequest with schema", () => {
      const validRequest: QueryRequest = {
        query: "Test query",
        session_id: "test-session",
        user_id: "test-user",
      }

      const result = QueryRequestSchema.safeParse(validRequest)
      expect(result.success).toBe(true)
    })

    it("should validate QueryResponse with schema", () => {
      const validResponse: QueryResponse = {
        result_markdown: "# Test Response",
        structured: { test: true },
        agent_type: "ORCHESTRATOR",
        confidence: 0.85,
        sources: ["test.py:1-10"],
        session_id: "test-session",
      }

      const result = QueryResponseSchema.safeParse(validResponse)
      expect(result.success).toBe(true)
    })

    it("should validate IngestionRequest with schema", () => {
      const validRequest: IngestionRequest = {
        repository_url: "https://github.com/test/repo",
        branch: "main",
        force_refresh: false,
      }

      const result = IngestionRequestSchema.safeParse(validRequest)
      expect(result.success).toBe(true)
    })

    it("should validate StatusResponse with schema", () => {
      const validResponse: StatusResponse = {
        api: "operational",
        agents: { ORCHESTRATOR: "operational" },
        vector_store: "operational",
        ingestion_pipeline: "operational",
        active_sessions: 5,
      }

      const result = StatusResponseSchema.safeParse(validResponse)
      expect(result.success).toBe(true)
    })
  })

  describe("Error Types", () => {
    it("should export all required error enums", () => {
      expect(ErrorCategory.NETWORK).toBe("network")
      expect(ErrorCategory.VALIDATION).toBe("validation")
      expect(ErrorCategory.SERVER).toBe("server")

      expect(ErrorSeverity.LOW).toBe("low")
      expect(ErrorSeverity.MEDIUM).toBe("medium")
      expect(ErrorSeverity.HIGH).toBe("high")
      expect(ErrorSeverity.CRITICAL).toBe("critical")

      expect(HttpStatusCode.BAD_REQUEST).toBe(400)
      expect(HttpStatusCode.INTERNAL_SERVER_ERROR).toBe(500)
    })

    it("should create base errors correctly", () => {
      const error = createBaseError(
        "Test error",
        ErrorCategory.VALIDATION,
        ErrorSeverity.MEDIUM
      )

      expect(error.message).toBe("Test error")
      expect(error.category).toBe(ErrorCategory.VALIDATION)
      expect(error.severity).toBe(ErrorSeverity.MEDIUM)
      expect(error.timestamp).toBeDefined()
    })

    it("should format errors for users", () => {
      const networkError: BaseError = {
        message: "Connection failed",
        category: ErrorCategory.NETWORK,
        severity: ErrorSeverity.HIGH,
        timestamp: new Date().toISOString(),
      }

      const userMessage = formatErrorForUser(networkError)
      expect(userMessage).toContain("Connection error")
    })

    it("should identify retryable errors", () => {
      const retryableError: ApiError = {
        message: "Server error",
        category: ErrorCategory.SERVER,
        severity: ErrorSeverity.HIGH,
        timestamp: new Date().toISOString(),
        status: HttpStatusCode.INTERNAL_SERVER_ERROR,
        detail: "Internal server error",
      }

      expect(isRetryableError(retryableError)).toBe(true)
    })
  })

  describe("API Mocking", () => {
    it("should provide mock API data", () => {
      expect(mockApiData.queryResponse).toBeDefined()
      expect(mockApiData.queryResponse.result_markdown).toContain("Test Response")
      expect(mockApiData.queryResponse.agent_type).toBe("TECHNICAL_ARCHITECT")

      expect(mockApiData.ingestionResponse).toBeDefined()
      expect(mockApiData.ingestionResponse.status).toBe("completed")

      expect(mockApiData.statusResponse).toBeDefined()
      expect(mockApiData.statusResponse.api).toBe("operational")
    })

    it("should create mock API client", () => {
      const mockClient = createMockApiClient()
      expect(mockClient).toBeDefined()
      expect(vi.isMockFunction(mockClient)).toBe(true)
    })

    it("should setup successful API responses", () => {
      const mockFetch = mockSuccessfulApiResponses()
      expect(mockFetch).toBeDefined()
      expect(vi.isMockFunction(mockFetch)).toBe(true)
      expect(global.fetch).toBe(mockFetch)
    })
  })

  describe("Testing Framework", () => {
    it("should have vitest globals available", () => {
      expect(describe).toBeDefined()
      expect(it).toBeDefined()
      expect(expect).toBeDefined()
      expect(vi).toBeDefined()
    })

    it("should support async testing", async () => {
      const asyncFunction = async () => {
        await new Promise((resolve) => setTimeout(resolve, 10))
        return "async result"
      }

      const result = await asyncFunction()
      expect(result).toBe("async result")
    })

    it("should support mocking", () => {
      const mockFn = vi.fn()
      mockFn("test")
      
      expect(mockFn).toHaveBeenCalledWith("test")
      expect(mockFn).toHaveBeenCalledTimes(1)
    })
  })
})
