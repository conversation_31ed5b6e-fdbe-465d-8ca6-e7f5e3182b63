/**
 * Repository Test Configuration
 * 
 * Configuration and utilities specifically for repository management tests.
 * Provides common test data, mocks, and helper functions.
 */

import type { 
  RepositoryMetadata, 
  RepositoryListResponse, 
  RepositoryStatsResponse,
  IngestionProgress,
  BatchOperationResponse 
} from "@/types/api"

/**
 * Mock repository data for testing
 */
export const mockRepositoryData: RepositoryMetadata = {
  id: "test-repo-123",
  url: new URL("https://github.com/test/repository"),
  name: "repository",
  owner: "test",
  branch: "main",
  commit_sha: "abc123def456",
  is_private: false,
  description: "A test repository for unit testing",
  language: "TypeScript",
  size: 2048,
  created_at: "2024-01-01T00:00:00Z",
  updated_at: "2024-01-02T12:00:00Z",
  ingested_at: "2024-01-02T12:30:00Z",
  status: "completed",
  processed_files: 100,
  chunks_created: 500,
  embeddings_generated: 500,
  processing_time: 300,
}

/**
 * Mock repository list response
 */
export const mockRepositoryListResponse: RepositoryListResponse = {
  repositories: [
    mockRepositoryData,
    {
      ...mockRepositoryData,
      id: "test-repo-456",
      name: "another-repo",
      status: "processing",
      processed_files: 50,
      chunks_created: 200,
      embeddings_generated: 150,
    },
  ],
  total_count: 2,
  page: 1,
  page_size: 20,
  total_pages: 1,
}

/**
 * Mock repository stats response
 */
export const mockRepositoryStatsResponse: RepositoryStatsResponse = {
  total_repositories: 10,
  total_files_processed: 1500,
  total_embeddings: 7500,
  storage_usage_mb: 512.75,
  repositories_by_status: {
    completed: 7,
    processing: 2,
    failed: 1,
    pending: 0,
    cancelled: 0,
  },
}

/**
 * Mock ingestion progress data
 */
export const mockIngestionProgress: IngestionProgress = {
  repository_id: "test-repo-123",
  status: "processing",
  stage: "generating_embeddings",
  current_step: "Processing TypeScript files",
  progress_percentage: 75.5,
  elapsed_time: 180,
  estimated_completion: new Date(Date.now() + 60000).toISOString(),
  files_discovered: 100,
  files_processed: 75,
  files_filtered: 5,
  chunks_created: 375,
  embeddings_generated: 300,
  errors: ["Failed to process large-file.json"],
  warnings: ["Binary file detected: image.png", "Large file: data.csv"],
}

/**
 * Mock batch operation response
 */
export const mockBatchOperationResponse: BatchOperationResponse = {
  operation: "delete",
  total_requested: 3,
  successful: 2,
  failed: 1,
  results: [
    {
      repository_id: "repo-1",
      success: true,
      message: "Repository deleted successfully",
    },
    {
      repository_id: "repo-2",
      success: true,
      message: "Repository deleted successfully",
    },
    {
      repository_id: "repo-3",
      success: false,
      error: "Repository is currently being processed",
    },
  ],
}

/**
 * Test utilities for repository components
 */
export const repositoryTestUtils = {
  /**
   * Create a mock repository with custom properties
   */
  createMockRepository(overrides: Partial<RepositoryMetadata> = {}): RepositoryMetadata {
    return {
      ...mockRepositoryData,
      ...overrides,
      id: overrides.id || `test-repo-${Date.now()}`,
    }
  },

  /**
   * Create a mock repository list response
   */
  createMockRepositoryList(
    repositories: RepositoryMetadata[] = [mockRepositoryData],
    options: {
      page?: number
      pageSize?: number
      totalCount?: number
    } = {}
  ): RepositoryListResponse {
    const { page = 1, pageSize = 20, totalCount = repositories.length } = options
    
    return {
      repositories,
      total_count: totalCount,
      page,
      page_size: pageSize,
      total_pages: Math.ceil(totalCount / pageSize),
    }
  },

  /**
   * Create a mock ingestion progress with custom properties
   */
  createMockIngestionProgress(overrides: Partial<IngestionProgress> = {}): IngestionProgress {
    return {
      ...mockIngestionProgress,
      ...overrides,
    }
  },

  /**
   * Create a mock batch operation response
   */
  createMockBatchResponse(
    operation: "delete" | "re_ingest",
    results: Array<{ repository_id: string; success: boolean; message?: string; error?: string }>
  ): BatchOperationResponse {
    const successful = results.filter(r => r.success).length
    const failed = results.filter(r => !r.success).length
    
    return {
      operation,
      total_requested: results.length,
      successful,
      failed,
      results,
    }
  },

  /**
   * Mock API hook return values
   */
  mockApiHooks: {
    repositoryList: (data = mockRepositoryListResponse, loading = false, error: Error | null = null) => ({
      data: loading ? undefined : data,
      isLoading: loading,
      error,
      refetch: vi.fn(),
    }),

    repositoryDetails: (data = mockRepositoryData, loading = false, error: Error | null = null) => ({
      data: loading ? undefined : data,
      isLoading: loading,
      error,
      refetch: vi.fn(),
    }),

    repositoryStats: (data = mockRepositoryStatsResponse, loading = false, error: Error | null = null) => ({
      data: loading ? undefined : data,
      isLoading: loading,
      error,
      refetch: vi.fn(),
    }),

    ingestionProgress: (data = mockIngestionProgress, loading = false, error: Error | null = null) => ({
      data: loading ? undefined : data,
      isLoading: loading,
      error,
      refetch: vi.fn(),
    }),

    mutation: (pending = false, error: Error | null = null) => ({
      mutate: vi.fn(),
      isPending: pending,
      isError: !!error,
      error,
      isSuccess: !pending && !error,
    }),
  },
}

/**
 * Common test scenarios for repository components
 */
export const repositoryTestScenarios = {
  /**
   * Loading state scenario
   */
  loading: {
    repositoryList: repositoryTestUtils.mockApiHooks.repositoryList(undefined, true),
    repositoryStats: repositoryTestUtils.mockApiHooks.repositoryStats(undefined, true),
    ingestionProgress: repositoryTestUtils.mockApiHooks.ingestionProgress(undefined, true),
  },

  /**
   * Error state scenario
   */
  error: {
    repositoryList: repositoryTestUtils.mockApiHooks.repositoryList(undefined, false, new Error("Failed to load")),
    repositoryStats: repositoryTestUtils.mockApiHooks.repositoryStats(undefined, false, new Error("Failed to load")),
    ingestionProgress: repositoryTestUtils.mockApiHooks.ingestionProgress(undefined, false, new Error("Failed to load")),
  },

  /**
   * Empty state scenario
   */
  empty: {
    repositoryList: repositoryTestUtils.mockApiHooks.repositoryList(
      repositoryTestUtils.createMockRepositoryList([])
    ),
    repositoryStats: repositoryTestUtils.mockApiHooks.repositoryStats({
      ...mockRepositoryStatsResponse,
      total_repositories: 0,
      total_files_processed: 0,
      total_embeddings: 0,
      storage_usage_mb: 0,
    }),
  },

  /**
   * Success state scenario
   */
  success: {
    repositoryList: repositoryTestUtils.mockApiHooks.repositoryList(),
    repositoryStats: repositoryTestUtils.mockApiHooks.repositoryStats(),
    ingestionProgress: repositoryTestUtils.mockApiHooks.ingestionProgress(),
  },
}

/**
 * Test assertions for repository components
 */
export const repositoryTestAssertions = {
  /**
   * Assert repository list is displayed correctly
   */
  expectRepositoryListDisplayed: (repositories: RepositoryMetadata[]) => {
    repositories.forEach(repo => {
      expect(screen.getByText(`${repo.owner}/${repo.name}`)).toBeInTheDocument()
      if (repo.description) {
        expect(screen.getByText(repo.description)).toBeInTheDocument()
      }
    })
  },

  /**
   * Assert loading state is displayed
   */
  expectLoadingState: (component: string) => {
    expect(screen.getByText(`Loading ${component}...`)).toBeInTheDocument()
  },

  /**
   * Assert error state is displayed
   */
  expectErrorState: (component: string) => {
    expect(screen.getByText(`Failed to load ${component}`)).toBeInTheDocument()
    expect(screen.getByText("Try Again")).toBeInTheDocument()
  },

  /**
   * Assert empty state is displayed
   */
  expectEmptyState: (message: string) => {
    expect(screen.getByText(message)).toBeInTheDocument()
  },

  /**
   * Assert progress information is displayed
   */
  expectProgressDisplayed: (progress: IngestionProgress) => {
    expect(screen.getByText(`${Math.round(progress.progress_percentage)}%`)).toBeInTheDocument()
    expect(screen.getByText(progress.current_step)).toBeInTheDocument()
  },
}

// Re-export commonly used testing utilities
export { vi } from "vitest"
export { render, screen, userEvent, waitFor } from "@/test/utils"
