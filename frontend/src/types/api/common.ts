/**
 * Common types and enums for API interactions
 * 
 * This module defines shared types, enums, and utility types used across
 * the API request/response interfaces, ensuring type safety and consistency.
 */

/**
 * Agent types available in the system
 * Matches the AgentType enum from the backend
 */
export enum AgentType {
  ORCHESTRATOR = "ORCHESTRATOR",
  TECHNICAL_ARCHITECT = "TECHNICAL_ARCHITECT", 
  TASK_PLANNER = "TASK_PLANNER",
  RAG_RETRIEVAL = "RAG_RETRIEVAL",
}

/**
 * API status values for system health checks
 */
export enum ApiStatus {
  OPERATIONAL = "operational",
  DEGRADED = "degraded", 
  DOWN = "down",
  MAINTENANCE = "maintenance",
}

/**
 * Ingestion status values for repository processing
 */
export enum IngestionStatus {
  PENDING = "pending",
  IN_PROGRESS = "in_progress",
  COMPLETED = "completed",
  FAILED = "failed",
  CANCELLED = "cancelled",
}

/**
 * Repository status values for repository management
 */
export enum RepositoryStatus {
  PENDING = "pending",
  PROCESSING = "processing",
  COMPLETED = "completed",
  FAILED = "failed",
  CANCELLED = "cancelled",
}

/**
 * Ingestion processing stages
 */
export enum IngestionStage {
  INITIALIZING = "initializing",
  CLONING = "cloning",
  SCANNING_FILES = "scanning_files",
  FILTERING_FILES = "filtering_files",
  EXTRACTING_METADATA = "extracting_metadata",
  CHUNKING_CONTENT = "chunking_content",
  GENERATING_EMBEDDINGS = "generating_embeddings",
  STORING_VECTORS = "storing_vectors",
  FINALIZING = "finalizing",
  COMPLETED = "completed",
}

/**
 * Context filters for query customization
 * Flexible object type for additional query parameters
 */
export type ContextFilters = Record<string, unknown>

/**
 * Structured response data container
 * Flexible object type for agent-specific structured data
 */
export type StructuredData = Record<string, unknown>

/**
 * Agent system status mapping
 * Maps agent type names to their operational status
 */
export type AgentSystemStatus = Record<string, string>

/**
 * Source citation format
 * Represents file references with optional line numbers
 * Format: "file_path:start_line-end_line" or just "file_path"
 */
export type SourceCitation = string

/**
 * Session identifier for conversation tracking
 */
export type SessionId = string

/**
 * User identifier for personalization
 */
export type UserId = string

/**
 * Repository URL or identifier
 */
export type RepositoryIdentifier = string

/**
 * Git branch name
 */
export type BranchName = string

/**
 * Git commit SHA hash
 */
export type CommitSha = string

/**
 * Confidence score range (0.0 to 1.0)
 */
export type ConfidenceScore = number

/**
 * Processing time in seconds
 */
export type ProcessingTime = number

/**
 * File count for ingestion metrics
 */
export type FileCount = number

/**
 * Chunk count for ingestion metrics  
 */
export type ChunkCount = number

/**
 * Embedding count for ingestion metrics
 */
export type EmbeddingCount = number

/**
 * Active session count for status monitoring
 */
export type ActiveSessionCount = number

/**
 * Repository unique identifier
 */
export type RepositoryId = string

/**
 * Repository metadata information
 */
export interface RepositoryMetadata {
  id: RepositoryId
  url: RepositoryIdentifier
  name: string
  owner: string
  branch: BranchName
  commit_sha: CommitSha
  is_private: boolean
  description?: string
  language?: string
  size?: number
  created_at: string
  updated_at: string
  ingested_at: string
  status: RepositoryStatus
  processed_files: FileCount
  chunks_created: ChunkCount
  embeddings_generated: EmbeddingCount
  processing_time: ProcessingTime
  error_message?: string
  error_details?: Record<string, unknown>
}

/**
 * Real-time ingestion progress information
 */
export interface IngestionProgress {
  repository_id: RepositoryId
  repository_url: RepositoryIdentifier
  status: RepositoryStatus
  stage: IngestionStage
  progress_percentage: number
  current_step: string
  files_discovered: number
  files_processed: number
  files_filtered: number
  chunks_created: number
  embeddings_generated: number
  started_at: string
  estimated_completion?: string
  elapsed_time: number
  errors: string[]
  warnings: string[]
}
