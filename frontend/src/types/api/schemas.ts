/**
 * Zod Validation Schemas for API Types
 * 
 * This module provides runtime validation schemas using Zod for all API types,
 * ensuring type safety at runtime and enabling proper error handling for
 * malformed API responses.
 */

import { z } from "zod"

/**
 * Schema for agent types
 */
export const AgentTypeSchema = z.enum([
  "ORCHESTRATOR",
  "TECHNICAL_ARCHITECT", 
  "TASK_PLANNER",
  "RAG_RETRIEVAL",
])

/**
 * Schema for API status values
 */
export const ApiStatusSchema = z.enum([
  "operational",
  "degraded",
  "down", 
  "maintenance",
])

/**
 * Schema for ingestion status values
 */
export const IngestionStatusSchema = z.enum([
  "pending",
  "in_progress",
  "completed",
  "failed",
  "cancelled",
])

/**
 * Schema for context filters (flexible object)
 */
export const ContextFiltersSchema = z.record(z.string(), z.unknown()).optional()

/**
 * Schema for structured data (flexible object)
 */
export const StructuredDataSchema = z.record(z.string(), z.unknown())

/**
 * Schema for confidence scores (0.0 to 1.0)
 */
export const ConfidenceScoreSchema = z.number().min(0).max(1)

/**
 * Schema for source citations
 */
export const SourceCitationSchema = z.string()

/**
 * Schema for session IDs
 */
export const SessionIdSchema = z.string()

/**
 * Schema for user IDs
 */
export const UserIdSchema = z.string()

/**
 * Schema for repository identifiers
 */
export const RepositoryIdentifierSchema = z.string().url()

/**
 * Schema for branch names
 */
export const BranchNameSchema = z.string().min(1)

/**
 * Schema for commit SHAs
 */
export const CommitShaSchema = z.string().regex(/^[a-f0-9]{40}$/)

/**
 * Schema for processing times (positive numbers)
 */
export const ProcessingTimeSchema = z.number().positive()

/**
 * Schema for file counts (non-negative integers)
 */
export const FileCountSchema = z.number().int().min(0)

/**
 * Schema for chunk counts (non-negative integers)
 */
export const ChunkCountSchema = z.number().int().min(0)

/**
 * Schema for embedding counts (non-negative integers)
 */
export const EmbeddingCountSchema = z.number().int().min(0)

/**
 * Schema for active session counts (non-negative integers)
 */
export const ActiveSessionCountSchema = z.number().int().min(0)

/**
 * Schema for repository IDs
 */
export const RepositoryIdSchema = z.string().min(1)

/**
 * Schema for repository status enum
 */
export const RepositoryStatusSchema = z.enum([
  "pending",
  "processing",
  "completed",
  "failed",
  "cancelled"
])

/**
 * Schema for ingestion stage enum
 */
export const IngestionStageSchema = z.enum([
  "initializing",
  "cloning",
  "scanning_files",
  "filtering_files",
  "extracting_metadata",
  "chunking_content",
  "generating_embeddings",
  "storing_vectors",
  "finalizing",
  "completed"
])

/**
 * Schema for agent system status mapping
 */
export const AgentSystemStatusSchema = z.record(z.string(), z.string())

/**
 * Schema for QueryRequest
 */
export const QueryRequestSchema = z.object({
  query: z.string().min(1),
  session_id: SessionIdSchema.optional(),
  user_id: UserIdSchema.optional(),
  context_filters: ContextFiltersSchema,
  repository: RepositoryIdentifierSchema.optional(),
})

/**
 * Schema for IngestionRequest
 */
export const IngestionRequestSchema = z.object({
  repository_url: RepositoryIdentifierSchema,
  branch: BranchNameSchema.optional(),
  commit_sha: CommitShaSchema.optional(),
  force_refresh: z.boolean().optional(),
})

/**
 * Schema for QueryResponse
 */
export const QueryResponseSchema = z.object({
  result_markdown: z.string(),
  structured: StructuredDataSchema,
  agent_type: z.string(),
  confidence: ConfidenceScoreSchema,
  sources: z.array(SourceCitationSchema),
  processing_time: ProcessingTimeSchema.optional(),
  session_id: SessionIdSchema,
})

/**
 * Schema for IngestionResponse
 */
export const IngestionResponseSchema = z.object({
  status: z.string(),
  repository: RepositoryIdentifierSchema,
  processed_files: FileCountSchema,
  chunks_created: ChunkCountSchema,
  embeddings_generated: EmbeddingCountSchema,
  processing_time: ProcessingTimeSchema,
})

/**
 * Schema for StatusResponse
 */
export const StatusResponseSchema = z.object({
  api: z.string(),
  agents: AgentSystemStatusSchema,
  vector_store: z.string(),
  ingestion_pipeline: z.string(),
  active_sessions: ActiveSessionCountSchema,
})

/**
 * Schema for RepositoryMetadata
 */
export const RepositoryMetadataSchema = z.object({
  id: RepositoryIdSchema,
  url: RepositoryIdentifierSchema,
  name: z.string(),
  owner: z.string(),
  branch: BranchNameSchema,
  commit_sha: CommitShaSchema,
  is_private: z.boolean(),
  description: z.string().optional(),
  language: z.string().optional(),
  size: z.number().optional(),
  created_at: z.string(),
  updated_at: z.string(),
  ingested_at: z.string(),
  status: RepositoryStatusSchema,
  processed_files: FileCountSchema,
  chunks_created: ChunkCountSchema,
  embeddings_generated: EmbeddingCountSchema,
  processing_time: ProcessingTimeSchema,
  error_message: z.string().optional(),
  error_details: z.record(z.string(), z.unknown()).optional(),
})

/**
 * Schema for IngestionProgress
 */
export const IngestionProgressSchema = z.object({
  repository_id: RepositoryIdSchema,
  repository_url: RepositoryIdentifierSchema,
  status: RepositoryStatusSchema,
  stage: IngestionStageSchema,
  progress_percentage: z.number().min(0).max(100),
  current_step: z.string(),
  files_discovered: z.number().int().min(0),
  files_processed: z.number().int().min(0),
  files_filtered: z.number().int().min(0),
  chunks_created: z.number().int().min(0),
  embeddings_generated: z.number().int().min(0),
  started_at: z.string(),
  estimated_completion: z.string().optional(),
  elapsed_time: z.number().min(0),
  errors: z.array(z.string()),
  warnings: z.array(z.string()),
})

/**
 * Schema for RepositoryListRequest
 */
export const RepositoryListRequestSchema = z.object({
  page: z.number().int().min(1).optional(),
  page_size: z.number().int().min(1).max(100).optional(),
  status_filter: RepositoryStatusSchema.optional(),
  search_query: z.string().optional(),
  sort_by: z.string().optional(),
  sort_order: z.enum(["asc", "desc"]).optional(),
})

/**
 * Schema for RepositoryListResponse
 */
export const RepositoryListResponseSchema = z.object({
  repositories: z.array(RepositoryMetadataSchema),
  total_count: z.number().int().min(0),
  page: z.number().int().min(1),
  page_size: z.number().int().min(1),
  total_pages: z.number().int().min(0),
})

/**
 * Schema for RepositoryDeleteRequest
 */
export const RepositoryDeleteRequestSchema = z.object({
  force: z.boolean().optional(),
})

/**
 * Schema for RepositoryDeleteResponse
 */
export const RepositoryDeleteResponseSchema = z.object({
  repository_id: RepositoryIdSchema,
  success: z.boolean(),
  message: z.string(),
})

/**
 * Schema for RepositoryReIngestRequest
 */
export const RepositoryReIngestRequestSchema = z.object({
  branch: BranchNameSchema.optional(),
  force_refresh: z.boolean().optional(),
})

/**
 * Schema for BatchOperationRequest
 */
export const BatchOperationRequestSchema = z.object({
  repository_ids: z.array(RepositoryIdSchema).min(1),
  operation: z.enum(["delete", "re_ingest"]),
  force: z.boolean().optional(),
})

/**
 * Schema for BatchOperationResponse
 */
export const BatchOperationResponseSchema = z.object({
  operation: z.string(),
  total_requested: z.number().int().min(0),
  successful: z.number().int().min(0),
  failed: z.number().int().min(0),
  results: z.array(z.object({
    repository_id: RepositoryIdSchema,
    success: z.boolean(),
    message: z.string(),
  })),
})

/**
 * Schema for RepositoryStatsResponse
 */
export const RepositoryStatsResponseSchema = z.object({
  total_repositories: z.number().int().min(0),
  by_status: z.record(z.string(), z.number().int().min(0)),
  total_files_processed: z.number().int().min(0),
  total_chunks_created: z.number().int().min(0),
  total_embeddings: z.number().int().min(0),
  average_processing_time: z.number().min(0),
  storage_usage_mb: z.number().min(0),
})

/**
 * Type inference helpers
 */
export type QueryRequestType = z.infer<typeof QueryRequestSchema>
export type IngestionRequestType = z.infer<typeof IngestionRequestSchema>
export type QueryResponseType = z.infer<typeof QueryResponseSchema>
export type IngestionResponseType = z.infer<typeof IngestionResponseSchema>
export type StatusResponseType = z.infer<typeof StatusResponseSchema>

// Repository management type inference helpers
export type RepositoryMetadataType = z.infer<typeof RepositoryMetadataSchema>
export type IngestionProgressType = z.infer<typeof IngestionProgressSchema>
export type RepositoryListRequestType = z.infer<typeof RepositoryListRequestSchema>
export type RepositoryListResponseType = z.infer<typeof RepositoryListResponseSchema>
export type RepositoryDeleteRequestType = z.infer<typeof RepositoryDeleteRequestSchema>
export type RepositoryDeleteResponseType = z.infer<typeof RepositoryDeleteResponseSchema>
export type RepositoryReIngestRequestType = z.infer<typeof RepositoryReIngestRequestSchema>
export type BatchOperationRequestType = z.infer<typeof BatchOperationRequestSchema>
export type BatchOperationResponseType = z.infer<typeof BatchOperationResponseSchema>
export type RepositoryStatsResponseType = z.infer<typeof RepositoryStatsResponseSchema>
