/**
 * API Response Type Definitions
 * 
 * This module defines TypeScript interfaces for all API response payloads,
 * matching the backend Pydantic models exactly to ensure type safety
 * and proper handling of server responses.
 */

import type {
  ActiveSessionCount,
  AgentSystemStatus,
  AgentType,
  ApiStatus,
  ChunkCount,
  ConfidenceScore,
  EmbeddingCount,
  FileCount,
  IngestionProgress,
  IngestionStatus,
  ProcessingTime,
  RepositoryId,
  RepositoryIdentifier,
  RepositoryMetadata,
  RepositoryStatus,
  SessionId,
  SourceCitation,
  StructuredData,
} from "./common"

/**
 * Response payload from the query endpoint
 * 
 * Corresponds to the backend QueryResponse Pydantic model.
 * Contains the processed response from the multi-agent system.
 * 
 * @example
 * ```typescript
 * const queryResponse: QueryResponse = {
 *   result_markdown: "# Authentication System\n\nThe system uses JWT tokens...",
 *   structured: { "components": ["auth", "jwt"], "complexity": "medium" },
 *   agent_type: "TECHNICAL_ARCHITECT",
 *   confidence: 0.95,
 *   sources: ["src/auth/jwt.py:1-50", "docs/auth.md:10-30"],
 *   processing_time: 2.3,
 *   session_id: "session-123"
 * }
 * ```
 */
export interface QueryResponse {
  /**
   * Formatted response content in Markdown
   * Contains the main response with proper formatting for display
   * @example "# Analysis Results\n\n## Overview\nThe authentication system..."
   */
  result_markdown: string

  /**
   * Structured response data for programmatic access
   * Contains machine-readable data extracted from the analysis
   * @example { "files_analyzed": 5, "patterns_found": ["singleton", "factory"] }
   */
  structured: StructuredData

  /**
   * Primary agent that processed the query
   * Indicates which agent was responsible for the main response
   * @example "TECHNICAL_ARCHITECT"
   */
  agent_type: string

  /**
   * Confidence score of the response (0.0 to 1.0)
   * Higher values indicate more confident responses
   * @example 0.85
   */
  confidence: ConfidenceScore

  /**
   * Source file citations referenced in the response
   * Array of file references with optional line numbers
   * @example ["src/main.py:10-25", "docs/architecture.md:45-60"]
   */
  sources: SourceCitation[]

  /**
   * Processing time in seconds (optional)
   * Time taken to generate the response
   * @example 2.34
   */
  processing_time?: ProcessingTime

  /**
   * Session ID for conversation tracking
   * Links the response to the conversation session
   * @example "session-abc123"
   */
  session_id: SessionId
}

/**
 * Response payload from the ingestion endpoint
 * 
 * Corresponds to the backend IngestionResponse Pydantic model.
 * Contains metrics and status information about repository ingestion.
 * 
 * @example
 * ```typescript
 * const ingestionResponse: IngestionResponse = {
 *   status: "completed",
 *   repository: "https://github.com/owner/repo",
 *   processed_files: 150,
 *   chunks_created: 1200,
 *   embeddings_generated: 1200,
 *   processing_time: 45.7
 * }
 * ```
 */
export interface IngestionResponse {
  /**
   * Ingestion status
   * Indicates the current state of the ingestion process
   * @example "completed"
   */
  status: string

  /**
   * Repository that was ingested
   * The repository URL that was processed
   * @example "https://github.com/owner/repository"
   */
  repository: RepositoryIdentifier

  /**
   * Number of files processed during ingestion
   * Count of individual files that were analyzed
   * @example 150
   */
  processed_files: FileCount

  /**
   * Number of text chunks created
   * Count of document chunks generated for embedding
   * @example 1200
   */
  chunks_created: ChunkCount

  /**
   * Number of embeddings generated
   * Count of vector embeddings created for search
   * @example 1200
   */
  embeddings_generated: EmbeddingCount

  /**
   * Total processing time in seconds
   * Time taken to complete the entire ingestion process
   * @example 45.7
   */
  processing_time: ProcessingTime
}

/**
 * Response payload from the status endpoint
 *
 * Corresponds to the backend StatusResponse Pydantic model.
 * Contains health and operational status of all system components.
 *
 * @example
 * ```typescript
 * const statusResponse: StatusResponse = {
 *   api: "operational",
 *   agents: {
 *     "ORCHESTRATOR": "operational",
 *     "TECHNICAL_ARCHITECT": "operational",
 *     "TASK_PLANNER": "operational"
 *   },
 *   vector_store: "operational",
 *   ingestion_pipeline: "operational",
 *   active_sessions: 5
 * }
 * ```
 */
export interface StatusResponse {
  /**
   * API service status
   * Overall health status of the API service
   * @example "operational"
   */
  api: string

  /**
   * Agent system status mapping
   * Status of each individual agent in the system
   * @example { "ORCHESTRATOR": "operational", "TASK_PLANNER": "error: timeout" }
   */
  agents: AgentSystemStatus

  /**
   * Vector store status
   * Health status of the vector database
   * @example "operational"
   */
  vector_store: string

  /**
   * Ingestion pipeline status
   * Health status of the document ingestion system
   * @example "operational"
   */
  ingestion_pipeline: string

  /**
   * Number of active conversation sessions
   * Current count of active user sessions
   * @example 5
   */
  active_sessions: ActiveSessionCount
}

/**
 * Response payload for repository listing
 *
 * Contains paginated list of repositories with metadata.
 *
 * @example
 * ```typescript
 * const listResponse: RepositoryListResponse = {
 *   repositories: [{ id: "repo-1", name: "my-repo", ... }],
 *   total_count: 50,
 *   page: 1,
 *   page_size: 20,
 *   total_pages: 3
 * }
 * ```
 */
export interface RepositoryListResponse {
  /**
   * List of repository metadata objects
   */
  repositories: RepositoryMetadata[]

  /**
   * Total number of repositories matching the filter
   */
  total_count: number

  /**
   * Current page number
   */
  page: number

  /**
   * Number of items per page
   */
  page_size: number

  /**
   * Total number of pages
   */
  total_pages: number
}

/**
 * Response payload for repository deletion
 *
 * @example
 * ```typescript
 * const deleteResponse: RepositoryDeleteResponse = {
 *   repository_id: "repo-123",
 *   success: true,
 *   message: "Repository deleted successfully"
 * }
 * ```
 */
export interface RepositoryDeleteResponse {
  /**
   * ID of the deleted repository
   */
  repository_id: RepositoryId

  /**
   * Whether the deletion was successful
   */
  success: boolean

  /**
   * Human-readable message about the deletion result
   */
  message: string
}

/**
 * Response payload for batch operations
 *
 * @example
 * ```typescript
 * const batchResponse: BatchOperationResponse = {
 *   operation: "delete",
 *   total_requested: 5,
 *   successful: 4,
 *   failed: 1,
 *   results: [...]
 * }
 * ```
 */
export interface BatchOperationResponse {
  /**
   * Operation that was performed
   */
  operation: string

  /**
   * Total number of repositories requested for operation
   */
  total_requested: number

  /**
   * Number of successfully processed repositories
   */
  successful: number

  /**
   * Number of failed repositories
   */
  failed: number

  /**
   * Detailed results for each repository
   */
  results: Array<{
    repository_id: RepositoryId
    success: boolean
    message: string
  }>
}

/**
 * Response payload for repository statistics
 *
 * @example
 * ```typescript
 * const statsResponse: RepositoryStatsResponse = {
 *   total_repositories: 25,
 *   by_status: { completed: 20, failed: 3, processing: 2 },
 *   total_files_processed: 15000,
 *   total_chunks_created: 75000,
 *   total_embeddings: 75000,
 *   average_processing_time: 45.2,
 *   storage_usage_mb: 1250.5
 * }
 * ```
 */
export interface RepositoryStatsResponse {
  /**
   * Total number of repositories in the system
   */
  total_repositories: number

  /**
   * Count of repositories by status
   */
  by_status: Record<string, number>

  /**
   * Total files processed across all repositories
   */
  total_files_processed: number

  /**
   * Total chunks created across all repositories
   */
  total_chunks_created: number

  /**
   * Total embeddings generated across all repositories
   */
  total_embeddings: number

  /**
   * Average processing time in seconds
   */
  average_processing_time: number

  /**
   * Storage usage in megabytes
   */
  storage_usage_mb: number
}
