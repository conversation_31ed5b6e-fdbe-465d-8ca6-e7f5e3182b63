/**
 * API Types Barrel Export
 * 
 * This module provides a centralized export point for all API-related types,
 * enabling clean imports throughout the application and maintaining a clear
 * separation between API types and other application types.
 * 
 * @example
 * ```typescript
 * import { QueryRequest, QueryResponse, ApiStatus } from '@/types/api'
 * ```
 */

// Common types and enums
export type {
  ContextFilters,
  StructuredData,
  AgentSystemStatus,
  SourceCitation,
  SessionId,
  UserId,
  RepositoryIdentifier,
  RepositoryId,
  BranchName,
  CommitSha,
  ConfidenceScore,
  ProcessingTime,
  FileCount,
  ChunkCount,
  EmbeddingCount,
  ActiveSessionCount,
  RepositoryMetadata,
  IngestionProgress,
} from "./common"

export {
  AgentType,
  ApiStatus,
  IngestionStatus,
  RepositoryStatus,
  IngestionStage,
} from "./common"

// Request types
export type {
  QueryRequest,
  IngestionRequest,
  RepositoryListRequest,
  RepositoryDeleteRequest,
  RepositoryReIngestRequest,
  BatchOperationRequest,
} from "./requests"

// Response types
export type {
  QueryResponse,
  IngestionResponse,
  StatusResponse,
  RepositoryListResponse,
  RepositoryDeleteResponse,
  BatchOperationResponse,
  RepositoryStatsResponse,
} from "./responses"

// Zod schemas for runtime validation
export {
  QueryRequestSchema,
  IngestionRequestSchema,
  QueryResponseSchema,
  IngestionResponseSchema,
  StatusResponseSchema,
  RepositoryMetadataSchema,
  IngestionProgressSchema,
  RepositoryListRequestSchema,
  RepositoryListResponseSchema,
  RepositoryDeleteRequestSchema,
  RepositoryDeleteResponseSchema,
  RepositoryReIngestRequestSchema,
  BatchOperationRequestSchema,
  BatchOperationResponseSchema,
  RepositoryStatsResponseSchema,
} from "./schemas"
