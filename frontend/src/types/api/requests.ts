/**
 * API Request Type Definitions
 * 
 * This module defines TypeScript interfaces for all API request payloads,
 * matching the backend Pydantic models exactly to ensure type safety
 * across the client-server boundary.
 */

import type {
  BranchName,
  CommitSha,
  ContextFilters,
  RepositoryId,
  RepositoryIdentifier,
  RepositoryStatus,
  SessionId,
  UserId,
} from "./common"

/**
 * Request payload for the query endpoint
 * 
 * Corresponds to the backend QueryRequest Pydantic model.
 * Used for natural language queries to the multi-agent system.
 * 
 * @example
 * ```typescript
 * const queryRequest: QueryRequest = {
 *   query: "How does the authentication system work?",
 *   session_id: "session-123",
 *   user_id: "user-456",
 *   repository: "https://github.com/owner/repo"
 * }
 * ```
 */
export interface QueryRequest {
  /**
   * Natural language query to process
   * @example "Explain the authentication flow in this codebase"
   */
  query: string

  /**
   * Optional session ID for conversation context
   * Enables multi-turn conversations with context preservation
   * @example "session-abc123"
   */
  session_id?: SessionId

  /**
   * Optional user ID for personalization
   * Allows for user-specific customizations and tracking
   * @example "user-xyz789"
   */
  user_id?: UserId

  /**
   * Optional additional context filters
   * Flexible object for query customization and filtering
   * @example { "file_types": ["py", "ts"], "max_results": 10 }
   */
  context_filters?: ContextFilters

  /**
   * Optional target repository for query
   * Specifies which repository to focus the query on
   * @example "https://github.com/owner/repo"
   */
  repository?: RepositoryIdentifier
}

/**
 * Request payload for the ingestion endpoint
 * 
 * Corresponds to the backend IngestionRequest Pydantic model.
 * Used to trigger repository ingestion into the knowledge base.
 * 
 * @example
 * ```typescript
 * const ingestionRequest: IngestionRequest = {
 *   repository_url: "https://github.com/owner/repo",
 *   branch: "main",
 *   force_refresh: true
 * }
 * ```
 */
export interface IngestionRequest {
  /**
   * GitHub repository URL to ingest
   * Must be a valid GitHub repository URL
   * @example "https://github.com/owner/repository"
   */
  repository_url: RepositoryIdentifier

  /**
   * Branch to ingest (defaults to "main" on backend)
   * Specifies which branch to process for ingestion
   * @example "develop"
   * @default "main"
   */
  branch?: BranchName

  /**
   * Specific commit SHA to ingest
   * If provided, ingests the repository at this specific commit
   * @example "a1b2c3d4e5f6789012345678901234567890abcd"
   */
  commit_sha?: CommitSha

  /**
   * Force refresh of existing data
   * When true, re-processes the repository even if already ingested
   * @default false
   */
  force_refresh?: boolean
}

/**
 * Request payload for listing repositories with filtering and pagination
 *
 * @example
 * ```typescript
 * const listRequest: RepositoryListRequest = {
 *   page: 1,
 *   page_size: 20,
 *   status_filter: RepositoryStatus.COMPLETED,
 *   search_query: "react"
 * }
 * ```
 */
export interface RepositoryListRequest {
  /**
   * Page number for pagination
   * @default 1
   */
  page?: number

  /**
   * Number of items per page
   * @default 20
   */
  page_size?: number

  /**
   * Filter repositories by status
   */
  status_filter?: RepositoryStatus

  /**
   * Search query for repository name or owner
   */
  search_query?: string

  /**
   * Field to sort by
   * @default "updated_at"
   */
  sort_by?: string

  /**
   * Sort order (asc or desc)
   * @default "desc"
   */
  sort_order?: "asc" | "desc"
}

/**
 * Request payload for deleting a repository
 *
 * @example
 * ```typescript
 * const deleteRequest: RepositoryDeleteRequest = {
 *   force: true
 * }
 * ```
 */
export interface RepositoryDeleteRequest {
  /**
   * Force deletion even if repository is processing
   * @default false
   */
  force?: boolean
}

/**
 * Request payload for re-ingesting a repository
 *
 * @example
 * ```typescript
 * const reIngestRequest: RepositoryReIngestRequest = {
 *   branch: "develop",
 *   force_refresh: true
 * }
 * ```
 */
export interface RepositoryReIngestRequest {
  /**
   * Branch to ingest (use current if not specified)
   */
  branch?: BranchName

  /**
   * Force refresh of existing data
   * @default true
   */
  force_refresh?: boolean
}

/**
 * Request payload for batch operations on multiple repositories
 *
 * @example
 * ```typescript
 * const batchRequest: BatchOperationRequest = {
 *   repository_ids: ["repo-1", "repo-2"],
 *   operation: "delete",
 *   force: false
 * }
 * ```
 */
export interface BatchOperationRequest {
  /**
   * List of repository IDs to operate on
   */
  repository_ids: RepositoryId[]

  /**
   * Operation to perform
   */
  operation: "delete" | "re_ingest"

  /**
   * Force operation even if repositories are processing
   * @default false
   */
  force?: boolean
}
