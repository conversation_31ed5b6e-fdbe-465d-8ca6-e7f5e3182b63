/**
 * Error Type Definitions
 * 
 * This module defines TypeScript interfaces and types for error handling
 * throughout the application, matching backend error response structures
 * and providing a unified error handling approach.
 */

/**
 * HTTP status codes for API errors
 */
export enum HttpStatusCode {
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  METHOD_NOT_ALLOWED = 405,
  CONFLICT = 409,
  UNPROCESSABLE_ENTITY = 422,
  TOO_MANY_REQUESTS = 429,
  INTERNAL_SERVER_ERROR = 500,
  BAD_GATEWAY = 502,
  SERVICE_UNAVAILABLE = 503,
  GATEWAY_TIMEOUT = 504,
}

/**
 * Error categories for classification
 */
export enum ErrorCategory {
  NETWORK = "network",
  VALIDATION = "validation",
  AUTHENTICATION = "authentication",
  AUTHORIZATION = "authorization",
  NOT_FOUND = "not_found",
  SERVER = "server",
  AGENT = "agent",
  INGESTION = "ingestion",
  UNKNOWN = "unknown",
}

/**
 * Error severity levels
 */
export enum ErrorSeverity {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  CRITICAL = "critical",
}

/**
 * Base error interface matching backend error structure
 */
export interface BaseError {
  /**
   * Error message for display
   */
  message: string

  /**
   * Error category for classification
   */
  category: ErrorCategory

  /**
   * Error severity level
   */
  severity: ErrorSeverity

  /**
   * Timestamp when error occurred
   */
  timestamp: string

  /**
   * Additional error details
   */
  details?: Record<string, unknown>

  /**
   * Error code for programmatic handling
   */
  code?: string

  /**
   * Stack trace for debugging (development only)
   */
  stack?: string
}

/**
 * API error interface for HTTP errors
 * Matches FastAPI HTTPException structure
 */
export interface ApiError extends BaseError {
  /**
   * HTTP status code
   */
  status: HttpStatusCode

  /**
   * Error detail from backend
   */
  detail: string

  /**
   * Request URL that caused the error
   */
  url?: string

  /**
   * HTTP method used
   */
  method?: string

  /**
   * Request ID for tracing
   */
  requestId?: string
}

/**
 * Agent-specific error interface
 * For errors from the multi-agent system
 */
export interface AgentError extends BaseError {
  /**
   * Agent type that caused the error
   */
  agentType?: string

  /**
   * Operation that failed
   */
  operation?: string

  /**
   * Agent-specific error details
   */
  agentDetails?: {
    timeout?: number
    retryCount?: number
    modelUsed?: string
    confidence?: number
  }
}

/**
 * Ingestion error interface
 * For repository ingestion failures
 */
export interface IngestionError extends BaseError {
  /**
   * Repository URL that failed
   */
  repositoryUrl?: string

  /**
   * Branch that was being processed
   */
  branch?: string

  /**
   * Commit SHA if specific commit failed
   */
  commitSha?: string

  /**
   * Ingestion stage where error occurred
   */
  stage?: "cloning" | "parsing" | "chunking" | "embedding" | "storing"

  /**
   * Files processed before error
   */
  filesProcessed?: number
}

/**
 * Validation error interface
 * For form and input validation errors
 */
export interface ValidationError extends BaseError {
  /**
   * Field that failed validation
   */
  field?: string

  /**
   * Validation rule that was violated
   */
  rule?: string

  /**
   * Expected value or format
   */
  expected?: string

  /**
   * Actual value that was provided
   */
  actual?: string
}

/**
 * Network error interface
 * For connectivity and network-related errors
 */
export interface NetworkError extends BaseError {
  /**
   * Network error type
   */
  type: "timeout" | "offline" | "dns" | "connection" | "unknown"

  /**
   * Retry attempt number
   */
  retryAttempt?: number

  /**
   * Maximum retry attempts
   */
  maxRetries?: number

  /**
   * Whether retry is possible
   */
  retryable: boolean
}

/**
 * Union type for all possible errors
 */
export type AppError = 
  | ApiError 
  | AgentError 
  | IngestionError 
  | ValidationError 
  | NetworkError 
  | BaseError

/**
 * Error context for error boundaries
 */
export interface ErrorContext {
  /**
   * Component where error occurred
   */
  component?: string

  /**
   * User action that triggered the error
   */
  action?: string

  /**
   * Additional context data
   */
  context?: Record<string, unknown>

  /**
   * User ID if available
   */
  userId?: string

  /**
   * Session ID if available
   */
  sessionId?: string
}

/**
 * Error handler function type
 */
export type ErrorHandler = (error: AppError, context?: ErrorContext) => void

/**
 * Error recovery function type
 */
export type ErrorRecovery = () => void | Promise<void>
