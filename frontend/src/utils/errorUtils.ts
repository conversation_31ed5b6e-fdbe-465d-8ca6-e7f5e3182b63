/**
 * Error Utility Functions
 * 
 * This module provides utility functions for error handling, formatting,
 * classification, and processing throughout the application.
 */

import type {
  AppError,
  ApiError,
  AgentError,
  IngestionError,
  ValidationError,
  NetworkError,
  BaseError,
} from "@/types/errors"

import {
  ErrorCategory,
  ErrorSeverity,
  HttpStatusCode,
} from "@/types/errors"

/**
 * Creates a base error with common properties
 */
export function createBaseError(
  message: string,
  category: ErrorCategory,
  severity: ErrorSeverity = ErrorSeverity.MEDIUM,
  details?: Record<string, unknown>
): BaseError {
  return {
    message,
    category,
    severity,
    timestamp: new Date().toISOString(),
    details,
    stack: process.env.NODE_ENV === "development" ? new Error().stack : undefined,
  }
}

/**
 * Creates an API error from a fetch response
 */
export async function createApiError(
  response: Response,
  url?: string,
  method?: string
): Promise<ApiError> {
  let detail = "An API error occurred"
  
  try {
    const errorData = await response.json()
    detail = errorData.detail || errorData.message || detail
  } catch {
    // If response is not JSON, use status text
    detail = response.statusText || detail
  }

  const category = categorizeHttpError(response.status as HttpStatusCode)
  const severity = getSeverityFromStatus(response.status as HttpStatusCode)

  return {
    ...createBaseError(detail, category, severity),
    status: response.status as HttpStatusCode,
    detail,
    url,
    method,
  }
}

/**
 * Creates an agent error
 */
export function createAgentError(
  message: string,
  agentType?: string,
  operation?: string,
  agentDetails?: AgentError["agentDetails"]
): AgentError {
  return {
    ...createBaseError(message, ErrorCategory.AGENT, ErrorSeverity.HIGH),
    agentType,
    operation,
    agentDetails,
  }
}

/**
 * Creates an ingestion error
 */
export function createIngestionError(
  message: string,
  repositoryUrl?: string,
  stage?: IngestionError["stage"],
  details?: Partial<IngestionError>
): IngestionError {
  return {
    ...createBaseError(message, ErrorCategory.INGESTION, ErrorSeverity.HIGH),
    repositoryUrl,
    stage,
    ...details,
  }
}

/**
 * Creates a validation error
 */
export function createValidationError(
  message: string,
  field?: string,
  rule?: string,
  expected?: string,
  actual?: string
): ValidationError {
  return {
    ...createBaseError(message, ErrorCategory.VALIDATION, ErrorSeverity.MEDIUM),
    field,
    rule,
    expected,
    actual,
  }
}

/**
 * Creates a network error
 */
export function createNetworkError(
  message: string,
  type: NetworkError["type"],
  retryable = true,
  retryAttempt?: number,
  maxRetries?: number
): NetworkError {
  return {
    ...createBaseError(message, ErrorCategory.NETWORK, ErrorSeverity.HIGH),
    type,
    retryable,
    retryAttempt,
    maxRetries,
  }
}

/**
 * Categorizes HTTP errors by status code
 */
export function categorizeHttpError(status: HttpStatusCode): ErrorCategory {
  if (status >= 400 && status < 500) {
    switch (status) {
      case HttpStatusCode.UNAUTHORIZED:
        return ErrorCategory.AUTHENTICATION
      case HttpStatusCode.FORBIDDEN:
        return ErrorCategory.AUTHORIZATION
      case HttpStatusCode.NOT_FOUND:
        return ErrorCategory.NOT_FOUND
      case HttpStatusCode.BAD_REQUEST:
      case HttpStatusCode.UNPROCESSABLE_ENTITY:
        return ErrorCategory.VALIDATION
      default:
        return ErrorCategory.VALIDATION
    }
  }
  
  if (status >= 500) {
    return ErrorCategory.SERVER
  }
  
  return ErrorCategory.UNKNOWN
}

/**
 * Gets error severity from HTTP status code
 */
export function getSeverityFromStatus(status: HttpStatusCode): ErrorSeverity {
  if (status >= 500) {
    return ErrorSeverity.CRITICAL
  }
  
  if (status === HttpStatusCode.UNAUTHORIZED || status === HttpStatusCode.FORBIDDEN) {
    return ErrorSeverity.HIGH
  }
  
  if (status === HttpStatusCode.NOT_FOUND) {
    return ErrorSeverity.MEDIUM
  }
  
  return ErrorSeverity.LOW
}

/**
 * Formats an error for user display
 */
export function formatErrorForUser(error: AppError): string {
  switch (error.category) {
    case ErrorCategory.NETWORK:
      return "Connection error. Please check your internet connection and try again."
    
    case ErrorCategory.AUTHENTICATION:
      return "Authentication required. Please log in and try again."
    
    case ErrorCategory.AUTHORIZATION:
      return "You don't have permission to perform this action."
    
    case ErrorCategory.NOT_FOUND:
      return "The requested resource was not found."
    
    case ErrorCategory.VALIDATION:
      return error.message || "Please check your input and try again."
    
    case ErrorCategory.AGENT:
      return "The AI system encountered an error. Please try again."
    
    case ErrorCategory.INGESTION:
      return "Repository processing failed. Please check the repository URL and try again."
    
    case ErrorCategory.SERVER:
      return "Server error. Please try again later."
    
    default:
      return error.message || "An unexpected error occurred."
  }
}

/**
 * Checks if an error is retryable
 */
export function isRetryableError(error: AppError): boolean {
  if ("retryable" in error) {
    return error.retryable
  }
  
  if ("status" in error) {
    // Retry on server errors and some client errors
    return error.status >= 500 || error.status === HttpStatusCode.TOO_MANY_REQUESTS
  }
  
  return error.category === ErrorCategory.NETWORK
}

/**
 * Gets retry delay based on attempt number
 */
export function getRetryDelay(attempt: number, baseDelay = 1000): number {
  // Exponential backoff with jitter
  const delay = baseDelay * Math.pow(2, attempt)
  const jitter = Math.random() * 0.1 * delay
  return delay + jitter
}

/**
 * Logs error for debugging and monitoring
 */
export function logError(error: AppError, context?: Record<string, unknown>): void {
  const logData = {
    error: {
      message: error.message,
      category: error.category,
      severity: error.severity,
      timestamp: error.timestamp,
      ...("status" in error && { status: error.status }),
      ...("agentType" in error && { agentType: error.agentType }),
    },
    context,
  }
  
  if (error.severity === ErrorSeverity.CRITICAL) {
    console.error("Critical error:", logData)
  } else if (error.severity === ErrorSeverity.HIGH) {
    console.error("High severity error:", logData)
  } else {
    console.warn("Error:", logData)
  }
}
