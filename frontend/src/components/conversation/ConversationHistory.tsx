"use client"

import * as React from "react"
import { MessageCircle, ArrowDown, Arrow<PERSON>p, MoreVertical } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"

import { useSession } from "@/providers/session-provider"
import { cn } from "@/lib/utils"
import type { ConversationMessage } from "@/types/api"

/**
 * Props for the ConversationHistory component
 */
export interface ConversationHistoryProps {
  /**
   * Custom CSS class name
   */
  className?: string
  
  /**
   * Maximum height for the conversation area
   */
  maxHeight?: number
  
  /**
   * Whether to show session information
   */
  showSessionInfo?: boolean
  
  /**
   * Whether to auto-scroll to new messages
   */
  autoScroll?: boolean
  
  /**
   * Whether to show message timestamps
   */
  showTimestamps?: boolean
  
  /**
   * Whether to show message actions
   */
  showActions?: boolean
  
  /**
   * Custom message renderer
   */
  messageRenderer?: (message: ConversationMessage, index: number) => React.ReactNode
  
  /**
   * Callback fired when a message is selected
   */
  onMessageSelect?: (message: ConversationMessage, index: number) => void
}

/**
 * ConversationHistory Component
 * 
 * Main container for displaying conversation message history with features:
 * - Integration with SessionProvider for message data
 * - Auto-scroll to new messages
 * - Scroll management with scroll-to-top/bottom buttons
 * - Message threading and timestamps
 * - Responsive design for mobile and desktop
 * - Accessibility support with proper ARIA labels
 * 
 * @example
 * ```tsx
 * <ConversationHistory
 *   maxHeight={600}
 *   showSessionInfo={true}
 *   autoScroll={true}
 *   showTimestamps={true}
 *   onMessageSelect={(message, index) => console.log('Selected:', message)}
 * />
 * ```
 */
export function ConversationHistory({
  className,
  maxHeight = 600,
  showSessionInfo = true,
  autoScroll = true,
  showTimestamps = true,
  showActions = true,
  messageRenderer,
  onMessageSelect,
}: ConversationHistoryProps) {
  const session = useSession()
  const scrollAreaRef = React.useRef<HTMLDivElement>(null)
  const [isAtBottom, setIsAtBottom] = React.useState(true)
  const [isAtTop, setIsAtTop] = React.useState(true)
  const [showScrollButtons, setShowScrollButtons] = React.useState(false)
  
  const messages = session.session.conversationHistory || []
  const hasMessages = messages.length > 0
  
  // Handle scroll position detection
  const handleScroll = React.useCallback((event: React.UIEvent<HTMLDivElement>) => {
    const target = event.target as HTMLDivElement
    const { scrollTop, scrollHeight, clientHeight } = target
    
    const atTop = scrollTop === 0
    const atBottom = scrollTop + clientHeight >= scrollHeight - 10 // 10px threshold
    
    setIsAtTop(atTop)
    setIsAtBottom(atBottom)
    setShowScrollButtons(scrollHeight > clientHeight)
  }, [])
  
  // Scroll to bottom
  const scrollToBottom = React.useCallback(() => {
    const scrollArea = scrollAreaRef.current?.querySelector('[data-radix-scroll-area-viewport]')
    if (scrollArea) {
      scrollArea.scrollTo({
        top: scrollArea.scrollHeight,
        behavior: "smooth",
      })
    }
  }, [])
  
  // Scroll to top
  const scrollToTop = React.useCallback(() => {
    const scrollArea = scrollAreaRef.current?.querySelector('[data-radix-scroll-area-viewport]')
    if (scrollArea) {
      scrollArea.scrollTo({
        top: 0,
        behavior: "smooth",
      })
    }
  }, [])
  
  // Auto-scroll to bottom when new messages arrive
  React.useEffect(() => {
    if (autoScroll && isAtBottom && hasMessages) {
      // Small delay to ensure DOM is updated
      setTimeout(scrollToBottom, 100)
    }
  }, [messages.length, autoScroll, isAtBottom, hasMessages, scrollToBottom])
  
  // Format timestamp
  const formatTimestamp = React.useCallback((timestamp: Date) => {
    const now = new Date()
    const messageTime = new Date(timestamp)
    const diffInMinutes = Math.floor((now.getTime() - messageTime.getTime()) / 60000)
    
    if (diffInMinutes < 1) return "Just now"
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
    
    return messageTime.toLocaleDateString()
  }, [])
  
  // Group messages by date for better organization
  const groupedMessages = React.useMemo(() => {
    const groups: Array<{ date: string; messages: Array<{ message: ConversationMessage; index: number }> }> = []
    
    messages.forEach((message, index) => {
      const messageDate = new Date(message.metadata?.timestamp || new Date()).toDateString()
      
      let group = groups.find(g => g.date === messageDate)
      if (!group) {
        group = { date: messageDate, messages: [] }
        groups.push(group)
      }
      
      group.messages.push({ message, index })
    })
    
    return groups
  }, [messages])
  
  return (
    <Card className={className}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-base flex items-center gap-2">
              <MessageCircle className="h-4 w-4" />
              Conversation History
            </CardTitle>
            <CardDescription>
              {hasMessages 
                ? `${messages.length} message${messages.length === 1 ? "" : "s"} in this session`
                : "No messages yet"
              }
            </CardDescription>
          </div>
          
          {showSessionInfo && session.isActive && (
            <Badge variant="outline" className="text-xs">
              Session: {session.session.sessionId?.slice(-8) || "Unknown"}
            </Badge>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="relative">
        {hasMessages ? (
          <div className="relative">
            <ScrollArea
              ref={scrollAreaRef}
              className="w-full rounded-md border"
              style={{ height: `${maxHeight}px` }}
              onScrollCapture={handleScroll}
            >
              <div className="p-4 space-y-6">
                {groupedMessages.map((group, groupIndex) => (
                  <div key={group.date}>
                    {/* Date separator */}
                    {groupedMessages.length > 1 && (
                      <div className="flex items-center gap-4 my-4">
                        <Separator className="flex-1" />
                        <span className="text-xs text-muted-foreground font-medium">
                          {group.date === new Date().toDateString() ? "Today" : group.date}
                        </span>
                        <Separator className="flex-1" />
                      </div>
                    )}
                    
                    {/* Messages in this date group */}
                    <div className="space-y-4">
                      {group.messages.map(({ message, index }) => (
                        <div
                          key={index}
                          className={cn(
                            "group relative",
                            onMessageSelect && "cursor-pointer hover:bg-muted/50 rounded-lg p-2 -m-2"
                          )}
                          onClick={() => onMessageSelect?.(message, index)}
                        >
                          {messageRenderer ? (
                            messageRenderer(message, index)
                          ) : (
                            <div className="space-y-2">
                              {/* Message header */}
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                  <Badge 
                                    variant={message.type === "user" ? "default" : "secondary"}
                                    className="text-xs"
                                  >
                                    {message.type === "user" ? "You" : "Assistant"}
                                  </Badge>
                                  
                                  {message.metadata?.agent_type && (
                                    <Badge variant="outline" className="text-xs">
                                      {message.metadata.agent_type}
                                    </Badge>
                                  )}
                                  
                                  {showTimestamps && message.metadata?.timestamp && (
                                    <span className="text-xs text-muted-foreground">
                                      {formatTimestamp(new Date(message.metadata.timestamp))}
                                    </span>
                                  )}
                                </div>
                                
                                {showActions && (
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                                    aria-label="Message actions"
                                  >
                                    <MoreVertical className="h-3 w-3" />
                                  </Button>
                                )}
                              </div>
                              
                              {/* Message content */}
                              <div className="text-sm leading-relaxed">
                                {message.content}
                              </div>
                              
                              {/* Message metadata */}
                              {message.metadata?.confidence && (
                                <div className="text-xs text-muted-foreground">
                                  Confidence: {Math.round(message.metadata.confidence * 100)}%
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
            
            {/* Scroll buttons */}
            {showScrollButtons && (
              <div className="absolute right-2 bottom-2 flex flex-col gap-1">
                {!isAtTop && (
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={scrollToTop}
                    className="h-8 w-8 bg-background/80 backdrop-blur-sm"
                    aria-label="Scroll to top"
                  >
                    <ArrowUp className="h-4 w-4" />
                  </Button>
                )}
                
                {!isAtBottom && (
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={scrollToBottom}
                    className="h-8 w-8 bg-background/80 backdrop-blur-sm"
                    aria-label="Scroll to bottom"
                  >
                    <ArrowDown className="h-4 w-4" />
                  </Button>
                )}
              </div>
            )}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <MessageCircle className="h-12 w-12 text-muted-foreground/50 mb-4" />
            <h3 className="text-lg font-medium text-muted-foreground mb-2">
              No conversation yet
            </h3>
            <p className="text-sm text-muted-foreground max-w-sm">
              Start by submitting a query above. Your conversation history will appear here.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
