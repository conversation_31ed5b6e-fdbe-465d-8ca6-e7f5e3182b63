"use client"

import * as React from "react"
import { Download, MessageCircle, Plus, RotateCcw, Settings, Trash2, Upload } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"

import { useSession } from "@/providers/session-provider"
import { useToast } from "@/hooks/useToast"
import { cn } from "@/lib/utils"

/**
 * Props for the ConversationControls component
 */
export interface ConversationControlsProps {
  /**
   * Custom CSS class name
   */
  className?: string
  
  /**
   * Whether to show export/import functionality
   */
  showExportImport?: boolean
  
  /**
   * Whether to show session settings
   */
  showSettings?: boolean
  
  /**
   * Callback fired when conversation is exported
   */
  onExport?: (data: any) => void
  
  /**
   * Callback fired when conversation is imported
   */
  onImport?: (data: any) => void
  
  /**
   * Callback fired when session settings change
   */
  onSettingsChange?: (settings: any) => void
}

/**
 * ConversationControls Component
 * 
 * Session management controls with features:
 * - Create new session
 * - Clear conversation history
 * - Export/import conversation data
 * - Session settings management
 * - Confirmation dialogs for destructive actions
 * - Accessibility support
 * 
 * @example
 * ```tsx
 * <ConversationControls
 *   showExportImport={true}
 *   showSettings={true}
 *   onExport={(data) => console.log('Exported:', data)}
 *   onImport={(data) => console.log('Imported:', data)}
 * />
 * ```
 */
export function ConversationControls({
  className,
  showExportImport = true,
  showSettings = false,
  onExport,
  onImport,
  onSettingsChange,
}: ConversationControlsProps) {
  const session = useSession()
  const { toast } = useToast()
  const [isExportDialogOpen, setIsExportDialogOpen] = React.useState(false)
  const [isImportDialogOpen, setIsImportDialogOpen] = React.useState(false)
  const [importData, setImportData] = React.useState("")
  
  const hasMessages = session.messageCount > 0
  
  // Handle creating new session
  const handleNewSession = React.useCallback(async () => {
    try {
      await session.createSession()
      toast({
        title: "New session created",
        description: "Started a fresh conversation session",
      })
    } catch (error) {
      toast({
        title: "Failed to create session",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      })
    }
  }, [session, toast])
  
  // Handle clearing conversation history
  const handleClearHistory = React.useCallback(() => {
    session.clearHistory()
    toast({
      title: "Conversation cleared",
      description: "All messages have been removed from this session",
    })
  }, [session, toast])
  
  // Handle ending current session
  const handleEndSession = React.useCallback(() => {
    session.endSession()
    toast({
      title: "Session ended",
      description: "The current session has been terminated",
    })
  }, [session, toast])
  
  // Handle exporting conversation
  const handleExport = React.useCallback(() => {
    const exportData = {
      sessionId: session.session.sessionId,
      userId: session.session.userId,
      createdAt: session.session.createdAt,
      messages: session.session.conversationHistory,
      metadata: session.session.metadata,
      exportedAt: new Date().toISOString(),
    }
    
    // Create downloadable file
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: "application/json",
    })
    const url = URL.createObjectURL(blob)
    const link = document.createElement("a")
    link.href = url
    link.download = `conversation-${session.session.sessionId || "unknown"}-${new Date().toISOString().split("T")[0]}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    toast({
      title: "Conversation exported",
      description: "Conversation data has been downloaded",
    })
    
    onExport?.(exportData)
    setIsExportDialogOpen(false)
  }, [session, toast, onExport])
  
  // Handle importing conversation
  const handleImport = React.useCallback(() => {
    try {
      const data = JSON.parse(importData)
      
      // Validate import data structure
      if (!data.messages || !Array.isArray(data.messages)) {
        throw new Error("Invalid conversation data format")
      }
      
      // Import messages into current session
      data.messages.forEach((message: any) => {
        session.addMessage(message)
      })
      
      toast({
        title: "Conversation imported",
        description: `Imported ${data.messages.length} messages`,
      })
      
      onImport?.(data)
      setIsImportDialogOpen(false)
      setImportData("")
    } catch (error) {
      toast({
        title: "Import failed",
        description: error instanceof Error ? error.message : "Invalid data format",
        variant: "destructive",
      })
    }
  }, [importData, session, toast, onImport])
  
  // Handle file import
  const handleFileImport = React.useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return
    
    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target?.result as string
      setImportData(content)
    }
    reader.readAsText(file)
  }, [])
  
  return (
    <Card className={className}>
      <CardHeader className="pb-4">
        <CardTitle className="text-base flex items-center gap-2">
          <Settings className="h-4 w-4" />
          Conversation Controls
        </CardTitle>
        <CardDescription>
          Manage your conversation session and data
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Primary Actions */}
        <div className="grid grid-cols-2 gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleNewSession}
            className="h-9"
          >
            <Plus className="mr-2 h-4 w-4" />
            New Session
          </Button>
          
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                disabled={!hasMessages}
                className="h-9"
              >
                <RotateCcw className="mr-2 h-4 w-4" />
                Clear History
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Clear Conversation History</AlertDialogTitle>
                <AlertDialogDescription>
                  This will remove all messages from the current session. 
                  This action cannot be undone.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={handleClearHistory}>
                  Clear History
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
        
        {/* Export/Import Actions */}
        {showExportImport && (
          <>
            <Separator />
            <div className="space-y-2">
              <Label className="text-sm font-medium">Data Management</Label>
              <div className="grid grid-cols-2 gap-2">
                <Dialog open={isExportDialogOpen} onOpenChange={setIsExportDialogOpen}>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={!hasMessages}
                      className="h-9"
                    >
                      <Download className="mr-2 h-4 w-4" />
                      Export
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Export Conversation</DialogTitle>
                      <DialogDescription>
                        Download your conversation data as a JSON file. This includes all messages, 
                        metadata, and session information.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="text-sm text-muted-foreground">
                        <p><strong>Session ID:</strong> {session.session.sessionId || "Unknown"}</p>
                        <p><strong>Messages:</strong> {session.messageCount}</p>
                        <p><strong>Created:</strong> {session.session.createdAt?.toLocaleString() || "Unknown"}</p>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setIsExportDialogOpen(false)}>
                        Cancel
                      </Button>
                      <Button onClick={handleExport}>
                        <Download className="mr-2 h-4 w-4" />
                        Download
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
                
                <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-9"
                    >
                      <Upload className="mr-2 h-4 w-4" />
                      Import
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Import Conversation</DialogTitle>
                      <DialogDescription>
                        Import conversation data from a previously exported JSON file. 
                        Messages will be added to the current session.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="file-import">Upload File</Label>
                        <Input
                          id="file-import"
                          type="file"
                          accept=".json"
                          onChange={handleFileImport}
                          className="mt-2"
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="data-import">Or Paste JSON Data</Label>
                        <Textarea
                          id="data-import"
                          placeholder="Paste exported conversation JSON here..."
                          value={importData}
                          onChange={(e) => setImportData(e.target.value)}
                          className="mt-2 min-h-[120px] font-mono text-xs"
                        />
                      </div>
                    </div>
                    <DialogFooter>
                      <Button 
                        variant="outline" 
                        onClick={() => {
                          setIsImportDialogOpen(false)
                          setImportData("")
                        }}
                      >
                        Cancel
                      </Button>
                      <Button 
                        onClick={handleImport}
                        disabled={!importData.trim()}
                      >
                        <Upload className="mr-2 h-4 w-4" />
                        Import
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </div>
          </>
        )}
        
        {/* Destructive Actions */}
        <Separator />
        <div className="space-y-2">
          <Label className="text-sm font-medium text-destructive">Danger Zone</Label>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                disabled={!session.isActive}
                className="h-9 w-full text-destructive hover:text-destructive border-destructive/20 hover:border-destructive/40"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                End Session
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>End Current Session</AlertDialogTitle>
                <AlertDialogDescription>
                  This will terminate the current session and clear all conversation data. 
                  This action cannot be undone. Consider exporting your conversation first.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction 
                  onClick={handleEndSession}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                >
                  End Session
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
        
        {/* Session Status */}
        <div className="text-xs text-muted-foreground text-center pt-2 border-t">
          {session.isActive ? (
            <span>Session active • {session.messageCount} messages</span>
          ) : (
            <span>No active session</span>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
