/**
 * Repository Error Boundary
 * 
 * Error boundary component specifically designed for repository operations
 * with comprehensive error handling, recovery suggestions, and retry mechanisms.
 */

"use client"

import * as React from "react"
import { AlertTriangle, RefreshCw, Home, Bug } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { RepositoryErrorHandler, type RepositoryError } from "@/lib/errors/repository-errors"

/**
 * Error boundary state
 */
interface ErrorBoundaryState {
  hasError: boolean
  error: RepositoryError | null
  errorInfo: React.ErrorInfo | null
  retryCount: number
}

/**
 * Props for the RepositoryErrorBoundary component
 */
export interface RepositoryErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<{
    error: RepositoryError
    retry: () => void
    reset: () => void
  }>
  onError?: (error: RepositoryError, errorInfo: React.ErrorInfo) => void
  maxRetries?: number
  showDetails?: boolean
}

/**
 * Default error fallback component
 */
function DefaultErrorFallback({
  error,
  retry,
  reset,
  showDetails = false,
}: {
  error: RepositoryError
  retry: () => void
  reset: () => void
  showDetails?: boolean
}) {
  const [showErrorDetails, setShowErrorDetails] = React.useState(false)

  return (
    <div className="flex items-center justify-center min-h-[400px] p-6">
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
            <AlertTriangle className="h-6 w-6 text-destructive" />
          </div>
          <CardTitle className="text-xl">Repository Operation Failed</CardTitle>
          <CardDescription className="text-base">
            {error.userMessage}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Error suggestions */}
          {error.suggestions.length > 0 && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <div className="font-medium mb-2">Suggested actions:</div>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  {error.suggestions.map((suggestion, index) => (
                    <li key={index}>{suggestion}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}
          
          {/* Action buttons */}
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            {error.retryable && (
              <Button onClick={retry} className="flex items-center gap-2">
                <RefreshCw className="h-4 w-4" />
                Try Again
              </Button>
            )}
            
            <Button variant="outline" onClick={reset} className="flex items-center gap-2">
              <Home className="h-4 w-4" />
              Go Back
            </Button>
            
            {showDetails && (
              <Button
                variant="ghost"
                onClick={() => setShowErrorDetails(!showErrorDetails)}
                className="flex items-center gap-2"
              >
                <Bug className="h-4 w-4" />
                {showErrorDetails ? "Hide" : "Show"} Details
              </Button>
            )}
          </div>
          
          {/* Error details (for debugging) */}
          {showDetails && showErrorDetails && (
            <>
              <Separator />
              <div className="space-y-2">
                <div className="text-sm font-medium">Error Details:</div>
                <div className="text-xs font-mono bg-muted p-3 rounded-md overflow-auto">
                  <div><strong>Type:</strong> {error.type}</div>
                  <div><strong>Message:</strong> {error.message}</div>
                  {error.context && (
                    <div><strong>Context:</strong> {JSON.stringify(error.context, null, 2)}</div>
                  )}
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * Repository Error Boundary Component
 * 
 * @example
 * ```tsx
 * <RepositoryErrorBoundary
 *   onError={(error, info) => console.log('Error:', error)}
 *   maxRetries={3}
 *   showDetails={process.env.NODE_ENV === 'development'}
 * >
 *   <RepositoryManagementPage />
 * </RepositoryErrorBoundary>
 * ```
 */
export class RepositoryErrorBoundary extends React.Component<
  RepositoryErrorBoundaryProps,
  ErrorBoundaryState
> {
  private retryTimeoutId: NodeJS.Timeout | null = null

  constructor(props: RepositoryErrorBoundaryProps) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
    }
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    const repositoryError = RepositoryErrorHandler.parseError(error)
    
    return {
      hasError: true,
      error: repositoryError,
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const repositoryError = RepositoryErrorHandler.parseError(error, {
      componentStack: errorInfo.componentStack,
      errorBoundary: true,
    })

    this.setState({
      error: repositoryError,
      errorInfo,
    })

    // Call onError callback if provided
    this.props.onError?.(repositoryError, errorInfo)

    // Log error for monitoring
    console.error("Repository Error Boundary caught an error:", {
      error: repositoryError,
      errorInfo,
    })
  }

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId)
    }
  }

  handleRetry = () => {
    const { maxRetries = 3 } = this.props
    const { retryCount, error } = this.state

    if (retryCount >= maxRetries) {
      RepositoryErrorHandler.handleError(
        new Error("Maximum retry attempts reached"),
        { retryCount, maxRetries },
        { customMessage: "Maximum retry attempts reached. Please refresh the page or contact support." }
      )
      return
    }

    // Clear error state and increment retry count
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: retryCount + 1,
    })

    // If error has a retry delay, wait before retrying
    if (error?.retryDelay) {
      this.retryTimeoutId = setTimeout(() => {
        // Force re-render to retry
        this.forceUpdate()
      }, error.retryDelay)
    }
  }

  handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
    })
  }

  render() {
    const { children, fallback: Fallback, showDetails = false } = this.props
    const { hasError, error } = this.state

    if (hasError && error) {
      if (Fallback) {
        return (
          <Fallback
            error={error}
            retry={this.handleRetry}
            reset={this.handleReset}
          />
        )
      }

      return (
        <DefaultErrorFallback
          error={error}
          retry={this.handleRetry}
          reset={this.handleReset}
          showDetails={showDetails}
        />
      )
    }

    return children
  }
}

/**
 * Hook for handling repository errors in functional components
 */
export function useRepositoryErrorHandler() {
  const handleError = React.useCallback((
    error: any,
    context?: Record<string, any>,
    options?: {
      showToast?: boolean
      onRetry?: () => void
      customMessage?: string
    }
  ) => {
    return RepositoryErrorHandler.handleError(error, context, options)
  }, [])

  const createRetryFunction = React.useCallback((
    originalFunction: () => Promise<any>,
    maxRetries?: number,
    baseDelay?: number
  ) => {
    return RepositoryErrorHandler.createRetryFunction(originalFunction, maxRetries, baseDelay)
  }, [])

  return {
    handleError,
    createRetryFunction,
    getDisplayMessage: RepositoryErrorHandler.getDisplayMessage,
    getSuggestions: RepositoryErrorHandler.getSuggestions,
    isRetryable: RepositoryErrorHandler.isRetryable,
  }
}
