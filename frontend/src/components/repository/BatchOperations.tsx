/**
 * Batch Operations Component
 * 
 * Advanced bulk repository management component with progress tracking,
 * detailed confirmation dialogs, and comprehensive error handling for
 * batch operations on multiple repositories.
 */

"use client"

import * as React from "react"
import { 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Loader2, 
  RefreshCw, 
  Trash2, 
  <PERSON>Circle 
} from "lucide-react"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Progress } from "@/components/ui/progress"
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"

import { useBatchOperation } from "@/hooks/api"
import { useRepositoryErrorHandler } from "@/components/error"
import type { RepositoryMetadata, RepositoryId } from "@/types/api"

/**
 * Batch operation types
 */
export type BatchOperationType = "delete" | "re_ingest"

/**
 * Batch operation status
 */
export interface BatchOperationStatus {
  operation: BatchOperationType
  total: number
  completed: number
  failed: number
  inProgress: boolean
  results: Array<{
    repository_id: RepositoryId
    status: "pending" | "success" | "error"
    error?: string
  }>
}

/**
 * Props for the BatchOperations component
 */
export interface BatchOperationsProps {
  /**
   * Selected repositories for batch operations
   */
  selectedRepositories: RepositoryMetadata[]
  
  /**
   * Custom CSS class name
   */
  className?: string
  
  /**
   * Callback fired when batch operation is completed
   */
  onOperationComplete?: (operation: BatchOperationType, results: BatchOperationStatus) => void
  
  /**
   * Callback fired when selection should be cleared
   */
  onClearSelection?: () => void
  
  /**
   * Whether operations are disabled
   */
  disabled?: boolean
}

/**
 * Batch operation confirmation dialog
 */
function BatchOperationDialog({
  open,
  onOpenChange,
  operation,
  repositories,
  onConfirm,
  isLoading,
}: {
  open: boolean
  onOpenChange: (open: boolean) => void
  operation: BatchOperationType
  repositories: RepositoryMetadata[]
  onConfirm: (force: boolean) => void
  isLoading: boolean
}) {
  const [force, setForce] = React.useState(false)
  const [showDetails, setShowDetails] = React.useState(false)
  
  const operationConfig = {
    delete: {
      title: "Delete Repositories",
      description: "This will permanently delete the selected repositories and all associated data.",
      warning: "This action cannot be undone and will remove all processed files, chunks, and embeddings.",
      buttonText: "Delete Repositories",
      buttonVariant: "destructive" as const,
    },
    re_ingest: {
      title: "Re-ingest Repositories",
      description: "This will re-process the selected repositories, updating their content and regenerating embeddings.",
      warning: "Re-ingestion will overwrite existing data for these repositories.",
      buttonText: "Re-ingest Repositories",
      buttonVariant: "default" as const,
    },
  }
  
  const config = operationConfig[operation]
  const processingRepositories = repositories.filter(repo => repo.status === "processing")
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            {config.title} ({repositories.length})
          </DialogTitle>
          <DialogDescription>
            {config.description}
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {config.warning}
            </AlertDescription>
          </Alert>
          
          {processingRepositories.length > 0 && (
            <Alert>
              <Clock className="h-4 w-4" />
              <AlertDescription>
                {processingRepositories.length} repositories are currently being processed. 
                Check "Force operation" to proceed anyway.
              </AlertDescription>
            </Alert>
          )}
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Selected Repositories</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowDetails(!showDetails)}
              >
                {showDetails ? "Hide" : "Show"} Details
              </Button>
            </div>
            
            {showDetails && (
              <ScrollArea className="h-32 border rounded-md p-3">
                <div className="space-y-2">
                  {repositories.map((repo) => (
                    <div key={repo.id} className="flex items-center justify-between text-sm">
                      <span>{repo.owner}/{repo.name}</span>
                      <Badge variant={repo.status === "processing" ? "default" : "secondary"}>
                        {repo.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            )}
          </div>
          
          {(processingRepositories.length > 0 || operation === "delete") && (
            <div className="flex items-center space-x-2">
              <Checkbox
                id="force-operation"
                checked={force}
                onCheckedChange={setForce}
              />
              <label htmlFor="force-operation" className="text-sm">
                Force operation (ignore processing status)
              </label>
            </div>
          )}
        </div>
        
        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button 
            variant={config.buttonVariant}
            onClick={() => onConfirm(force)}
            disabled={isLoading}
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {config.buttonText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

/**
 * Batch operation progress display
 */
function BatchOperationProgress({
  status,
  onClose,
}: {
  status: BatchOperationStatus
  onClose: () => void
}) {
  const progressPercentage = status.total > 0 ? (status.completed + status.failed) / status.total * 100 : 0
  const successRate = status.completed > 0 ? status.completed / (status.completed + status.failed) * 100 : 0
  
  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">
            Batch {status.operation === "delete" ? "Delete" : "Re-ingest"} Progress
          </CardTitle>
          {!status.inProgress && (
            <Button variant="ghost" size="sm" onClick={onClose}>
              Close
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Overall Progress</span>
            <span>{Math.round(progressPercentage)}%</span>
          </div>
          <Progress value={progressPercentage} className="h-2" />
        </div>
        
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-green-600">{status.completed}</div>
            <div className="text-sm text-muted-foreground">Completed</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-destructive">{status.failed}</div>
            <div className="text-sm text-muted-foreground">Failed</div>
          </div>
          <div>
            <div className="text-2xl font-bold">{status.total}</div>
            <div className="text-sm text-muted-foreground">Total</div>
          </div>
        </div>
        
        {!status.inProgress && (
          <Alert>
            {status.failed === 0 ? (
              <>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  Batch operation completed successfully! All {status.completed} repositories were processed.
                </AlertDescription>
              </>
            ) : (
              <>
                <XCircle className="h-4 w-4" />
                <AlertDescription>
                  Batch operation completed with {status.failed} failures. 
                  {status.completed > 0 && ` ${status.completed} repositories were processed successfully.`}
                </AlertDescription>
              </>
            )}
          </Alert>
        )}
        
        {status.results.some(r => r.status === "error") && (
          <div className="space-y-2">
            <div className="text-sm font-medium">Failed Operations:</div>
            <ScrollArea className="h-24 border rounded-md p-2">
              <div className="space-y-1">
                {status.results
                  .filter(r => r.status === "error")
                  .map((result) => (
                    <div key={result.repository_id} className="text-xs">
                      <span className="font-medium">{result.repository_id}:</span>{" "}
                      <span className="text-destructive">{result.error}</span>
                    </div>
                  ))}
              </div>
            </ScrollArea>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

/**
 * Batch Operations Component
 * 
 * @example
 * ```tsx
 * <BatchOperations
 *   selectedRepositories={selectedRepos}
 *   onOperationComplete={(operation, results) => console.log(operation, results)}
 *   onClearSelection={() => setSelectedRepos([])}
 * />
 * ```
 */
export function BatchOperations({
  selectedRepositories,
  className,
  onOperationComplete,
  onClearSelection,
  disabled = false,
}: BatchOperationsProps) {
  const [dialogOpen, setDialogOpen] = React.useState(false)
  const [currentOperation, setCurrentOperation] = React.useState<BatchOperationType | null>(null)
  const [operationStatus, setOperationStatus] = React.useState<BatchOperationStatus | null>(null)
  
  const { handleError } = useRepositoryErrorHandler()
  
  const batchMutation = useBatchOperation({
    onSuccess: (data) => {
      const status: BatchOperationStatus = {
        operation: currentOperation!,
        total: data.total_requested,
        completed: data.successful,
        failed: data.failed,
        inProgress: false,
        results: data.results.map(result => ({
          repository_id: result.repository_id,
          status: result.success ? "success" : "error",
          error: result.error,
        })),
      }
      
      setOperationStatus(status)
      setDialogOpen(false)
      onOperationComplete?.(currentOperation!, status)
    },
    onError: (error) => {
      handleError(error, { context: { operation: "batch_operation" } })
      setDialogOpen(false)
    },
  })
  
  const handleOperationStart = React.useCallback((operation: BatchOperationType) => {
    setCurrentOperation(operation)
    setDialogOpen(true)
  }, [])
  
  const handleOperationConfirm = React.useCallback((force: boolean) => {
    if (!currentOperation || selectedRepositories.length === 0) return
    
    const repositoryIds = selectedRepositories.map(repo => repo.id)
    
    // Initialize operation status
    setOperationStatus({
      operation: currentOperation,
      total: repositoryIds.length,
      completed: 0,
      failed: 0,
      inProgress: true,
      results: repositoryIds.map(id => ({
        repository_id: id,
        status: "pending",
      })),
    })
    
    batchMutation.mutate({
      repository_ids: repositoryIds,
      operation: currentOperation,
      force,
    })
  }, [currentOperation, selectedRepositories, batchMutation])
  
  const handleCloseProgress = React.useCallback(() => {
    setOperationStatus(null)
    setCurrentOperation(null)
    onClearSelection?.()
  }, [onClearSelection])
  
  const hasSelection = selectedRepositories.length > 0
  const isLoading = batchMutation.isPending
  
  if (operationStatus) {
    return (
      <div className={className}>
        <BatchOperationProgress
          status={operationStatus}
          onClose={handleCloseProgress}
        />
      </div>
    )
  }
  
  return (
    <div className={cn("space-y-4", className)}>
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">Batch Operations</CardTitle>
          <CardDescription>
            {hasSelection 
              ? `${selectedRepositories.length} repositories selected`
              : "Select repositories to perform batch operations"
            }
          </CardDescription>
        </CardHeader>
        
        {hasSelection && (
          <CardContent className="space-y-3">
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleOperationStart("re_ingest")}
                disabled={disabled || isLoading}
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Re-ingest All ({selectedRepositories.length})
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleOperationStart("delete")}
                disabled={disabled || isLoading}
                className="text-destructive hover:text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete All ({selectedRepositories.length})
              </Button>
            </div>
            
            <div className="text-xs text-muted-foreground">
              Operations will be performed on: {selectedRepositories.map(r => `${r.owner}/${r.name}`).join(", ")}
            </div>
          </CardContent>
        )}
      </Card>
      
      {/* Batch Operation Confirmation Dialog */}
      {currentOperation && (
        <BatchOperationDialog
          open={dialogOpen}
          onOpenChange={setDialogOpen}
          operation={currentOperation}
          repositories={selectedRepositories}
          onConfirm={handleOperationConfirm}
          isLoading={isLoading}
        />
      )}
    </div>
  )
}
