/**
 * Real-time Ingestion Progress Component
 * 
 * Displays detailed ingestion progress with file counts, current processing step,
 * progress percentage, estimated time remaining, and error reporting with retry options.
 */

"use client"

import * as React from "react"
import { 
  AlertCircle, 
  CheckCircle, 
  Clock, 
  FileText, 
  Loader2, 
  RefreshCw, 
  XCircle,
  Zap
} from "lucide-react"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"

import { useIngestionProgress } from "@/hooks/api"
import type { IngestionProgress as IngestionProgressType, RepositoryId } from "@/types/api"

/**
 * Props for the IngestionProgress component
 */
export interface IngestionProgressProps {
  /**
   * Repository ID to track progress for
   */
  repositoryId: RepositoryId
  
  /**
   * Custom CSS class name
   */
  className?: string
  
  /**
   * Callback fired when retry is requested
   */
  onRetry?: (repositoryId: RepositoryId) => void
  
  /**
   * Callback fired when progress is completed
   */
  onComplete?: (repositoryId: RepositoryId) => void
  
  /**
   * Whether to show detailed statistics
   */
  showDetails?: boolean
  
  /**
   * Whether to auto-refresh progress
   */
  autoRefresh?: boolean
  
  /**
   * Refresh interval in milliseconds
   */
  refreshInterval?: number
}

/**
 * Stage display configuration
 */
const STAGE_CONFIG = {
  initializing: { label: "Initializing", icon: Loader2, color: "text-blue-500" },
  cloning: { label: "Cloning Repository", icon: Loader2, color: "text-blue-500" },
  scanning_files: { label: "Scanning Files", icon: FileText, color: "text-yellow-500" },
  filtering_files: { label: "Filtering Files", icon: FileText, color: "text-yellow-500" },
  extracting_metadata: { label: "Extracting Metadata", icon: Zap, color: "text-orange-500" },
  chunking_content: { label: "Chunking Content", icon: Zap, color: "text-orange-500" },
  generating_embeddings: { label: "Generating Embeddings", icon: Zap, color: "text-purple-500" },
  storing_vectors: { label: "Storing Vectors", icon: Zap, color: "text-purple-500" },
  finalizing: { label: "Finalizing", icon: Loader2, color: "text-green-500" },
  completed: { label: "Completed", icon: CheckCircle, color: "text-green-500" },
}

/**
 * Status badge component
 */
function StatusBadge({ status }: { status: string }) {
  const statusConfig = {
    pending: { variant: "secondary" as const, label: "Pending" },
    processing: { variant: "default" as const, label: "Processing" },
    completed: { variant: "success" as const, label: "Completed" },
    failed: { variant: "destructive" as const, label: "Failed" },
    cancelled: { variant: "outline" as const, label: "Cancelled" },
  }
  
  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending
  
  return (
    <Badge variant={config.variant} className="text-xs">
      {config.label}
    </Badge>
  )
}

/**
 * Format duration in seconds to human readable format
 */
function formatDuration(seconds: number): string {
  if (seconds < 60) {
    return `${Math.round(seconds)}s`
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.round(seconds % 60)
    return `${minutes}m ${remainingSeconds}s`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${hours}h ${minutes}m`
  }
}

/**
 * Format estimated completion time
 */
function formatEstimatedCompletion(estimatedCompletion?: string): string {
  if (!estimatedCompletion) return "Calculating..."
  
  const now = new Date()
  const completion = new Date(estimatedCompletion)
  const diffMs = completion.getTime() - now.getTime()
  
  if (diffMs <= 0) return "Almost done"
  
  const diffSeconds = Math.floor(diffMs / 1000)
  return `~${formatDuration(diffSeconds)} remaining`
}

/**
 * Real-time Ingestion Progress Component
 * 
 * @example
 * ```tsx
 * <IngestionProgress
 *   repositoryId="repo-123"
 *   onRetry={(id) => console.log('Retry:', id)}
 *   onComplete={(id) => console.log('Completed:', id)}
 *   showDetails={true}
 * />
 * ```
 */
export function IngestionProgress({
  repositoryId,
  className,
  onRetry,
  onComplete,
  showDetails = true,
  autoRefresh = true,
  refreshInterval = 2000,
}: IngestionProgressProps) {
  const { data: progress, isLoading, error, refetch } = useIngestionProgress(repositoryId, {
    enabled: !!repositoryId && autoRefresh,
    refetchInterval: (data) => {
      // Stop polling if completed or failed
      if (data?.status === "completed" || data?.status === "failed") {
        return false
      }
      return refreshInterval
    },
  })
  
  // Handle completion callback
  React.useEffect(() => {
    if (progress?.status === "completed") {
      onComplete?.(repositoryId)
    }
  }, [progress?.status, repositoryId, onComplete])
  
  if (isLoading && !progress) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Loading progress...</span>
          </div>
        </CardContent>
      </Card>
    )
  }
  
  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load progress information.
              <Button 
                onClick={() => refetch()} 
                variant="outline" 
                size="sm" 
                className="ml-2"
              >
                Retry
              </Button>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }
  
  if (!progress) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <p className="text-muted-foreground">No progress information available</p>
        </CardContent>
      </Card>
    )
  }
  
  const stageConfig = STAGE_CONFIG[progress.stage as keyof typeof STAGE_CONFIG] || STAGE_CONFIG.initializing
  const StageIcon = stageConfig.icon
  const isCompleted = progress.status === "completed"
  const isFailed = progress.status === "failed"
  const isProcessing = progress.status === "processing"
  
  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Ingestion Progress</CardTitle>
          <div className="flex items-center gap-2">
            <StatusBadge status={progress.status} />
            {!isCompleted && !isFailed && (
              <Button onClick={() => refetch()} variant="ghost" size="sm">
                <RefreshCw className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Current Stage */}
        <div className="flex items-center gap-3">
          <StageIcon className={cn("h-5 w-5", stageConfig.color, isProcessing && "animate-spin")} />
          <div className="flex-1">
            <div className="font-medium">{stageConfig.label}</div>
            <div className="text-sm text-muted-foreground">{progress.current_step}</div>
          </div>
        </div>
        
        {/* Progress Bar */}
        {!isFailed && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progress</span>
              <span>{Math.round(progress.progress_percentage)}%</span>
            </div>
            <Progress 
              value={progress.progress_percentage} 
              className="h-2"
            />
          </div>
        )}
        
        {/* Timing Information */}
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <Clock className="h-4 w-4" />
            <span>Elapsed: {formatDuration(progress.elapsed_time)}</span>
          </div>
          {progress.estimated_completion && isProcessing && (
            <span>{formatEstimatedCompletion(progress.estimated_completion)}</span>
          )}
        </div>
        
        {/* Detailed Statistics */}
        {showDetails && (
          <>
            <Separator />
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <div className="font-medium">Files</div>
                <div className="text-muted-foreground">
                  {progress.files_processed.toLocaleString()} / {progress.files_discovered.toLocaleString()} processed
                </div>
                {progress.files_filtered > 0 && (
                  <div className="text-muted-foreground">
                    {progress.files_filtered.toLocaleString()} filtered
                  </div>
                )}
              </div>
              <div>
                <div className="font-medium">Content</div>
                <div className="text-muted-foreground">
                  {progress.chunks_created.toLocaleString()} chunks
                </div>
                <div className="text-muted-foreground">
                  {progress.embeddings_generated.toLocaleString()} embeddings
                </div>
              </div>
            </div>
          </>
        )}
        
        {/* Errors and Warnings */}
        {(progress.errors.length > 0 || progress.warnings.length > 0) && (
          <>
            <Separator />
            <div className="space-y-2">
              {progress.errors.length > 0 && (
                <Alert variant="destructive">
                  <XCircle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="font-medium">Errors ({progress.errors.length})</div>
                    <ul className="mt-1 list-disc list-inside text-sm">
                      {progress.errors.slice(0, 3).map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                      {progress.errors.length > 3 && (
                        <li>... and {progress.errors.length - 3} more</li>
                      )}
                    </ul>
                  </AlertDescription>
                </Alert>
              )}
              
              {progress.warnings.length > 0 && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="font-medium">Warnings ({progress.warnings.length})</div>
                    <ul className="mt-1 list-disc list-inside text-sm">
                      {progress.warnings.slice(0, 2).map((warning, index) => (
                        <li key={index}>{warning}</li>
                      ))}
                      {progress.warnings.length > 2 && (
                        <li>... and {progress.warnings.length - 2} more</li>
                      )}
                    </ul>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </>
        )}
        
        {/* Action Buttons */}
        {isFailed && onRetry && (
          <div className="flex justify-end">
            <Button onClick={() => onRetry(repositoryId)} variant="outline">
              <RefreshCw className="mr-2 h-4 w-4" />
              Retry Ingestion
            </Button>
          </div>
        )}
        
        {isCompleted && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              Repository ingestion completed successfully! 
              Processed {progress.files_processed.toLocaleString()} files 
              in {formatDuration(progress.elapsed_time)}.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}
