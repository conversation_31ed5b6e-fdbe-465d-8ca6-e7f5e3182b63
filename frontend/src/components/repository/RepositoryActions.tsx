/**
 * Repository Actions Component
 * 
 * Provides individual repository operations (view details, re-ingest, delete)
 * with confirmation dialogs, loading states, and bulk selection capabilities.
 */

"use client"

import * as React from "react"
import { 
  AlertTriangle, 
  ExternalLink, 
  Eye, 
  Loader2, 
  Refresh<PERSON><PERSON>, 
  Trash2 
} from "lucide-react"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"

import { 
  useRepositoryDelete, 
  useRepositoryReIngest, 
  useBatchOperation 
} from "@/hooks/api"
import type { RepositoryMetadata, RepositoryId } from "@/types/api"

/**
 * Props for the RepositoryActions component
 */
export interface RepositoryActionsProps {
  /**
   * Repository to perform actions on
   */
  repository?: RepositoryMetadata
  
  /**
   * Selected repository IDs for bulk operations
   */
  selectedRepositoryIds?: RepositoryId[]
  
  /**
   * Custom CSS class name
   */
  className?: string
  
  /**
   * Callback fired when an action is completed
   */
  onActionComplete?: (action: string, repositoryId?: RepositoryId) => void
  
  /**
   * Callback fired when repository details should be viewed
   */
  onViewDetails?: (repository: RepositoryMetadata) => void
  
  /**
   * Whether to show bulk actions
   */
  showBulkActions?: boolean
  
  /**
   * Whether actions are disabled
   */
  disabled?: boolean
}

/**
 * Confirmation dialog for delete operations
 */
function DeleteConfirmationDialog({
  open,
  onOpenChange,
  repository,
  selectedCount,
  onConfirm,
  isLoading,
}: {
  open: boolean
  onOpenChange: (open: boolean) => void
  repository?: RepositoryMetadata
  selectedCount?: number
  onConfirm: (force: boolean) => void
  isLoading: boolean
}) {
  const [force, setForce] = React.useState(false)
  const isBulk = selectedCount && selectedCount > 1
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            {isBulk ? `Delete ${selectedCount} Repositories` : "Delete Repository"}
          </DialogTitle>
          <DialogDescription>
            {isBulk ? (
              <>
                Are you sure you want to delete {selectedCount} repositories? 
                This action cannot be undone and will remove all associated data.
              </>
            ) : (
              <>
                Are you sure you want to delete <strong>{repository?.owner}/{repository?.name}</strong>? 
                This action cannot be undone and will remove all associated data including 
                processed files, chunks, and embeddings.
              </>
            )}
          </DialogDescription>
        </DialogHeader>
        
        {(repository?.status === "processing" || (isBulk && selectedCount)) && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {isBulk ? (
                "Some repositories may be currently processing. "
              ) : (
                "This repository is currently being processed. "
              )}
              Check "Force delete" to proceed anyway.
            </AlertDescription>
          </Alert>
        )}
        
        <div className="flex items-center space-x-2">
          <Checkbox
            id="force-delete"
            checked={force}
            onCheckedChange={setForce}
          />
          <label htmlFor="force-delete" className="text-sm">
            Force delete (ignore processing status)
          </label>
        </div>
        
        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button 
            variant="destructive" 
            onClick={() => onConfirm(force)}
            disabled={isLoading}
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isBulk ? `Delete ${selectedCount} Repositories` : "Delete Repository"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

/**
 * Confirmation dialog for re-ingest operations
 */
function ReIngestConfirmationDialog({
  open,
  onOpenChange,
  repository,
  selectedCount,
  onConfirm,
  isLoading,
}: {
  open: boolean
  onOpenChange: (open: boolean) => void
  repository?: RepositoryMetadata
  selectedCount?: number
  onConfirm: () => void
  isLoading: boolean
}) {
  const isBulk = selectedCount && selectedCount > 1
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {isBulk ? `Re-ingest ${selectedCount} Repositories` : "Re-ingest Repository"}
          </DialogTitle>
          <DialogDescription>
            {isBulk ? (
              <>
                This will re-process {selectedCount} repositories, updating their content 
                and regenerating embeddings. This may take some time.
              </>
            ) : (
              <>
                This will re-process <strong>{repository?.owner}/{repository?.name}</strong>, 
                updating its content and regenerating embeddings. This may take some time.
              </>
            )}
          </DialogDescription>
        </DialogHeader>
        
        <Alert>
          <RefreshCw className="h-4 w-4" />
          <AlertDescription>
            Re-ingestion will overwrite existing data for {isBulk ? "these repositories" : "this repository"}. 
            The {isBulk ? "repositories" : "repository"} will be marked as "processing" during this operation.
          </AlertDescription>
        </Alert>
        
        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button 
            onClick={onConfirm}
            disabled={isLoading}
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isBulk ? `Re-ingest ${selectedCount} Repositories` : "Re-ingest Repository"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

/**
 * Repository Actions Component
 * 
 * @example
 * ```tsx
 * <RepositoryActions
 *   repository={repository}
 *   selectedRepositoryIds={["repo-1", "repo-2"]}
 *   onActionComplete={(action, id) => console.log(action, id)}
 *   onViewDetails={(repo) => console.log('View:', repo)}
 *   showBulkActions={true}
 * />
 * ```
 */
export function RepositoryActions({
  repository,
  selectedRepositoryIds = [],
  className,
  onActionComplete,
  onViewDetails,
  showBulkActions = false,
  disabled = false,
}: RepositoryActionsProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false)
  const [reIngestDialogOpen, setReIngestDialogOpen] = React.useState(false)
  const [bulkAction, setBulkAction] = React.useState<"delete" | "re-ingest" | null>(null)
  
  // Mutations
  const deleteMutation = useRepositoryDelete({
    onSuccess: (data) => {
      setDeleteDialogOpen(false)
      onActionComplete?.("delete", data.repository_id)
    },
  })
  
  const reIngestMutation = useRepositoryReIngest({
    onSuccess: (data, variables) => {
      setReIngestDialogOpen(false)
      onActionComplete?.("re-ingest", variables.repository_id)
    },
  })
  
  const batchMutation = useBatchOperation({
    onSuccess: (data) => {
      setDeleteDialogOpen(false)
      setReIngestDialogOpen(false)
      setBulkAction(null)
      onActionComplete?.(data.operation)
    },
  })
  
  // Handle single repository delete
  const handleDelete = React.useCallback((force: boolean) => {
    if (!repository) return
    deleteMutation.mutate({
      repository_id: repository.id,
      force,
    })
  }, [repository, deleteMutation])
  
  // Handle single repository re-ingest
  const handleReIngest = React.useCallback(() => {
    if (!repository) return
    reIngestMutation.mutate({
      repository_id: repository.id,
      force_refresh: true,
    })
  }, [repository, reIngestMutation])
  
  // Handle bulk operations
  const handleBulkOperation = React.useCallback((operation: "delete" | "re_ingest", force = false) => {
    if (selectedRepositoryIds.length === 0) return
    
    batchMutation.mutate({
      repository_ids: selectedRepositoryIds,
      operation,
      force,
    })
  }, [selectedRepositoryIds, batchMutation])
  
  const isLoading = deleteMutation.isPending || reIngestMutation.isPending || batchMutation.isPending
  const hasSelection = selectedRepositoryIds.length > 0
  const canDelete = repository ? repository.status !== "processing" : true
  const canReIngest = repository ? repository.status !== "processing" : true
  
  return (
    <div className={cn("space-y-4", className)}>
      {/* Single Repository Actions */}
      {repository && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Repository Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="font-medium">{repository.owner}/{repository.name}</span>
                <Badge variant="outline">{repository.status}</Badge>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.open(repository.url.toString(), "_blank")}
              >
                <ExternalLink className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onViewDetails?.(repository)}
                disabled={disabled}
              >
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setReIngestDialogOpen(true)}
                disabled={disabled || !canReIngest || isLoading}
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Re-ingest
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setDeleteDialogOpen(true)}
                disabled={disabled || isLoading}
                className="text-destructive hover:text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
      
      {/* Bulk Actions */}
      {showBulkActions && hasSelection && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">
              Bulk Actions ({selectedRepositoryIds.length} selected)
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setBulkAction("re-ingest")
                  setReIngestDialogOpen(true)
                }}
                disabled={disabled || isLoading}
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Re-ingest All
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setBulkAction("delete")
                  setDeleteDialogOpen(true)
                }}
                disabled={disabled || isLoading}
                className="text-destructive hover:text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete All
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
      
      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        repository={bulkAction === "delete" ? undefined : repository}
        selectedCount={bulkAction === "delete" ? selectedRepositoryIds.length : undefined}
        onConfirm={(force) => {
          if (bulkAction === "delete") {
            handleBulkOperation("delete", force)
          } else {
            handleDelete(force)
          }
        }}
        isLoading={isLoading}
      />
      
      {/* Re-ingest Confirmation Dialog */}
      <ReIngestConfirmationDialog
        open={reIngestDialogOpen}
        onOpenChange={setReIngestDialogOpen}
        repository={bulkAction === "re-ingest" ? undefined : repository}
        selectedCount={bulkAction === "re-ingest" ? selectedRepositoryIds.length : undefined}
        onConfirm={() => {
          if (bulkAction === "re-ingest") {
            handleBulkOperation("re_ingest")
          } else {
            handleReIngest()
          }
        }}
        isLoading={isLoading}
      />
    </div>
  )
}
