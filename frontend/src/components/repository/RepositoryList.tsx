/**
 * Repository List Component
 * 
 * Displays a list of ingested repositories with metadata, search/filter capabilities,
 * and action buttons for each repository. Supports pagination and sorting.
 */

"use client"

import * as React from "react"
import { 
  Calendar, 
  ChevronDown, 
  ChevronUp, 
  ExternalLink, 
  Filter, 
  GitBranch, 
  MoreHorizontal, 
  RefreshCw, 
  Search, 
  Trash2 
} from "lucide-react"

import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

import { useRepositoryList } from "@/hooks/api"
import type { RepositoryMetadata, RepositoryStatus } from "@/types/api"

/**
 * Props for the RepositoryList component
 */
export interface RepositoryListProps {
  /**
   * Custom CSS class name
   */
  className?: string
  
  /**
   * Callback fired when repositories are selected
   */
  onSelectionChange?: (selectedIds: string[]) => void
  
  /**
   * Callback fired when a repository action is triggered
   */
  onRepositoryAction?: (action: string, repository: RepositoryMetadata) => void
  
  /**
   * Whether to show selection checkboxes
   */
  showSelection?: boolean
  
  /**
   * Whether to show the search and filter controls
   */
  showControls?: boolean
  
  /**
   * Initial page size
   */
  initialPageSize?: number
}

/**
 * Repository status badge component
 */
function RepositoryStatusBadge({ status }: { status: RepositoryStatus }) {
  const statusConfig = {
    pending: { variant: "secondary" as const, label: "Pending" },
    processing: { variant: "default" as const, label: "Processing" },
    completed: { variant: "success" as const, label: "Completed" },
    failed: { variant: "destructive" as const, label: "Failed" },
    cancelled: { variant: "outline" as const, label: "Cancelled" },
  }
  
  const config = statusConfig[status] || statusConfig.pending
  
  return (
    <Badge variant={config.variant} className="text-xs">
      {config.label}
    </Badge>
  )
}

/**
 * Repository actions dropdown menu
 */
function RepositoryActions({ 
  repository, 
  onAction 
}: { 
  repository: RepositoryMetadata
  onAction?: (action: string, repository: RepositoryMetadata) => void 
}) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <MoreHorizontal className="h-4 w-4" />
          <span className="sr-only">Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => onAction?.("view", repository)}>
          View Details
        </DropdownMenuItem>
        <DropdownMenuItem 
          onClick={() => window.open(repository.url.toString(), "_blank")}
        >
          <ExternalLink className="mr-2 h-4 w-4" />
          Open Repository
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem 
          onClick={() => onAction?.("re-ingest", repository)}
          disabled={repository.status === "processing"}
        >
          <RefreshCw className="mr-2 h-4 w-4" />
          Re-ingest
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem 
          onClick={() => onAction?.("delete", repository)}
          className="text-destructive"
          disabled={repository.status === "processing"}
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

/**
 * Repository List Component
 * 
 * @example
 * ```tsx
 * <RepositoryList
 *   onSelectionChange={(ids) => console.log('Selected:', ids)}
 *   onRepositoryAction={(action, repo) => console.log(action, repo)}
 *   showSelection={true}
 * />
 * ```
 */
export function RepositoryList({
  className,
  onSelectionChange,
  onRepositoryAction,
  showSelection = false,
  showControls = true,
  initialPageSize = 20,
}: RepositoryListProps) {
  const [page, setPage] = React.useState(1)
  const [pageSize, setPageSize] = React.useState(initialPageSize)
  const [searchQuery, setSearchQuery] = React.useState("")
  const [statusFilter, setStatusFilter] = React.useState<RepositoryStatus | "">("")
  const [sortBy, setSortBy] = React.useState("updated_at")
  const [sortOrder, setSortOrder] = React.useState<"asc" | "desc">("desc")
  const [selectedIds, setSelectedIds] = React.useState<string[]>([])
  
  // Fetch repositories with current filters
  const { data, isLoading, error, refetch } = useRepositoryList({
    page,
    page_size: pageSize,
    search_query: searchQuery || undefined,
    status_filter: statusFilter || undefined,
    sort_by: sortBy,
    sort_order: sortOrder,
  })
  
  // Handle search input changes with debouncing
  const debouncedSearch = React.useMemo(() => {
    const timeoutId = React.useRef<NodeJS.Timeout>()
    return (value: string) => {
      if (timeoutId.current) {
        clearTimeout(timeoutId.current)
      }
      timeoutId.current = setTimeout(() => {
        setSearchQuery(value)
        setPage(1) // Reset to first page when searching
      }, 300)
    }
  }, [])
  
  // Handle selection changes
  const handleSelectionChange = React.useCallback((repositoryId: string, checked: boolean) => {
    const newSelectedIds = checked
      ? [...selectedIds, repositoryId]
      : selectedIds.filter(id => id !== repositoryId)
    
    setSelectedIds(newSelectedIds)
    onSelectionChange?.(newSelectedIds)
  }, [selectedIds, onSelectionChange])
  
  // Handle select all
  const handleSelectAll = React.useCallback((checked: boolean) => {
    const newSelectedIds = checked ? (data?.repositories.map(repo => repo.id) || []) : []
    setSelectedIds(newSelectedIds)
    onSelectionChange?.(newSelectedIds)
  }, [data?.repositories, onSelectionChange])
  
  // Handle sorting
  const handleSort = React.useCallback((field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc")
    } else {
      setSortBy(field)
      setSortOrder("desc")
    }
    setPage(1) // Reset to first page when sorting
  }, [sortBy, sortOrder])
  
  // Format date for display
  const formatDate = React.useCallback((dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }, [])
  
  // Calculate pagination info
  const totalPages = data?.total_pages || 0
  const totalCount = data?.total_count || 0
  const startItem = (page - 1) * pageSize + 1
  const endItem = Math.min(page * pageSize, totalCount)
  
  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center">
            <p className="text-destructive">Failed to load repositories</p>
            <Button onClick={() => refetch()} variant="outline" className="mt-2">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }
  
  return (
    <div className={cn("space-y-4", className)}>
      {/* Search and Filter Controls */}
      {showControls && (
        <Card>
          <CardHeader className="pb-3">
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
              <div className="flex flex-1 items-center gap-2">
                <div className="relative flex-1 max-w-sm">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search repositories..."
                    onChange={(e) => debouncedSearch(e.target.value)}
                    className="pl-8"
                  />
                </div>
                <Select value={statusFilter} onValueChange={(value) => {
                  setStatusFilter(value as RepositoryStatus | "")
                  setPage(1)
                }}>
                  <SelectTrigger className="w-32">
                    <Filter className="mr-2 h-4 w-4" />
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Status</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="processing">Processing</SelectItem>
                    <SelectItem value="failed">Failed</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex items-center gap-2">
                <Button onClick={() => refetch()} variant="outline" size="sm">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Refresh
                </Button>
              </div>
            </div>
          </CardHeader>
        </Card>
      )}
      
      {/* Repository Table */}
      <Card>
        <CardContent className="p-0">
          {isLoading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-muted-foreground">Loading repositories...</p>
            </div>
          ) : !data?.repositories.length ? (
            <div className="p-8 text-center">
              <p className="text-muted-foreground">No repositories found</p>
              {searchQuery && (
                <p className="text-sm text-muted-foreground mt-1">
                  Try adjusting your search or filters
                </p>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  {showSelection && (
                    <TableHead className="w-12">
                      <Checkbox
                        checked={selectedIds.length === data.repositories.length}
                        onCheckedChange={handleSelectAll}
                        aria-label="Select all repositories"
                      />
                    </TableHead>
                  )}
                  <TableHead 
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort("name")}
                  >
                    <div className="flex items-center gap-1">
                      Repository
                      {sortBy === "name" && (
                        sortOrder === "asc" ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead>Branch</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort("updated_at")}
                  >
                    <div className="flex items-center gap-1">
                      Last Updated
                      {sortBy === "updated_at" && (
                        sortOrder === "asc" ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead className="text-right">Files</TableHead>
                  <TableHead className="w-12"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.repositories.map((repository) => (
                  <TableRow key={repository.id}>
                    {showSelection && (
                      <TableCell>
                        <Checkbox
                          checked={selectedIds.includes(repository.id)}
                          onCheckedChange={(checked) => handleSelectionChange(repository.id, !!checked)}
                          aria-label={`Select ${repository.name}`}
                        />
                      </TableCell>
                    )}
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <GitBranch className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <div className="font-medium">{repository.owner}/{repository.name}</div>
                          {repository.description && (
                            <div className="text-sm text-muted-foreground truncate max-w-xs">
                              {repository.description}
                            </div>
                          )}
                        </div>
                        {repository.is_private && (
                          <Badge variant="secondary" className="text-xs">
                            Private
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <code className="text-sm bg-muted px-1 py-0.5 rounded">
                        {repository.branch}
                      </code>
                    </TableCell>
                    <TableCell>
                      <RepositoryStatusBadge status={repository.status} />
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <Calendar className="h-3 w-3" />
                        {formatDate(repository.updated_at)}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="text-sm">
                        {repository.processed_files.toLocaleString()}
                      </div>
                    </TableCell>
                    <TableCell>
                      <RepositoryActions 
                        repository={repository} 
                        onAction={onRepositoryAction}
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
      
      {/* Pagination */}
      {data && data.total_pages > 1 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                Showing {startItem}-{endItem} of {totalCount} repositories
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(page - 1)}
                  disabled={page <= 1}
                >
                  Previous
                </Button>
                
                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const pageNum = Math.max(1, Math.min(totalPages - 4, page - 2)) + i
                    return (
                      <Button
                        key={pageNum}
                        variant={pageNum === page ? "default" : "outline"}
                        size="sm"
                        onClick={() => setPage(pageNum)}
                        className="w-8 h-8 p-0"
                      >
                        {pageNum}
                      </Button>
                    )
                  })}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(page + 1)}
                  disabled={page >= totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
