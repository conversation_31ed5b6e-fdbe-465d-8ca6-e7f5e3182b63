/**
 * Ingestion Progress Component Tests
 * 
 * Comprehensive tests for the IngestionProgress component including
 * progress display, real-time updates, error handling, and retry functionality.
 */

import { describe, it, expect, vi, beforeEach, afterEach } from "vitest"
import { render, screen, userEvent, waitFor } from "@/test/utils"
import { IngestionProgress } from "../IngestionProgress"
import * as apiHooks from "@/hooks/api"
import type { IngestionProgress as IngestionProgressType } from "@/types/api"

// Mock the API hooks
vi.mock("@/hooks/api", () => ({
  useIngestionProgress: vi.fn(),
}))

// Mock the toast hook
vi.mock("@/hooks/useToast", () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}))

describe("IngestionProgress", () => {
  const mockProgressData: IngestionProgressType = {
    repository_id: "repo-123",
    status: "processing",
    stage: "chunking_content",
    current_step: "Processing TypeScript files",
    progress_percentage: 65.5,
    elapsed_time: 120,
    estimated_completion: new Date(Date.now() + 60000).toISOString(),
    files_discovered: 100,
    files_processed: 65,
    files_filtered: 5,
    chunks_created: 250,
    embeddings_generated: 200,
    errors: ["Failed to process file1.ts", "Timeout on file2.js"],
    warnings: ["Large file detected: bigfile.json"],
  }

  const mockUseIngestionProgress = vi.mocked(apiHooks.useIngestionProgress)

  beforeEach(() => {
    vi.clearAllMocks()
    
    // Default successful mock
    mockUseIngestionProgress.mockReturnValue({
      data: mockProgressData,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    } as any)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe("Rendering", () => {
    it("should render progress information", () => {
      render(<IngestionProgress repositoryId="repo-123" />)
      
      expect(screen.getByText("Ingestion Progress")).toBeInTheDocument()
      expect(screen.getByText("Processing")).toBeInTheDocument()
      expect(screen.getByText("Chunking Content")).toBeInTheDocument()
      expect(screen.getByText("Processing TypeScript files")).toBeInTheDocument()
    })

    it("should display progress percentage", () => {
      render(<IngestionProgress repositoryId="repo-123" />)
      
      expect(screen.getByText("66%")).toBeInTheDocument() // Rounded from 65.5%
    })

    it("should show elapsed time", () => {
      render(<IngestionProgress repositoryId="repo-123" />)
      
      expect(screen.getByText("Elapsed: 2m 0s")).toBeInTheDocument()
    })

    it("should display file statistics", () => {
      render(<IngestionProgress repositoryId="repo-123" showDetails={true} />)
      
      expect(screen.getByText("65 / 100 processed")).toBeInTheDocument()
      expect(screen.getByText("5 filtered")).toBeInTheDocument()
      expect(screen.getByText("250 chunks")).toBeInTheDocument()
      expect(screen.getByText("200 embeddings")).toBeInTheDocument()
    })

    it("should show loading state", () => {
      mockUseIngestionProgress.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
        refetch: vi.fn(),
      } as any)

      render(<IngestionProgress repositoryId="repo-123" />)
      
      expect(screen.getByText("Loading progress...")).toBeInTheDocument()
    })

    it("should show error state", () => {
      const mockRefetch = vi.fn()
      mockUseIngestionProgress.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: new Error("Failed to load"),
        refetch: mockRefetch,
      } as any)

      render(<IngestionProgress repositoryId="repo-123" />)
      
      expect(screen.getByText("Failed to load progress information.")).toBeInTheDocument()
      expect(screen.getByText("Retry")).toBeInTheDocument()
    })

    it("should show no data state", () => {
      mockUseIngestionProgress.mockReturnValue({
        data: null,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as any)

      render(<IngestionProgress repositoryId="repo-123" />)
      
      expect(screen.getByText("No progress information available")).toBeInTheDocument()
    })
  })

  describe("Progress States", () => {
    it("should show completed state", () => {
      const completedProgress = {
        ...mockProgressData,
        status: "completed" as const,
        stage: "completed" as const,
        progress_percentage: 100,
      }

      mockUseIngestionProgress.mockReturnValue({
        data: completedProgress,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as any)

      render(<IngestionProgress repositoryId="repo-123" />)
      
      expect(screen.getByText("Completed")).toBeInTheDocument()
      expect(screen.getByText(/Repository ingestion completed successfully/)).toBeInTheDocument()
    })

    it("should show failed state with retry button", () => {
      const failedProgress = {
        ...mockProgressData,
        status: "failed" as const,
        errors: ["Critical error occurred"],
      }

      mockUseIngestionProgress.mockReturnValue({
        data: failedProgress,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as any)

      const mockOnRetry = vi.fn()
      render(
        <IngestionProgress 
          repositoryId="repo-123" 
          onRetry={mockOnRetry}
        />
      )
      
      expect(screen.getByText("Failed")).toBeInTheDocument()
      expect(screen.getByText("Retry Ingestion")).toBeInTheDocument()
    })

    it("should call onComplete when status is completed", () => {
      const mockOnComplete = vi.fn()
      const completedProgress = {
        ...mockProgressData,
        status: "completed" as const,
      }

      mockUseIngestionProgress.mockReturnValue({
        data: completedProgress,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as any)

      render(
        <IngestionProgress 
          repositoryId="repo-123" 
          onComplete={mockOnComplete}
        />
      )
      
      expect(mockOnComplete).toHaveBeenCalledWith("repo-123")
    })
  })

  describe("Error and Warning Display", () => {
    it("should display errors", () => {
      render(<IngestionProgress repositoryId="repo-123" />)
      
      expect(screen.getByText("Errors (2)")).toBeInTheDocument()
      expect(screen.getByText("Failed to process file1.ts")).toBeInTheDocument()
      expect(screen.getByText("Timeout on file2.js")).toBeInTheDocument()
    })

    it("should display warnings", () => {
      render(<IngestionProgress repositoryId="repo-123" />)
      
      expect(screen.getByText("Warnings (1)")).toBeInTheDocument()
      expect(screen.getByText("Large file detected: bigfile.json")).toBeInTheDocument()
    })

    it("should limit displayed errors and warnings", () => {
      const progressWithManyErrors = {
        ...mockProgressData,
        errors: Array.from({ length: 10 }, (_, i) => `Error ${i + 1}`),
        warnings: Array.from({ length: 10 }, (_, i) => `Warning ${i + 1}`),
      }

      mockUseIngestionProgress.mockReturnValue({
        data: progressWithManyErrors,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as any)

      render(<IngestionProgress repositoryId="repo-123" />)
      
      expect(screen.getByText("... and 7 more")).toBeInTheDocument() // For errors
      expect(screen.getByText("... and 8 more")).toBeInTheDocument() // For warnings
    })
  })

  describe("Stage Icons and Animation", () => {
    it("should show spinning icon for processing stages", () => {
      render(<IngestionProgress repositoryId="repo-123" />)
      
      const stageIcon = screen.getByText("Chunking Content").previousElementSibling
      expect(stageIcon).toHaveClass("animate-spin")
    })

    it("should show appropriate icons for different stages", () => {
      const stages = [
        { stage: "initializing", expectedIcon: "Loader2" },
        { stage: "cloning", expectedIcon: "Loader2" },
        { stage: "scanning_files", expectedIcon: "FileText" },
        { stage: "completed", expectedIcon: "CheckCircle" },
      ]

      stages.forEach(({ stage }) => {
        const stageProgress = {
          ...mockProgressData,
          stage: stage as any,
        }

        mockUseIngestionProgress.mockReturnValue({
          data: stageProgress,
          isLoading: false,
          error: null,
          refetch: vi.fn(),
        } as any)

        const { unmount } = render(<IngestionProgress repositoryId="repo-123" />)
        
        // Icon should be present (specific icon testing would require more complex setup)
        expect(screen.getByText("Ingestion Progress")).toBeInTheDocument()
        
        unmount()
      })
    })
  })

  describe("Auto-refresh and Polling", () => {
    it("should enable auto-refresh by default", () => {
      render(<IngestionProgress repositoryId="repo-123" />)
      
      expect(mockUseIngestionProgress).toHaveBeenCalledWith(
        "repo-123",
        expect.objectContaining({
          enabled: true,
          refetchInterval: expect.any(Function),
        })
      )
    })

    it("should disable auto-refresh when autoRefresh is false", () => {
      render(<IngestionProgress repositoryId="repo-123" autoRefresh={false} />)
      
      expect(mockUseIngestionProgress).toHaveBeenCalledWith(
        "repo-123",
        expect.objectContaining({
          enabled: false,
        })
      )
    })

    it("should use custom refresh interval", () => {
      render(
        <IngestionProgress 
          repositoryId="repo-123" 
          refreshInterval={5000}
        />
      )
      
      expect(mockUseIngestionProgress).toHaveBeenCalledWith(
        "repo-123",
        expect.objectContaining({
          refetchInterval: expect.any(Function),
        })
      )
    })
  })

  describe("User Interactions", () => {
    it("should handle retry button click", async () => {
      const mockOnRetry = vi.fn()
      const failedProgress = {
        ...mockProgressData,
        status: "failed" as const,
      }

      mockUseIngestionProgress.mockReturnValue({
        data: failedProgress,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as any)

      const user = userEvent.setup()
      render(
        <IngestionProgress 
          repositoryId="repo-123" 
          onRetry={mockOnRetry}
        />
      )
      
      const retryButton = screen.getByText("Retry Ingestion")
      await user.click(retryButton)
      
      expect(mockOnRetry).toHaveBeenCalledWith("repo-123")
    })

    it("should handle refresh button click", async () => {
      const mockRefetch = vi.fn()
      mockUseIngestionProgress.mockReturnValue({
        data: mockProgressData,
        isLoading: false,
        error: null,
        refetch: mockRefetch,
      } as any)

      const user = userEvent.setup()
      render(<IngestionProgress repositoryId="repo-123" />)
      
      const refreshButton = screen.getByRole("button", { name: /refresh/i })
      await user.click(refreshButton)
      
      expect(mockRefetch).toHaveBeenCalled()
    })

    it("should handle error retry button click", async () => {
      const mockRefetch = vi.fn()
      mockUseIngestionProgress.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: new Error("Failed to load"),
        refetch: mockRefetch,
      } as any)

      const user = userEvent.setup()
      render(<IngestionProgress repositoryId="repo-123" />)
      
      const retryButton = screen.getByText("Retry")
      await user.click(retryButton)
      
      expect(mockRefetch).toHaveBeenCalled()
    })
  })

  describe("Time Formatting", () => {
    it("should format elapsed time correctly", () => {
      const timeTests = [
        { seconds: 30, expected: "30s" },
        { seconds: 90, expected: "1m 30s" },
        { seconds: 3661, expected: "1h 1m" },
      ]

      timeTests.forEach(({ seconds, expected }) => {
        const progressWithTime = {
          ...mockProgressData,
          elapsed_time: seconds,
        }

        mockUseIngestionProgress.mockReturnValue({
          data: progressWithTime,
          isLoading: false,
          error: null,
          refetch: vi.fn(),
        } as any)

        const { unmount } = render(<IngestionProgress repositoryId="repo-123" />)
        
        expect(screen.getByText(`Elapsed: ${expected}`)).toBeInTheDocument()
        
        unmount()
      })
    })

    it("should format estimated completion time", () => {
      const futureTime = new Date(Date.now() + 120000).toISOString() // 2 minutes from now
      const progressWithEstimate = {
        ...mockProgressData,
        estimated_completion: futureTime,
      }

      mockUseIngestionProgress.mockReturnValue({
        data: progressWithEstimate,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as any)

      render(<IngestionProgress repositoryId="repo-123" />)
      
      expect(screen.getByText(/~2m 0s remaining/)).toBeInTheDocument()
    })
  })

  describe("Accessibility", () => {
    it("should have proper ARIA labels", () => {
      render(<IngestionProgress repositoryId="repo-123" />)
      
      expect(screen.getByRole("progressbar")).toBeInTheDocument()
    })

    it("should support keyboard navigation", async () => {
      const mockRefetch = vi.fn()
      mockUseIngestionProgress.mockReturnValue({
        data: mockProgressData,
        isLoading: false,
        error: null,
        refetch: mockRefetch,
      } as any)

      const user = userEvent.setup()
      render(<IngestionProgress repositoryId="repo-123" />)
      
      const refreshButton = screen.getByRole("button", { name: /refresh/i })
      refreshButton.focus()
      
      await user.keyboard("{Enter}")
      
      expect(mockRefetch).toHaveBeenCalled()
    })
  })
})
