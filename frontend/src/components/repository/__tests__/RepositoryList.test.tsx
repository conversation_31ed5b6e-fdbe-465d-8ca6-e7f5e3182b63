/**
 * Repository List Component Tests
 * 
 * Comprehensive tests for the RepositoryList component including
 * rendering, user interactions, search/filter functionality, and error scenarios.
 */

import { describe, it, expect, vi, beforeEach, afterEach } from "vitest"
import { render, screen, userEvent, waitFor } from "@/test/utils"
import { RepositoryList } from "../RepositoryList"
import * as apiHooks from "@/hooks/api"
import type { RepositoryListResponse, RepositoryMetadata } from "@/types/api"

// Mock the API hooks
vi.mock("@/hooks/api", () => ({
  useRepositoryList: vi.fn(),
}))

// Mock the toast hook
vi.mock("@/hooks/useToast", () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}))

describe("RepositoryList", () => {
  const mockRepositories: RepositoryMetadata[] = [
    {
      id: "repo-1",
      url: new URL("https://github.com/test/repo1"),
      name: "repo1",
      owner: "test",
      branch: "main",
      commit_sha: "abc123",
      is_private: false,
      description: "Test repository 1",
      language: "TypeScript",
      size: 1024,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-02T00:00:00Z",
      ingested_at: "2024-01-02T00:00:00Z",
      status: "completed",
      processed_files: 50,
      chunks_created: 200,
      embeddings_generated: 200,
      processing_time: 120,
    },
    {
      id: "repo-2",
      url: new URL("https://github.com/test/repo2"),
      name: "repo2",
      owner: "test",
      branch: "develop",
      commit_sha: "def456",
      is_private: true,
      description: "Test repository 2",
      language: "Python",
      size: 2048,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-03T00:00:00Z",
      ingested_at: "2024-01-03T00:00:00Z",
      status: "processing",
      processed_files: 25,
      chunks_created: 100,
      embeddings_generated: 100,
      processing_time: 60,
    },
  ]

  const mockRepositoryListResponse: RepositoryListResponse = {
    repositories: mockRepositories,
    total_count: 2,
    page: 1,
    page_size: 20,
    total_pages: 1,
  }

  const mockUseRepositoryList = vi.mocked(apiHooks.useRepositoryList)

  beforeEach(() => {
    vi.clearAllMocks()
    
    // Default successful mock
    mockUseRepositoryList.mockReturnValue({
      data: mockRepositoryListResponse,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    } as any)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe("Rendering", () => {
    it("should render repository list with data", () => {
      render(<RepositoryList />)
      
      expect(screen.getByText("repo1")).toBeInTheDocument()
      expect(screen.getByText("repo2")).toBeInTheDocument()
      expect(screen.getByText("Test repository 1")).toBeInTheDocument()
      expect(screen.getByText("Test repository 2")).toBeInTheDocument()
    })

    it("should show repository status badges", () => {
      render(<RepositoryList />)
      
      expect(screen.getByText("Completed")).toBeInTheDocument()
      expect(screen.getByText("Processing")).toBeInTheDocument()
    })

    it("should show private repository badge", () => {
      render(<RepositoryList />)
      
      expect(screen.getByText("Private")).toBeInTheDocument()
    })

    it("should display file counts", () => {
      render(<RepositoryList />)
      
      expect(screen.getByText("50")).toBeInTheDocument()
      expect(screen.getByText("25")).toBeInTheDocument()
    })

    it("should show loading state", () => {
      mockUseRepositoryList.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
        refetch: vi.fn(),
      } as any)

      render(<RepositoryList />)
      
      expect(screen.getByText("Loading repositories...")).toBeInTheDocument()
      expect(screen.getByRole("progressbar")).toBeInTheDocument()
    })

    it("should show empty state when no repositories", () => {
      mockUseRepositoryList.mockReturnValue({
        data: { ...mockRepositoryListResponse, repositories: [], total_count: 0 },
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as any)

      render(<RepositoryList />)
      
      expect(screen.getByText("No repositories found")).toBeInTheDocument()
    })

    it("should show error state", () => {
      const mockRefetch = vi.fn()
      mockUseRepositoryList.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: new Error("Failed to load"),
        refetch: mockRefetch,
      } as any)

      render(<RepositoryList />)
      
      expect(screen.getByText("Failed to load repositories")).toBeInTheDocument()
      expect(screen.getByText("Try Again")).toBeInTheDocument()
    })
  })

  describe("Search and Filter", () => {
    it("should render search input", () => {
      render(<RepositoryList showControls={true} />)
      
      expect(screen.getByPlaceholderText("Search repositories...")).toBeInTheDocument()
    })

    it("should render status filter", () => {
      render(<RepositoryList showControls={true} />)
      
      expect(screen.getByText("Status")).toBeInTheDocument()
    })

    it("should handle search input", async () => {
      const user = userEvent.setup()
      render(<RepositoryList showControls={true} />)
      
      const searchInput = screen.getByPlaceholderText("Search repositories...")
      await user.type(searchInput, "test query")
      
      // Should debounce the search
      await waitFor(() => {
        expect(mockUseRepositoryList).toHaveBeenCalledWith(
          expect.objectContaining({
            search_query: "test query",
            page: 1,
          })
        )
      }, { timeout: 500 })
    })

    it("should handle status filter change", async () => {
      const user = userEvent.setup()
      render(<RepositoryList showControls={true} />)
      
      const statusFilter = screen.getByText("Status")
      await user.click(statusFilter)
      
      const completedOption = screen.getByText("Completed")
      await user.click(completedOption)
      
      expect(mockUseRepositoryList).toHaveBeenCalledWith(
        expect.objectContaining({
          status_filter: "completed",
          page: 1,
        })
      )
    })

    it("should handle refresh button", async () => {
      const mockRefetch = vi.fn()
      mockUseRepositoryList.mockReturnValue({
        data: mockRepositoryListResponse,
        isLoading: false,
        error: null,
        refetch: mockRefetch,
      } as any)

      const user = userEvent.setup()
      render(<RepositoryList showControls={true} />)
      
      const refreshButton = screen.getByText("Refresh")
      await user.click(refreshButton)
      
      expect(mockRefetch).toHaveBeenCalled()
    })
  })

  describe("Selection", () => {
    it("should show selection checkboxes when enabled", () => {
      render(<RepositoryList showSelection={true} />)
      
      const checkboxes = screen.getAllByRole("checkbox")
      expect(checkboxes).toHaveLength(3) // 2 repositories + select all
    })

    it("should handle individual repository selection", async () => {
      const mockOnSelectionChange = vi.fn()
      const user = userEvent.setup()
      
      render(
        <RepositoryList 
          showSelection={true} 
          onSelectionChange={mockOnSelectionChange}
        />
      )
      
      const firstCheckbox = screen.getAllByRole("checkbox")[1] // Skip select all
      await user.click(firstCheckbox)
      
      expect(mockOnSelectionChange).toHaveBeenCalledWith(["repo-1"])
    })

    it("should handle select all", async () => {
      const mockOnSelectionChange = vi.fn()
      const user = userEvent.setup()
      
      render(
        <RepositoryList 
          showSelection={true} 
          onSelectionChange={mockOnSelectionChange}
        />
      )
      
      const selectAllCheckbox = screen.getAllByRole("checkbox")[0]
      await user.click(selectAllCheckbox)
      
      expect(mockOnSelectionChange).toHaveBeenCalledWith(["repo-1", "repo-2"])
    })
  })

  describe("Sorting", () => {
    it("should handle column sorting", async () => {
      const user = userEvent.setup()
      render(<RepositoryList />)
      
      const nameHeader = screen.getByText("Repository")
      await user.click(nameHeader)
      
      expect(mockUseRepositoryList).toHaveBeenCalledWith(
        expect.objectContaining({
          sort_by: "name",
          sort_order: "desc",
          page: 1,
        })
      )
    })

    it("should toggle sort order on repeated clicks", async () => {
      const user = userEvent.setup()
      render(<RepositoryList />)
      
      const nameHeader = screen.getByText("Repository")
      
      // First click - desc
      await user.click(nameHeader)
      expect(mockUseRepositoryList).toHaveBeenCalledWith(
        expect.objectContaining({
          sort_by: "name",
          sort_order: "desc",
        })
      )
      
      // Second click - asc
      await user.click(nameHeader)
      expect(mockUseRepositoryList).toHaveBeenCalledWith(
        expect.objectContaining({
          sort_by: "name",
          sort_order: "asc",
        })
      )
    })
  })

  describe("Repository Actions", () => {
    it("should show repository action menu", async () => {
      const user = userEvent.setup()
      render(<RepositoryList />)
      
      const actionButtons = screen.getAllByLabelText("Open menu")
      await user.click(actionButtons[0])
      
      expect(screen.getByText("View Details")).toBeInTheDocument()
      expect(screen.getByText("Open Repository")).toBeInTheDocument()
      expect(screen.getByText("Re-ingest")).toBeInTheDocument()
      expect(screen.getByText("Delete")).toBeInTheDocument()
    })

    it("should call onRepositoryAction when action is clicked", async () => {
      const mockOnRepositoryAction = vi.fn()
      const user = userEvent.setup()
      
      render(<RepositoryList onRepositoryAction={mockOnRepositoryAction} />)
      
      const actionButtons = screen.getAllByLabelText("Open menu")
      await user.click(actionButtons[0])
      
      const viewDetailsButton = screen.getByText("View Details")
      await user.click(viewDetailsButton)
      
      expect(mockOnRepositoryAction).toHaveBeenCalledWith("view", mockRepositories[0])
    })

    it("should disable actions for processing repositories", async () => {
      const user = userEvent.setup()
      render(<RepositoryList />)
      
      const actionButtons = screen.getAllByLabelText("Open menu")
      await user.click(actionButtons[1]) // Second repository (processing)
      
      const reIngestButton = screen.getByText("Re-ingest")
      const deleteButton = screen.getByText("Delete")
      
      expect(reIngestButton).toBeDisabled()
      expect(deleteButton).toBeDisabled()
    })
  })

  describe("Pagination", () => {
    it("should show pagination when multiple pages", () => {
      mockUseRepositoryList.mockReturnValue({
        data: { ...mockRepositoryListResponse, total_pages: 3, page: 1 },
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as any)

      render(<RepositoryList />)
      
      expect(screen.getByText("Previous")).toBeInTheDocument()
      expect(screen.getByText("Next")).toBeInTheDocument()
      expect(screen.getByText("1")).toBeInTheDocument()
    })

    it("should handle page navigation", async () => {
      mockUseRepositoryList.mockReturnValue({
        data: { ...mockRepositoryListResponse, total_pages: 3, page: 1 },
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as any)

      const user = userEvent.setup()
      render(<RepositoryList />)
      
      const nextButton = screen.getByText("Next")
      await user.click(nextButton)
      
      expect(mockUseRepositoryList).toHaveBeenCalledWith(
        expect.objectContaining({
          page: 2,
        })
      )
    })

    it("should show pagination info", () => {
      render(<RepositoryList />)
      
      expect(screen.getByText("Showing 1-2 of 2 repositories")).toBeInTheDocument()
    })
  })

  describe("Accessibility", () => {
    it("should have proper ARIA labels", () => {
      render(<RepositoryList showSelection={true} />)
      
      expect(screen.getByLabelText("Select all repositories")).toBeInTheDocument()
      expect(screen.getByLabelText("Select repo1")).toBeInTheDocument()
      expect(screen.getByLabelText("Select repo2")).toBeInTheDocument()
    })

    it("should support keyboard navigation", async () => {
      const user = userEvent.setup()
      render(<RepositoryList />)
      
      const firstActionButton = screen.getAllByLabelText("Open menu")[0]
      firstActionButton.focus()
      
      await user.keyboard("{Enter}")
      
      expect(screen.getByText("View Details")).toBeInTheDocument()
    })
  })
})
