# Repository Management Components

## Overview

This directory contains all components related to repository management functionality in the LLM RAG Codebase Query System. These components provide a comprehensive interface for managing GitHub repository ingestion, tracking progress, and performing bulk operations.

## Quick Start

```tsx
import { 
  RepositoryList, 
  IngestionProgress, 
  RepositoryActions, 
  BatchOperations 
} from "@/components/repository"

// Basic repository list
<RepositoryList 
  showSelection={true}
  onSelectionChange={(ids) => setSelectedIds(ids)}
/>

// Progress tracking
<IngestionProgress 
  repositoryId="repo-123"
  onComplete={(id) => toast.success(`${id} completed!`)}
/>

// Bulk operations
<BatchOperations 
  selectedRepositories={selectedRepos}
  onOperationComplete={(op, results) => handleResults(op, results)}
/>
```

## Components

### Core Components

| Component | Purpose | Key Features |
|-----------|---------|--------------|
| `RepositoryList` | Display repositories with search/filter | Pagination, sorting, bulk selection |
| `IngestionProgress` | Real-time progress tracking | Live updates, error reporting, retry |
| `RepositoryActions` | Individual repository operations | View, re-ingest, delete with confirmations |
| `BatchOperations` | Bulk repository management | Multi-repo operations with progress |

### Supporting Components

| Component | Purpose | Location |
|-----------|---------|----------|
| `RepositoryErrorBoundary` | Error handling | `@/components/error` |
| `RepositorySelector` | Repository input form | `@/components/query` |

## File Structure

```
src/components/repository/
├── RepositoryList.tsx          # Main repository listing
├── IngestionProgress.tsx       # Progress tracking component
├── RepositoryActions.tsx       # Individual operations
├── BatchOperations.tsx         # Bulk operations
├── index.ts                    # Barrel exports
├── README.md                   # This file
└── __tests__/                  # Test files
    ├── RepositoryList.test.tsx
    ├── IngestionProgress.test.tsx
    ├── RepositoryActions.test.tsx
    └── BatchOperations.test.tsx
```

## Features

### ✅ Repository Management
- List repositories with metadata
- Search and filter capabilities
- Sortable columns with pagination
- Bulk selection and operations

### ✅ Real-time Progress Tracking
- Live ingestion progress updates
- Detailed stage information
- Error and warning reporting
- Automatic retry mechanisms

### ✅ Comprehensive Actions
- Individual repository operations
- Bulk operations with progress tracking
- Confirmation dialogs with force options
- External GitHub integration

### ✅ Error Handling
- Repository-specific error types
- User-friendly error messages
- Automatic retry with exponential backoff
- Error boundaries for graceful failures

### ✅ Accessibility
- Full keyboard navigation
- ARIA labels and descriptions
- Screen reader support
- High contrast mode compatibility

## Usage Examples

### Basic Repository List

```tsx
function RepositoryPage() {
  const [selectedIds, setSelectedIds] = useState<string[]>([])
  
  return (
    <RepositoryList
      showSelection={true}
      showControls={true}
      onSelectionChange={setSelectedIds}
      onRepositoryAction={(action, repo) => {
        console.log(`${action} on ${repo.name}`)
      }}
    />
  )
}
```

### Progress Monitoring

```tsx
function ProgressPage() {
  const activeIngestions = useActiveIngestions()
  
  return (
    <div className="space-y-4">
      {activeIngestions.data?.map((progress) => (
        <IngestionProgress
          key={progress.repository_id}
          repositoryId={progress.repository_id}
          showDetails={true}
          onComplete={(id) => {
            toast.success(`Repository ${id} completed!`)
          }}
          onRetry={(id) => {
            // Handle retry logic
          }}
        />
      ))}
    </div>
  )
}
```

### Bulk Operations

```tsx
function BulkActionsPage() {
  const [selectedRepos, setSelectedRepos] = useState<RepositoryMetadata[]>([])
  
  return (
    <BatchOperations
      selectedRepositories={selectedRepos}
      onOperationComplete={(operation, results) => {
        toast.success(
          `${operation} completed: ${results.completed} successful, ${results.failed} failed`
        )
        setSelectedRepos([]) // Clear selection
      }}
      onClearSelection={() => setSelectedRepos([])}
    />
  )
}
```

### Error Handling

```tsx
function App() {
  return (
    <RepositoryErrorBoundary
      maxRetries={3}
      showDetails={process.env.NODE_ENV === 'development'}
      onError={(error, errorInfo) => {
        // Log to monitoring service
        console.error('Repository error:', error)
      }}
    >
      <RepositoryManagementPage />
    </RepositoryErrorBoundary>
  )
}
```

## API Integration

### Required Hooks

```tsx
import {
  useRepositoryList,
  useRepositoryDetails,
  useRepositoryDelete,
  useRepositoryReIngest,
  useBatchOperation,
  useRepositoryStats,
  useIngestionProgress,
} from "@/hooks/api"
```

### Error Handling

```tsx
import { useRepositoryErrorHandler } from "@/components/error"

const { handleError, createRetryFunction } = useRepositoryErrorHandler()
```

## Testing

### Running Tests

```bash
# Run all repository component tests
pnpm test:repository:components

# Run specific component tests
pnpm test src/components/repository/__tests__/RepositoryList.test.tsx

# Run with coverage
pnpm test:coverage --include="src/components/repository/**"
```

### Test Utilities

```tsx
import { 
  repositoryTestUtils, 
  mockRepositoryData,
  repositoryTestScenarios 
} from "@/test/repository/repository.test.config"

// Create mock data
const mockRepo = repositoryTestUtils.createMockRepository({
  name: "test-repo",
  status: "completed"
})

// Use test scenarios
const { success, loading, error } = repositoryTestScenarios
```

## Styling

### Tailwind Classes

Components use Tailwind CSS for styling:

```tsx
<RepositoryList className="border rounded-lg shadow-sm" />
<IngestionProgress className="bg-white dark:bg-gray-800" />
```

### Custom Styling

Override default styles with CSS modules or styled-components:

```css
.custom-repository-list {
  @apply border-2 border-blue-500 rounded-xl;
}

.custom-progress-bar {
  @apply bg-gradient-to-r from-blue-500 to-purple-600;
}
```

## Performance

### Optimization Features

- **Debounced Search**: 300ms debounce on search inputs
- **Pagination**: Configurable page sizes (default: 20)
- **Memoization**: React.memo on expensive components
- **Caching**: API responses cached appropriately
- **Polling**: Smart polling that stops when not needed

### Best Practices

```tsx
// Memoize expensive calculations
const sortedRepositories = useMemo(() => {
  return repositories.sort((a, b) => a.name.localeCompare(b.name))
}, [repositories])

// Debounce user inputs
const debouncedSearch = useDebounce(searchQuery, 300)

// Use pagination for large datasets
<RepositoryList initialPageSize={50} />
```

## Accessibility

### ARIA Support

- All interactive elements have proper ARIA labels
- Tables include headers and descriptions
- Progress bars announce progress to screen readers
- Error messages are announced when they appear

### Keyboard Navigation

- Tab order is logical and intuitive
- All functionality accessible via keyboard
- Focus indicators are clearly visible
- Escape key closes dialogs and menus

### Testing Accessibility

```bash
# Run accessibility tests
pnpm test src/test/accessibility/repository-a11y.test.tsx

# Manual testing with screen readers
# - NVDA (Windows)
# - JAWS (Windows)
# - VoiceOver (macOS)
```

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Contributing

### Development Setup

1. Install dependencies: `pnpm install`
2. Start development server: `pnpm dev`
3. Run tests: `pnpm test:repository`
4. Check types: `pnpm type-check`
5. Lint code: `pnpm lint`

### Adding New Components

1. Create component file in this directory
2. Add comprehensive TypeScript types
3. Include proper error handling
4. Write comprehensive tests
5. Update index.ts exports
6. Add documentation

### Code Standards

- Follow existing patterns and conventions
- Use TypeScript for all new code
- Include comprehensive error handling
- Write tests for all functionality
- Follow accessibility guidelines
- Document all public APIs

## Troubleshooting

### Common Issues

**Component not rendering**
- Check if all required props are provided
- Verify API hooks are properly mocked in tests
- Ensure error boundaries are in place

**Performance issues**
- Check for unnecessary re-renders
- Verify memoization is working
- Consider pagination for large datasets

**Accessibility issues**
- Test with keyboard navigation
- Verify ARIA labels are present
- Check color contrast ratios

### Getting Help

1. Check the documentation in `/docs`
2. Review test files for usage examples
3. Check the console for error messages
4. Contact the development team

## Related Documentation

- [Repository Management Guide](../../../docs/repository-management.md)
- [API Reference](../../../docs/repository-api.md)
- [Component Reference](../../../docs/repository-components.md)
- [Testing Guide](../../../docs/repository-testing.md)
