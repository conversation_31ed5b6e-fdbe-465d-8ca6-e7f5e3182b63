"use client"

import * as React from "react"
import { 
  <PERSON><PERSON>, 
  <PERSON>, 
  <PERSON><PERSON>, 
  <PERSON><PERSON>, 
  <PERSON>hum<PERSON><PERSON>p, 
  ThumbsDown, 
  MoreVertical,
  AlertCircle,
  TrendingUp,
  Database
} from "lucide-react"

import { Card, CardContent, CardHeader, CardTitle, CardAction } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Skeleton } from "@/components/ui/skeleton"

import { useCopyToClipboard } from "@/hooks/useCopyToClipboard"
import { useToast } from "@/hooks/useToast"
import { cn } from "@/lib/utils"

import type { AgentResponseCardProps, ParsedSourceCitation } from "./types"
import { MarkdownRenderer } from "./MarkdownRenderer"
import { SourceCitationDisplay } from "./SourceCitationDisplay"

/**
 * Get agent display information
 */
function getAgentInfo(agentType: string) {
  switch (agentType) {
    case "TECHNICAL_ARCHITECT":
      return { 
        icon: Zap, 
        color: "text-blue-500", 
        bgColor: "bg-blue-50 dark:bg-blue-950", 
        label: "Technical Architect",
        description: "Architecture analysis and design recommendations"
      }
    case "TASK_PLANNER":
      return { 
        icon: Clock, 
        color: "text-green-500", 
        bgColor: "bg-green-50 dark:bg-green-950", 
        label: "Task Planner",
        description: "Task breakdown and planning guidance"
      }
    case "RAG_RETRIEVAL":
      return { 
        icon: Database, 
        color: "text-purple-500", 
        bgColor: "bg-purple-50 dark:bg-purple-950", 
        label: "RAG Retrieval",
        description: "Information retrieval and synthesis"
      }
    case "ORCHESTRATOR":
      return { 
        icon: Bot, 
        color: "text-orange-500", 
        bgColor: "bg-orange-50 dark:bg-orange-950", 
        label: "Orchestrator",
        description: "Multi-agent coordination and routing"
      }
    default:
      return { 
        icon: Bot, 
        color: "text-muted-foreground", 
        bgColor: "bg-muted/50", 
        label: "Assistant",
        description: "AI assistant response"
      }
  }
}

/**
 * Structured data display component
 */
interface StructuredDataDisplayProps {
  data: Record<string, unknown>
  className?: string
}

function StructuredDataDisplay({ data, className }: StructuredDataDisplayProps) {
  const [isExpanded, setIsExpanded] = React.useState(false)
  
  if (!data || Object.keys(data).length === 0) {
    return null
  }

  const entries = Object.entries(data)
  const visibleEntries = isExpanded ? entries : entries.slice(0, 3)
  const hasMore = entries.length > 3

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium text-muted-foreground">
          Structured Data
        </span>
        {hasMore && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="h-auto p-1 text-xs"
          >
            {isExpanded ? "Show less" : `Show ${entries.length - 3} more`}
          </Button>
        )}
      </div>
      
      <Collapsible open={isExpanded || !hasMore}>
        <div className="grid gap-2">
          {visibleEntries.map(([key, value]) => (
            <div key={key} className="flex items-center justify-between text-sm">
              <span className="font-medium text-muted-foreground">{key}:</span>
              <span className="font-mono text-xs bg-muted px-2 py-1 rounded">
                {typeof value === "object" ? JSON.stringify(value) : String(value)}
              </span>
            </div>
          ))}
        </div>
        
        {hasMore && (
          <CollapsibleContent>
            <div className="grid gap-2 mt-2">
              {entries.slice(3).map(([key, value]) => (
                <div key={key} className="flex items-center justify-between text-sm">
                  <span className="font-medium text-muted-foreground">{key}:</span>
                  <span className="font-mono text-xs bg-muted px-2 py-1 rounded">
                    {typeof value === "object" ? JSON.stringify(value) : String(value)}
                  </span>
                </div>
              ))}
            </div>
          </CollapsibleContent>
        )}
      </Collapsible>
    </div>
  )
}

/**
 * AgentResponseCard Component
 * 
 * Comprehensive component for displaying agent responses with:
 * - Agent metadata and branding
 * - Markdown content rendering
 * - Source citation display
 * - Structured data presentation
 * - Performance metrics
 * - User interaction (copy, rate)
 * - Responsive design and accessibility
 * 
 * @example
 * ```tsx
 * <AgentResponseCard
 *   content="# Analysis Results\n\nThe system architecture..."
 *   metadata={{
 *     agentType: "TECHNICAL_ARCHITECT",
 *     confidence: 0.95,
 *     processingTime: 2.3
 *   }}
 *   sources={["src/main.py:10-20", "docs/architecture.md"]}
 *   structured={{ complexity: "medium", patterns: ["singleton", "factory"] }}
 *   onSourceClick={(citation) => console.log('Navigate to:', citation)}
 * />
 * ```
 */
export function AgentResponseCard({
  content,
  metadata,
  sources = [],
  structured,
  isLoading = false,
  error = null,
  className,
  markdownOptions = {},
  citationOptions = {},
  showMetadata = true,
  showSources = true,
  showStructured = true,
  onSourceClick,
  onCopy,
  onRate,
}: AgentResponseCardProps) {
  const { toast } = useToast()
  const { copyToClipboard } = useCopyToClipboard()
  
  const agentInfo = getAgentInfo(metadata.agentType)
  const AgentIcon = agentInfo.icon

  // Handle copy to clipboard
  const handleCopy = React.useCallback(async () => {
    try {
      await copyToClipboard(content)
      toast({
        title: "Response copied",
        description: "Agent response has been copied to clipboard",
      })
      onCopy?.(content)
    } catch (err) {
      toast({
        title: "Failed to copy",
        description: "Could not copy response content",
        variant: "destructive",
      })
    }
  }, [content, copyToClipboard, toast, onCopy])

  // Handle rating
  const handleRate = React.useCallback((rating: "up" | "down") => {
    toast({
      title: `Response rated ${rating === "up" ? "positively" : "negatively"}`,
      description: "Thank you for your feedback",
    })
    onRate?.(rating)
  }, [toast, onRate])

  // Loading state
  if (isLoading) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <div className="flex items-center gap-3">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-3 w-24" />
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
          <Skeleton className="h-20 w-full" />
          <Skeleton className="h-4 w-2/3" />
        </CardContent>
      </Card>
    )
  }

  // Error state
  if (error) {
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="p-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error}
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn("w-full group", className)}>
      <CardHeader className={cn("pb-4", agentInfo.bgColor)}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {/* Agent Avatar */}
            <div className={cn(
              "flex items-center justify-center w-10 h-10 rounded-full bg-background shadow-sm",
            )}>
              <AgentIcon className={cn("w-5 h-5", agentInfo.color)} />
            </div>
            
            {/* Agent Info */}
            <div className="space-y-1">
              <CardTitle className="text-lg font-semibold">
                {agentInfo.label}
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                {agentInfo.description}
              </p>
            </div>
          </div>

          {/* Actions */}
          <CardAction>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
                  aria-label="Response actions"
                >
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleCopy}>
                  <Copy className="mr-2 h-4 w-4" />
                  Copy response
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => handleRate("up")}>
                  <ThumbsUp className="mr-2 h-4 w-4" />
                  Helpful
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleRate("down")}>
                  <ThumbsDown className="mr-2 h-4 w-4" />
                  Not helpful
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </CardAction>
        </div>

        {/* Metadata */}
        {showMetadata && (
          <div className="flex flex-wrap items-center gap-4 pt-3 text-xs text-muted-foreground">
            {metadata.confidence !== undefined && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center gap-1">
                      <TrendingUp className="w-3 h-3" />
                      <span>Confidence:</span>
                      <Badge variant="outline" className="text-xs">
                        {Math.round(metadata.confidence * 100)}%
                      </Badge>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Agent confidence in response accuracy</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
            
            {metadata.processingTime !== undefined && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      <span>{metadata.processingTime.toFixed(2)}s</span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Response processing time</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
            
            {sources.length > 0 && (
              <div className="flex items-center gap-1">
                <span>Sources:</span>
                <Badge variant="secondary" className="text-xs">
                  {sources.length}
                </Badge>
              </div>
            )}
          </div>
        )}
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Main Content */}
        <MarkdownRenderer
          content={content}
          options={markdownOptions}
          className="min-h-0"
        />

        {/* Source Citations */}
        {showSources && sources.length > 0 && (
          <>
            <Separator />
            <SourceCitationDisplay
              sources={sources}
              options={citationOptions}
              onSourceClick={onSourceClick}
            />
          </>
        )}

        {/* Structured Data */}
        {showStructured && structured && Object.keys(structured).length > 0 && (
          <>
            <Separator />
            <StructuredDataDisplay data={structured} />
          </>
        )}
      </CardContent>
    </Card>
  )
}
