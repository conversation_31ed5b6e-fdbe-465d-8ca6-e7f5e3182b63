/**
 * Response Display Component Types
 * 
 * TypeScript interfaces and types for the response display system components.
 * These types ensure type safety and consistency across all response display components.
 */

import type { ReactNode } from "react"
import type { 
  SourceCitation, 
  ConfidenceScore, 
  ProcessingTime, 
  StructuredData,
  AgentType 
} from "@/types/api"

/**
 * Parsed source citation with structured data
 * Represents a parsed version of the string format "file_path:start_line-end_line"
 */
export interface ParsedSourceCitation {
  /** Original citation string */
  raw: string
  /** File path */
  filePath: string
  /** Starting line number (1-based, optional) */
  startLine?: number
  /** Ending line number (1-based, optional) */
  endLine?: number
  /** Whether the citation format is valid */
  isValid: boolean
  /** Error message if parsing failed */
  error?: string
}

/**
 * Agent metadata for response display
 */
export interface AgentMetadata {
  /** Type of agent that generated the response */
  agentType: string
  /** Confidence score (0.0 to 1.0) */
  confidence?: ConfidenceScore
  /** Processing time in seconds */
  processingTime?: ProcessingTime
  /** Additional metadata */
  metadata?: Record<string, unknown>
}

/**
 * Markdown rendering options
 */
export interface MarkdownRenderingOptions {
  /** Enable syntax highlighting for code blocks */
  enableSyntaxHighlighting?: boolean
  /** Enable GitHub Flavored Markdown features */
  enableGfm?: boolean
  /** Custom CSS classes for the container */
  className?: string
  /** Enable XSS protection (recommended: true) */
  enableXssProtection?: boolean
  /** Custom component overrides for markdown elements */
  components?: Record<string, React.ComponentType<any>>
}

/**
 * Source citation display options
 */
export interface SourceCitationDisplayOptions {
  /** Maximum number of citations to show initially */
  maxVisible?: number
  /** Show file type indicators */
  showFileTypes?: boolean
  /** Show line numbers in display */
  showLineNumbers?: boolean
  /** Enable click-to-navigate functionality */
  enableNavigation?: boolean
  /** Custom CSS classes */
  className?: string
}

/**
 * Props for the MarkdownRenderer component
 */
export interface MarkdownRendererProps {
  /** Markdown content to render */
  content: string
  /** Rendering options */
  options?: MarkdownRenderingOptions
  /** Loading state */
  isLoading?: boolean
  /** Error state */
  error?: string | null
  /** Custom CSS class name */
  className?: string
  /** Callback fired when content is rendered */
  onRender?: () => void
  /** Callback fired when rendering fails */
  onError?: (error: Error) => void
}

/**
 * Props for the SourceCitationDisplay component
 */
export interface SourceCitationDisplayProps {
  /** Array of source citations */
  sources: SourceCitation[]
  /** Display options */
  options?: SourceCitationDisplayOptions
  /** Loading state */
  isLoading?: boolean
  /** Custom CSS class name */
  className?: string
  /** Callback fired when a source is clicked */
  onSourceClick?: (citation: ParsedSourceCitation) => void
  /** Callback fired when sources are expanded/collapsed */
  onToggleExpanded?: (expanded: boolean) => void
  /** Custom render function for individual citations */
  renderCitation?: (citation: ParsedSourceCitation, index: number) => ReactNode
}

/**
 * Props for the AgentResponseCard component
 */
export interface AgentResponseCardProps {
  /** Markdown content to display */
  content: string
  /** Agent metadata */
  metadata: AgentMetadata
  /** Source citations */
  sources?: SourceCitation[]
  /** Structured response data */
  structured?: StructuredData
  /** Loading state */
  isLoading?: boolean
  /** Error state */
  error?: string | null
  /** Custom CSS class name */
  className?: string
  /** Markdown rendering options */
  markdownOptions?: MarkdownRenderingOptions
  /** Source citation display options */
  citationOptions?: SourceCitationDisplayOptions
  /** Show/hide different sections */
  showMetadata?: boolean
  /** Show/hide source citations */
  showSources?: boolean
  /** Show/hide structured data */
  showStructured?: boolean
  /** Callback fired when a source is clicked */
  onSourceClick?: (citation: ParsedSourceCitation) => void
  /** Callback fired when content is copied */
  onCopy?: (content: string) => void
  /** Callback fired when response is rated */
  onRate?: (rating: "up" | "down") => void
}

/**
 * File type information for source citations
 */
export interface FileTypeInfo {
  /** File extension */
  extension: string
  /** Display name */
  name: string
  /** Icon component or icon name */
  icon: string
  /** Color class for styling */
  color: string
  /** Whether syntax highlighting is supported */
  supportsHighlighting: boolean
}

/**
 * Citation parsing result
 */
export interface CitationParsingResult {
  /** Successfully parsed citations */
  valid: ParsedSourceCitation[]
  /** Citations that failed to parse */
  invalid: ParsedSourceCitation[]
  /** Total number of citations processed */
  total: number
}

/**
 * Performance metrics for response display
 */
export interface ResponseDisplayMetrics {
  /** Time to render markdown (ms) */
  markdownRenderTime?: number
  /** Time to parse citations (ms) */
  citationParseTime?: number
  /** Total component render time (ms) */
  totalRenderTime?: number
  /** Number of citations processed */
  citationCount?: number
  /** Content size in characters */
  contentSize?: number
}

/**
 * Error types for response display components
 */
export type ResponseDisplayError = 
  | "MARKDOWN_RENDER_ERROR"
  | "CITATION_PARSE_ERROR" 
  | "XSS_PROTECTION_ERROR"
  | "NAVIGATION_ERROR"
  | "UNKNOWN_ERROR"

/**
 * Error details for response display
 */
export interface ResponseDisplayErrorDetails {
  /** Error type */
  type: ResponseDisplayError
  /** Error message */
  message: string
  /** Original error object */
  originalError?: Error
  /** Context information */
  context?: Record<string, unknown>
}
