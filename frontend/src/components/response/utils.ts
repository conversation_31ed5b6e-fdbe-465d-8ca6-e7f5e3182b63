/**
 * Response Display Utility Functions
 * 
 * Utility functions for parsing source citations, handling file types,
 * and other common operations for response display components.
 */

import type { 
  ParsedSourceCitation, 
  CitationParsingResult, 
  FileTypeInfo,
  ResponseDisplayError,
  ResponseDisplayErrorDetails
} from "./types"
import type { SourceCitation } from "@/types/api"

/**
 * Regular expression for parsing source citations
 * Matches formats like:
 * - "file.py:10-20"
 * - "src/main.py:5"
 * - "docs/readme.md"
 * - "path/to/file.ts:1-100"
 */
const CITATION_REGEX = /^(.+?)(?::(\d+)(?:-(\d+))?)?$/

/**
 * Parse a single source citation string
 * 
 * @param citation - Source citation string to parse
 * @returns Parsed citation object with structured data
 * 
 * @example
 * ```typescript
 * const parsed = parseSourceCitation("src/main.py:10-20")
 * // Returns: { raw: "src/main.py:10-20", filePath: "src/main.py", startLine: 10, endLine: 20, isValid: true }
 * ```
 */
export function parseSourceCitation(citation: SourceCitation): ParsedSourceCitation {
  if (!citation || typeof citation !== "string") {
    return {
      raw: citation || "",
      filePath: "",
      isValid: false,
      error: "Invalid citation format: citation must be a non-empty string"
    }
  }

  const trimmed = citation.trim()
  if (!trimmed) {
    return {
      raw: citation,
      filePath: "",
      isValid: false,
      error: "Invalid citation format: citation cannot be empty"
    }
  }

  const match = trimmed.match(CITATION_REGEX)
  if (!match) {
    return {
      raw: citation,
      filePath: trimmed,
      isValid: false,
      error: "Invalid citation format: could not parse citation string"
    }
  }

  const [, filePath, startLineStr, endLineStr] = match
  
  if (!filePath) {
    return {
      raw: citation,
      filePath: "",
      isValid: false,
      error: "Invalid citation format: file path is required"
    }
  }

  const result: ParsedSourceCitation = {
    raw: citation,
    filePath: filePath.trim(),
    isValid: true
  }

  // Parse line numbers if present
  if (startLineStr) {
    const startLine = parseInt(startLineStr, 10)
    if (isNaN(startLine) || startLine < 1) {
      return {
        ...result,
        isValid: false,
        error: "Invalid citation format: start line must be a positive integer"
      }
    }
    result.startLine = startLine

    if (endLineStr) {
      const endLine = parseInt(endLineStr, 10)
      if (isNaN(endLine) || endLine < 1) {
        return {
          ...result,
          isValid: false,
          error: "Invalid citation format: end line must be a positive integer"
        }
      }
      if (endLine < startLine) {
        return {
          ...result,
          isValid: false,
          error: "Invalid citation format: end line must be greater than or equal to start line"
        }
      }
      result.endLine = endLine
    }
  }

  return result
}

/**
 * Parse multiple source citations
 * 
 * @param citations - Array of source citation strings
 * @returns Parsing result with valid and invalid citations
 */
export function parseSourceCitations(citations: SourceCitation[]): CitationParsingResult {
  const valid: ParsedSourceCitation[] = []
  const invalid: ParsedSourceCitation[] = []

  for (const citation of citations) {
    const parsed = parseSourceCitation(citation)
    if (parsed.isValid) {
      valid.push(parsed)
    } else {
      invalid.push(parsed)
    }
  }

  return {
    valid,
    invalid,
    total: citations.length
  }
}

/**
 * Get file extension from file path
 * 
 * @param filePath - File path to extract extension from
 * @returns File extension (without dot) or empty string
 */
export function getFileExtension(filePath: string): string {
  if (!filePath) return ""
  
  const lastDot = filePath.lastIndexOf(".")
  const lastSlash = Math.max(filePath.lastIndexOf("/"), filePath.lastIndexOf("\\"))
  
  // Extension must be after the last slash and contain a dot
  if (lastDot > lastSlash && lastDot < filePath.length - 1) {
    return filePath.substring(lastDot + 1).toLowerCase()
  }
  
  return ""
}

/**
 * File type information mapping
 */
const FILE_TYPE_MAP: Record<string, FileTypeInfo> = {
  // Programming languages
  "ts": { extension: "ts", name: "TypeScript", icon: "file-code", color: "text-blue-500", supportsHighlighting: true },
  "tsx": { extension: "tsx", name: "TypeScript React", icon: "file-code", color: "text-blue-500", supportsHighlighting: true },
  "js": { extension: "js", name: "JavaScript", icon: "file-code", color: "text-yellow-500", supportsHighlighting: true },
  "jsx": { extension: "jsx", name: "JavaScript React", icon: "file-code", color: "text-yellow-500", supportsHighlighting: true },
  "py": { extension: "py", name: "Python", icon: "file-code", color: "text-green-500", supportsHighlighting: true },
  "java": { extension: "java", name: "Java", icon: "file-code", color: "text-orange-500", supportsHighlighting: true },
  "cpp": { extension: "cpp", name: "C++", icon: "file-code", color: "text-purple-500", supportsHighlighting: true },
  "c": { extension: "c", name: "C", icon: "file-code", color: "text-purple-500", supportsHighlighting: true },
  "rs": { extension: "rs", name: "Rust", icon: "file-code", color: "text-orange-600", supportsHighlighting: true },
  "go": { extension: "go", name: "Go", icon: "file-code", color: "text-cyan-500", supportsHighlighting: true },
  
  // Markup and config
  "md": { extension: "md", name: "Markdown", icon: "file-text", color: "text-gray-500", supportsHighlighting: true },
  "html": { extension: "html", name: "HTML", icon: "file-code", color: "text-red-500", supportsHighlighting: true },
  "css": { extension: "css", name: "CSS", icon: "file-code", color: "text-blue-400", supportsHighlighting: true },
  "json": { extension: "json", name: "JSON", icon: "file-code", color: "text-yellow-600", supportsHighlighting: true },
  "yaml": { extension: "yaml", name: "YAML", icon: "file-code", color: "text-purple-400", supportsHighlighting: true },
  "yml": { extension: "yml", name: "YAML", icon: "file-code", color: "text-purple-400", supportsHighlighting: true },
  "xml": { extension: "xml", name: "XML", icon: "file-code", color: "text-orange-400", supportsHighlighting: true },
  "toml": { extension: "toml", name: "TOML", icon: "file-code", color: "text-gray-600", supportsHighlighting: true },
  
  // Documentation
  "txt": { extension: "txt", name: "Text", icon: "file-text", color: "text-gray-500", supportsHighlighting: false },
  "readme": { extension: "readme", name: "README", icon: "file-text", color: "text-blue-600", supportsHighlighting: false },
  
  // Default fallback
  "": { extension: "", name: "File", icon: "file", color: "text-gray-400", supportsHighlighting: false }
}

/**
 * Get file type information for a file path
 * 
 * @param filePath - File path to get type info for
 * @returns File type information
 */
export function getFileTypeInfo(filePath: string): FileTypeInfo {
  const extension = getFileExtension(filePath)
  return FILE_TYPE_MAP[extension] || FILE_TYPE_MAP[""]
}

/**
 * Format a source citation for display
 * 
 * @param citation - Parsed source citation
 * @param options - Formatting options
 * @returns Formatted citation string
 */
export function formatSourceCitation(
  citation: ParsedSourceCitation, 
  options: { showLineNumbers?: boolean; maxPathLength?: number } = {}
): string {
  const { showLineNumbers = true, maxPathLength = 50 } = options
  
  let filePath = citation.filePath
  
  // Truncate long file paths
  if (maxPathLength && filePath.length > maxPathLength) {
    const parts = filePath.split("/")
    if (parts.length > 1) {
      filePath = "..." + filePath.substring(filePath.length - maxPathLength + 3)
    }
  }
  
  if (!showLineNumbers || (!citation.startLine && !citation.endLine)) {
    return filePath
  }
  
  if (citation.startLine && citation.endLine && citation.startLine !== citation.endLine) {
    return `${filePath}:${citation.startLine}-${citation.endLine}`
  }
  
  if (citation.startLine) {
    return `${filePath}:${citation.startLine}`
  }
  
  return filePath
}

/**
 * Create a response display error
 * 
 * @param type - Error type
 * @param message - Error message
 * @param originalError - Original error object
 * @param context - Additional context
 * @returns Error details object
 */
export function createResponseDisplayError(
  type: ResponseDisplayError,
  message: string,
  originalError?: Error,
  context?: Record<string, unknown>
): ResponseDisplayErrorDetails {
  return {
    type,
    message,
    originalError,
    context
  }
}

/**
 * Sanitize content for XSS protection
 * Basic sanitization - in production, consider using a library like DOMPurify
 * 
 * @param content - Content to sanitize
 * @returns Sanitized content
 */
export function sanitizeContent(content: string): string {
  if (!content) return ""
  
  // Basic HTML entity encoding for common XSS vectors
  return content
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#x27;")
    .replace(/\//g, "&#x2F;")
}

/**
 * Debounce function for performance optimization
 * 
 * @param func - Function to debounce
 * @param wait - Wait time in milliseconds
 * @returns Debounced function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    
    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}
