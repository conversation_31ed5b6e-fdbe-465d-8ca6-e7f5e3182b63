/**
 * Response Display Components Barrel Export
 * 
 * Centralized export point for all response display components, types, and utilities.
 * This enables clean imports throughout the application.
 * 
 * @example
 * ```typescript
 * import { MarkdownRenderer, SourceCitationDisplay, AgentResponseCard } from '@/components/response'
 * import type { ParsedSourceCitation, AgentMetadata } from '@/components/response'
 * ```
 */

// Components
export { MarkdownRenderer } from "./MarkdownRenderer"
export { SourceCitationDisplay } from "./SourceCitationDisplay"
export { AgentResponseCard } from "./AgentResponseCard"

// Types
export type {
  ParsedSourceCitation,
  AgentMetadata,
  MarkdownRenderingOptions,
  SourceCitationDisplayOptions,
  MarkdownRendererProps,
  SourceCitationDisplayProps,
  AgentResponseCardProps,
  FileTypeInfo,
  CitationParsingResult,
  ResponseDisplayMetrics,
  ResponseDisplayError,
  ResponseDisplayErrorDetails,
} from "./types"

// Utilities
export {
  parseSourceCitation,
  parseSourceCitations,
  getFileExtension,
  getFileTypeInfo,
  formatSourceCitation,
  createResponseDisplayError,
  sanitizeContent,
  debounce,
} from "./utils"
