# Response Display Components

A comprehensive set of React components for displaying agent responses in the LLM RAG Codebase Query System.

## Overview

This package provides three main components for rendering agent responses with rich formatting, interactive source citations, and comprehensive metadata display:

- **MarkdownRenderer** - Standalone markdown rendering with syntax highlighting and XSS protection
- **SourceCitationDisplay** - Interactive source file citations with navigation support
- **AgentResponseCard** - Complete agent response display with metadata and structured data

## Quick Start

```tsx
import { 
  MarkdownRenderer, 
  SourceCitationDisplay, 
  AgentResponseCard 
} from "@/components/response"

// Basic markdown rendering
<MarkdownRenderer
  content="# Hello World\n\n```python\nprint('Hello!')\n```"
  options={{
    enableSyntaxHighlighting: true,
    enableGfm: true,
    enableXssProtection: true
  }}
/>

// Source citations with navigation
<SourceCitationDisplay
  sources={["src/main.py:10-20", "docs/readme.md"]}
  options={{
    maxVisible: 3,
    showFileTypes: true,
    enableNavigation: true
  }}
  onSourceClick={(citation) => console.log('Navigate to:', citation)}
/>

// Complete agent response
<AgentResponseCard
  content="# Analysis Results\n\nThe system architecture..."
  metadata={{
    agentType: "TECHNICAL_ARCHITECT",
    confidence: 0.95,
    processingTime: 2.3
  }}
  sources={["src/main.py:10-20"]}
  structured={{ complexity: "medium", patterns: ["singleton"] }}
  onSourceClick={(citation) => navigateToFile(citation)}
/>
```

## Components

### MarkdownRenderer

Renders markdown content with syntax highlighting, XSS protection, and interactive features.

**Features:**
- GitHub Flavored Markdown support
- Syntax highlighting for code blocks
- Copy-to-clipboard for code blocks
- XSS protection through content sanitization
- Responsive design and accessibility
- Custom component overrides
- Loading and error states

**Props:**
```tsx
interface MarkdownRendererProps {
  content: string
  options?: MarkdownRenderingOptions
  isLoading?: boolean
  error?: string | null
  className?: string
  onRender?: () => void
  onError?: (error: Error) => void
}
```

**Example:**
```tsx
<MarkdownRenderer
  content={responseContent}
  options={{
    enableSyntaxHighlighting: true,
    enableGfm: true,
    enableXssProtection: true,
    className: "custom-prose"
  }}
  onRender={() => console.log('Content rendered')}
  onError={(error) => console.error('Render error:', error)}
/>
```

### SourceCitationDisplay

Displays interactive source file citations with navigation and metadata.

**Features:**
- Clickable file navigation
- Line number highlighting
- File type indicators
- Expandable/collapsible display
- Error handling for invalid citations
- Accessibility support
- Custom rendering functions

**Props:**
```tsx
interface SourceCitationDisplayProps {
  sources: SourceCitation[]
  options?: SourceCitationDisplayOptions
  isLoading?: boolean
  className?: string
  onSourceClick?: (citation: ParsedSourceCitation) => void
  onToggleExpanded?: (expanded: boolean) => void
  renderCitation?: (citation: ParsedSourceCitation, index: number) => ReactNode
}
```

**Example:**
```tsx
<SourceCitationDisplay
  sources={["src/main.py:10-20", "docs/readme.md", "test.js:5"]}
  options={{
    maxVisible: 3,
    showFileTypes: true,
    showLineNumbers: true,
    enableNavigation: true
  }}
  onSourceClick={(citation) => {
    // Navigate to file
    window.open(`/file/${citation.filePath}#L${citation.startLine}`)
  }}
  onToggleExpanded={(expanded) => {
    console.log('Citations expanded:', expanded)
  }}
/>
```

### AgentResponseCard

Complete agent response display with metadata, content, and structured data.

**Features:**
- Agent branding and metadata display
- Integrated markdown rendering
- Source citation display
- Structured data presentation
- Performance metrics
- User interaction (copy, rate)
- Responsive design

**Props:**
```tsx
interface AgentResponseCardProps {
  content: string
  metadata: AgentMetadata
  sources?: SourceCitation[]
  structured?: StructuredData
  isLoading?: boolean
  error?: string | null
  className?: string
  markdownOptions?: MarkdownRenderingOptions
  citationOptions?: SourceCitationDisplayOptions
  showMetadata?: boolean
  showSources?: boolean
  showStructured?: boolean
  onSourceClick?: (citation: ParsedSourceCitation) => void
  onCopy?: (content: string) => void
  onRate?: (rating: "up" | "down") => void
}
```

**Example:**
```tsx
<AgentResponseCard
  content="# Technical Analysis\n\nThe system uses a microservices architecture..."
  metadata={{
    agentType: "TECHNICAL_ARCHITECT",
    confidence: 0.95,
    processingTime: 2.3,
    metadata: { version: "1.0" }
  }}
  sources={[
    "src/services/auth.py:1-50",
    "src/services/user.py:25-75",
    "docs/architecture.md:10-30"
  ]}
  structured={{
    architecture: "microservices",
    patterns: ["repository", "factory"],
    complexity: "medium",
    recommendations: ["add caching", "improve error handling"]
  }}
  markdownOptions={{
    enableSyntaxHighlighting: true,
    enableGfm: true
  }}
  citationOptions={{
    maxVisible: 5,
    showFileTypes: true,
    enableNavigation: true
  }}
  onSourceClick={(citation) => navigateToFile(citation)}
  onCopy={(content) => copyToClipboard(content)}
  onRate={(rating) => submitFeedback(rating)}
/>
```

## Types

### Core Types

```tsx
// Parsed source citation with structured data
interface ParsedSourceCitation {
  raw: string
  filePath: string
  startLine?: number
  endLine?: number
  isValid: boolean
  error?: string
}

// Agent metadata for response display
interface AgentMetadata {
  agentType: string
  confidence?: ConfidenceScore
  processingTime?: ProcessingTime
  metadata?: Record<string, unknown>
}

// Markdown rendering options
interface MarkdownRenderingOptions {
  enableSyntaxHighlighting?: boolean
  enableGfm?: boolean
  className?: string
  enableXssProtection?: boolean
  components?: Record<string, React.ComponentType<any>>
}

// Source citation display options
interface SourceCitationDisplayOptions {
  maxVisible?: number
  showFileTypes?: boolean
  showLineNumbers?: boolean
  enableNavigation?: boolean
  className?: string
}
```

## Utilities

### Citation Parsing

```tsx
import { parseSourceCitation, parseSourceCitations } from "@/components/response"

// Parse single citation
const citation = parseSourceCitation("src/main.py:10-20")
// Returns: { raw: "src/main.py:10-20", filePath: "src/main.py", startLine: 10, endLine: 20, isValid: true }

// Parse multiple citations
const result = parseSourceCitations(["src/main.py:10-20", "docs/readme.md", "invalid:0"])
// Returns: { valid: [...], invalid: [...], total: 3 }
```

### File Type Detection

```tsx
import { getFileTypeInfo, getFileExtension } from "@/components/response"

const fileInfo = getFileTypeInfo("src/main.py")
// Returns: { name: "Python", icon: "file-code", color: "text-green-500", supportsHighlighting: true }

const extension = getFileExtension("src/component.tsx")
// Returns: "tsx"
```

### Content Sanitization

```tsx
import { sanitizeContent } from "@/components/response"

const safe = sanitizeContent('<script>alert("xss")</script>')
// Returns: "&lt;script&gt;alert(&quot;xss&quot;)&lt;/script&gt;"
```

## Integration with Existing Components

The response display components are designed to integrate seamlessly with existing components:

### MessageCard Integration

The existing `MessageCard` component has been refactored to use the new response display components:

```tsx
// Before: Inline markdown rendering
<ReactMarkdown>{message.content}</ReactMarkdown>

// After: Using MarkdownRenderer
<MarkdownRenderer
  content={message.content}
  options={{
    enableSyntaxHighlighting: true,
    enableGfm: true,
    enableXssProtection: true
  }}
/>

// Before: Basic source display
{sources.map(source => <Badge>{source}</Badge>)}

// After: Using SourceCitationDisplay
<SourceCitationDisplay
  sources={sources}
  options={{
    maxVisible: 3,
    showFileTypes: true,
    enableNavigation: !!onSourceClick
  }}
  onSourceClick={onSourceClick}
/>
```

## Performance Considerations

- **Code Splitting**: Components are designed for tree-shaking and code splitting
- **Lazy Loading**: Large responses can be handled with pagination or virtualization
- **Memoization**: Components use React.memo and useMemo for performance optimization
- **Debouncing**: User interactions are debounced to prevent excessive API calls

## Accessibility

All components follow WCAG 2.1 guidelines:

- **Keyboard Navigation**: Full keyboard support for all interactive elements
- **Screen Readers**: Proper ARIA labels and semantic HTML
- **Color Contrast**: Sufficient contrast ratios for all text and UI elements
- **Focus Management**: Clear focus indicators and logical tab order

## Testing

Comprehensive test suite included:

```bash
# Run all response component tests
pnpm test src/components/response/__tests__/

# Run specific component tests
pnpm test src/components/response/__tests__/MarkdownRenderer.test.tsx
pnpm test src/components/response/__tests__/SourceCitationDisplay.test.tsx
pnpm test src/components/response/__tests__/utils.test.ts
```

## Security

- **XSS Protection**: Content sanitization prevents script injection
- **Safe Links**: External links open with `rel="noopener noreferrer"`
- **Input Validation**: All user inputs are validated and sanitized
- **Error Boundaries**: Graceful error handling prevents crashes

## Browser Support

- **Modern Browsers**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **Mobile**: iOS Safari 14+, Chrome Mobile 90+
- **Accessibility**: Screen readers and assistive technologies

## Migration Guide

### From Inline Markdown

```tsx
// Before
<div className="prose">
  <ReactMarkdown>{content}</ReactMarkdown>
</div>

// After
<MarkdownRenderer
  content={content}
  options={{ enableSyntaxHighlighting: true }}
/>
```

### From Basic Source Display

```tsx
// Before
<div>
  {sources.map(source => (
    <span key={source} onClick={() => navigate(source)}>
      {source}
    </span>
  ))}
</div>

// After
<SourceCitationDisplay
  sources={sources}
  onSourceClick={(citation) => navigate(citation.filePath)}
/>
```

## Contributing

When contributing to the response display components:

1. Follow the established patterns and interfaces
2. Add comprehensive tests for new features
3. Update documentation for API changes
4. Ensure accessibility compliance
5. Test with real backend data
6. Follow the project's TypeScript standards

## License

Part of the LLM RAG Codebase Query System project.
