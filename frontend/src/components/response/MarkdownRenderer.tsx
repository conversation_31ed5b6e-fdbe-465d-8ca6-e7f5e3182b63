"use client"

import * as React from "react"
import ReactMarkdown from "react-markdown"
import remarkGfm from "remark-gfm"
import rehypeHighlight from "rehype-highlight"
import { Copy, AlertCircle } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

import { useCopyToClipboard } from "@/hooks/useCopyToClipboard"
import { useToast } from "@/hooks/useToast"
import { cn } from "@/lib/utils"

import type { MarkdownRendererProps } from "./types"
import { createResponseDisplayError, sanitizeContent } from "./utils"

// Import highlight.js styles
import "highlight.js/styles/github.css"

/**
 * MarkdownRenderer Component
 * 
 * A standalone, reusable component for rendering markdown content with:
 * - Syntax highlighting for code blocks
 * - XSS protection through content sanitization
 * - GitHub Flavored Markdown support
 * - Responsive design and accessibility
 * - Copy-to-clipboard functionality for code blocks
 * - Error handling and loading states
 * 
 * @example
 * ```tsx
 * <MarkdownRenderer
 *   content="# Hello World\n\n```python\nprint('Hello!')\n```"
 *   options={{
 *     enableSyntaxHighlighting: true,
 *     enableGfm: true,
 *     enableXssProtection: true
 *   }}
 *   onRender={() => console.log('Rendered')}
 * />
 * ```
 */
export function MarkdownRenderer({
  content,
  options = {},
  isLoading = false,
  error = null,
  className,
  onRender,
  onError,
}: MarkdownRendererProps) {
  const { toast } = useToast()
  const { copyToClipboard } = useCopyToClipboard()
  const [renderError, setRenderError] = React.useState<string | null>(null)
  const [isRendering, setIsRendering] = React.useState(false)

  // Default options
  const {
    enableSyntaxHighlighting = true,
    enableGfm = true,
    enableXssProtection = true,
    components: customComponents = {},
  } = options

  // Process content with XSS protection if enabled
  const processedContent = React.useMemo(() => {
    if (!content) return ""
    
    try {
      if (enableXssProtection) {
        // Note: In production, consider using DOMPurify for more robust XSS protection
        return sanitizeContent(content)
      }
      return content
    } catch (err) {
      const error = createResponseDisplayError(
        "XSS_PROTECTION_ERROR",
        "Failed to sanitize content",
        err as Error,
        { contentLength: content.length }
      )
      onError?.(err as Error)
      setRenderError(error.message)
      return ""
    }
  }, [content, enableXssProtection, onError])

  // Handle copy to clipboard for code blocks
  const handleCopyCode = React.useCallback(async (code: string) => {
    try {
      await copyToClipboard(code)
      toast({
        title: "Code copied",
        description: "Code block has been copied to clipboard",
      })
    } catch (err) {
      toast({
        title: "Failed to copy",
        description: "Could not copy code to clipboard",
        variant: "destructive",
      })
    }
  }, [copyToClipboard, toast])

  // Custom components for markdown rendering
  const markdownComponents = React.useMemo(() => ({
    // Enhanced code block with copy functionality
    code: ({ node, inline, className, children, ...props }: any) => {
      const match = /language-(\w+)/.exec(className || "")
      const language = match ? match[1] : ""
      const codeContent = String(children).replace(/\n$/, "")

      if (!inline && codeContent) {
        return (
          <div className="relative group">
            <pre className={cn("relative", className)} {...props}>
              <code className={className}>
                {children}
              </code>
              {/* Copy button for code blocks */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute top-2 right-2 h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity bg-background/80 hover:bg-background"
                      onClick={() => handleCopyCode(codeContent)}
                      aria-label={`Copy ${language || 'code'} to clipboard`}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Copy code</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </pre>
          </div>
        )
      }

      return (
        <code className={cn("relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm", className)} {...props}>
          {children}
        </code>
      )
    },

    // Enhanced blockquote styling
    blockquote: ({ children, ...props }: any) => (
      <blockquote 
        className="mt-6 border-l-2 border-primary pl-6 italic text-muted-foreground"
        {...props}
      >
        {children}
      </blockquote>
    ),

    // Enhanced table styling
    table: ({ children, ...props }: any) => (
      <div className="my-6 w-full overflow-y-auto">
        <table className="w-full border-collapse border border-border" {...props}>
          {children}
        </table>
      </div>
    ),

    th: ({ children, ...props }: any) => (
      <th className="border border-border px-4 py-2 text-left font-bold bg-muted/50" {...props}>
        {children}
      </th>
    ),

    td: ({ children, ...props }: any) => (
      <td className="border border-border px-4 py-2" {...props}>
        {children}
      </td>
    ),

    // Enhanced link styling with security
    a: ({ href, children, ...props }: any) => (
      <a
        href={href}
        className="text-primary underline underline-offset-4 hover:text-primary/80"
        target="_blank"
        rel="noopener noreferrer"
        {...props}
      >
        {children}
      </a>
    ),

    // Merge custom components
    ...customComponents,
  }), [handleCopyCode, customComponents])

  // Prepare rehype plugins
  const rehypePlugins = React.useMemo(() => {
    const plugins = []
    if (enableSyntaxHighlighting) {
      plugins.push(rehypeHighlight)
    }
    return plugins
  }, [enableSyntaxHighlighting])

  // Prepare remark plugins
  const remarkPlugins = React.useMemo(() => {
    const plugins = []
    if (enableGfm) {
      plugins.push(remarkGfm)
    }
    return plugins
  }, [enableGfm])

  // Handle rendering lifecycle
  React.useEffect(() => {
    if (processedContent && !isLoading && !error && !renderError) {
      setIsRendering(true)
      // Simulate async rendering
      const timer = setTimeout(() => {
        setIsRendering(false)
        onRender?.()
      }, 0)
      return () => clearTimeout(timer)
    }
  }, [processedContent, isLoading, error, renderError, onRender])

  // Loading state
  if (isLoading || isRendering) {
    return (
      <div className={cn("space-y-3", className)}>
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-4 w-1/2" />
        <Skeleton className="h-20 w-full" />
        <Skeleton className="h-4 w-2/3" />
      </div>
    )
  }

  // Error state
  if (error || renderError) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          {error || renderError || "Failed to render markdown content"}
        </AlertDescription>
      </Alert>
    )
  }

  // Empty content
  if (!processedContent) {
    return (
      <div className={cn("text-muted-foreground text-sm italic", className)}>
        No content to display
      </div>
    )
  }

  return (
    <div 
      className={cn(
        "prose prose-sm max-w-none dark:prose-invert",
        "prose-headings:scroll-m-20 prose-headings:font-semibold prose-headings:tracking-tight",
        "prose-h1:text-2xl prose-h2:text-xl prose-h3:text-lg",
        "prose-p:leading-7 prose-li:mt-2",
        "prose-pre:bg-muted prose-pre:border prose-pre:border-border",
        "prose-code:relative prose-code:rounded prose-code:bg-muted prose-code:px-[0.3rem] prose-code:py-[0.2rem] prose-code:font-mono prose-code:text-sm",
        className
      )}
      role="article"
      aria-label="Rendered markdown content"
    >
      <ReactMarkdown
        remarkPlugins={remarkPlugins}
        rehypePlugins={rehypePlugins}
        components={markdownComponents}
      >
        {processedContent}
      </ReactMarkdown>
    </div>
  )
}
