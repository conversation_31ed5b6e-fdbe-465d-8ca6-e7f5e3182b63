/**
 * @vitest-environment jsdom
 */

import { describe, it, expect, vi, beforeEach } from "vitest"
import { render, screen, fireEvent } from "@testing-library/react"
import userEvent from "@testing-library/user-event"

import { SourceCitationDisplay } from "../SourceCitationDisplay"
import type { ParsedSourceCitation } from "../types"

// Mock the hooks
vi.mock("@/hooks/useToast", () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}))

describe("SourceCitationDisplay", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  const mockSources = [
    "src/main.py:10-20",
    "docs/readme.md",
    "test/utils.js:5",
    "invalid:0-5", // Invalid citation
  ]

  it("should render source citations", () => {
    render(<SourceCitationDisplay sources={mockSources} />)
    
    expect(screen.getByText("Sources")).toBeInTheDocument()
    expect(screen.getByText("4")).toBeInTheDocument() // Total count badge
    expect(screen.getByText("src/main.py")).toBeInTheDocument()
    expect(screen.getByText("docs/readme.md")).toBeInTheDocument()
  })

  it("should show invalid citations with error indicators", () => {
    render(<SourceCitationDisplay sources={mockSources} />)
    
    // Should show invalid count badge
    expect(screen.getByText("1 invalid")).toBeInTheDocument()
  })

  it("should handle empty sources array", () => {
    const { container } = render(<SourceCitationDisplay sources={[]} />)
    
    // Component should not render anything for empty sources
    expect(container.firstChild).toBeNull()
  })

  it("should show expand/collapse for many sources", () => {
    const manySources = Array.from({ length: 10 }, (_, i) => `file${i}.py`)
    render(
      <SourceCitationDisplay 
        sources={manySources} 
        options={{ maxVisible: 3 }} 
      />
    )
    
    // Should show expand button
    expect(screen.getByText(/show.*more/i)).toBeInTheDocument()
  })

  it("should expand and collapse sources", async () => {
    const user = userEvent.setup()
    const manySources = Array.from({ length: 5 }, (_, i) => `file${i}.py`)
    
    render(
      <SourceCitationDisplay 
        sources={manySources} 
        options={{ maxVisible: 2 }} 
      />
    )
    
    // Initially should show only 2 sources
    expect(screen.getByText("file0.py")).toBeInTheDocument()
    expect(screen.getByText("file1.py")).toBeInTheDocument()
    expect(screen.queryByText("file2.py")).not.toBeInTheDocument()
    
    // Click expand
    const expandButton = screen.getByText(/show.*more/i)
    await user.click(expandButton)
    
    // Should now show all sources
    expect(screen.getByText("file2.py")).toBeInTheDocument()
    expect(screen.getByText("file3.py")).toBeInTheDocument()
    expect(screen.getByText("file4.py")).toBeInTheDocument()
    
    // Should show collapse button
    expect(screen.getByText(/show less/i)).toBeInTheDocument()
  })

  it("should call onSourceClick when citation is clicked", async () => {
    const user = userEvent.setup()
    const onSourceClick = vi.fn()
    
    render(
      <SourceCitationDisplay 
        sources={["src/main.py:10-20"]} 
        options={{ enableNavigation: true }}
        onSourceClick={onSourceClick}
      />
    )
    
    const citationButton = screen.getByRole("button", { name: /src\/main\.py/ })
    await user.click(citationButton)
    
    expect(onSourceClick).toHaveBeenCalledWith(
      expect.objectContaining({
        filePath: "src/main.py",
        startLine: 10,
        endLine: 20,
        isValid: true,
      })
    )
  })

  it("should disable navigation when option is false", () => {
    render(
      <SourceCitationDisplay 
        sources={["src/main.py"]} 
        options={{ enableNavigation: false }}
      />
    )
    
    const citationButton = screen.getByRole("button", { name: /src\/main\.py/ })
    expect(citationButton).toBeDisabled()
  })

  it("should show file type icons when enabled", () => {
    render(
      <SourceCitationDisplay 
        sources={["src/main.py", "docs/readme.md"]} 
        options={{ showFileTypes: true }}
      />
    )
    
    // Should show file type icons (mocked as FileText components)
    const icons = screen.getAllByTestId(/file-icon/i)
    expect(icons.length).toBeGreaterThan(0)
  })

  it("should show line numbers when enabled", () => {
    render(
      <SourceCitationDisplay 
        sources={["src/main.py:10-20"]} 
        options={{ showLineNumbers: true }}
      />
    )
    
    expect(screen.getByText("10-20")).toBeInTheDocument()
  })

  it("should hide line numbers when disabled", () => {
    render(
      <SourceCitationDisplay 
        sources={["src/main.py:10-20"]} 
        options={{ showLineNumbers: false }}
      />
    )
    
    expect(screen.queryByText("10-20")).not.toBeInTheDocument()
    expect(screen.getByText("src/main.py")).toBeInTheDocument()
  })

  it("should show loading state", () => {
    render(<SourceCitationDisplay sources={mockSources} isLoading={true} />)
    
    // Should show skeleton loaders
    const skeletons = screen.getAllByTestId(/skeleton/i)
    expect(skeletons.length).toBeGreaterThan(0)
  })

  it("should call onToggleExpanded when expanding/collapsing", async () => {
    const user = userEvent.setup()
    const onToggleExpanded = vi.fn()
    const manySources = Array.from({ length: 5 }, (_, i) => `file${i}.py`)
    
    render(
      <SourceCitationDisplay 
        sources={manySources} 
        options={{ maxVisible: 2 }}
        onToggleExpanded={onToggleExpanded}
      />
    )
    
    const expandButton = screen.getByText(/show.*more/i)
    await user.click(expandButton)
    
    expect(onToggleExpanded).toHaveBeenCalledWith(true)
  })

  it("should use custom render function when provided", () => {
    const customRender = (citation: ParsedSourceCitation, index: number) => (
      <div key={index} data-testid="custom-citation">
        Custom: {citation.filePath}
      </div>
    )
    
    render(
      <SourceCitationDisplay 
        sources={["src/main.py"]} 
        renderCitation={customRender}
      />
    )
    
    expect(screen.getByTestId("custom-citation")).toBeInTheDocument()
    expect(screen.getByText("Custom: src/main.py")).toBeInTheDocument()
  })

  it("should show tooltips with file information", async () => {
    const user = userEvent.setup()
    
    render(
      <SourceCitationDisplay 
        sources={["src/main.py:10-20"]} 
        options={{ enableNavigation: true }}
      />
    )
    
    const citationButton = screen.getByRole("button", { name: /src\/main\.py/ })
    await user.hover(citationButton)
    
    // Tooltip should appear with file info
    // Note: Tooltip testing might require additional setup depending on the tooltip library
  })

  it("should handle parse errors gracefully", () => {
    // Mock parseSourceCitations to throw an error
    const originalConsoleError = console.error
    console.error = vi.fn()
    
    render(<SourceCitationDisplay sources={["malformed citation"]} />)
    
    // Should show error state instead of crashing
    expect(screen.getByRole("alert")).toBeInTheDocument()
    
    console.error = originalConsoleError
  })

  it("should apply custom CSS classes", () => {
    const customClass = "custom-citations"
    render(
      <SourceCitationDisplay 
        sources={["src/main.py"]} 
        className={customClass}
      />
    )
    
    const container = screen.getByText("Sources").closest("div")
    expect(container).toHaveClass(customClass)
  })
})
