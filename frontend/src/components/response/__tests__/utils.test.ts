/**
 * @vitest-environment jsdom
 */

import { describe, it, expect } from "vitest"

import {
  parseSourceCitation,
  parseSourceCitations,
  getFileExtension,
  getFileTypeInfo,
  formatSourceCitation,
  sanitizeContent,
} from "../utils"

describe("parseSourceCitation", () => {
  it("should parse file path only", () => {
    const result = parseSourceCitation("src/main.py")
    expect(result).toEqual({
      raw: "src/main.py",
      filePath: "src/main.py",
      isValid: true,
    })
  })

  it("should parse file path with single line", () => {
    const result = parseSourceCitation("src/main.py:10")
    expect(result).toEqual({
      raw: "src/main.py:10",
      filePath: "src/main.py",
      startLine: 10,
      isValid: true,
    })
  })

  it("should parse file path with line range", () => {
    const result = parseSourceCitation("src/main.py:10-20")
    expect(result).toEqual({
      raw: "src/main.py:10-20",
      filePath: "src/main.py",
      startLine: 10,
      endLine: 20,
      isValid: true,
    })
  })

  it("should handle invalid citations", () => {
    const result = parseSourceCitation("")
    expect(result.isValid).toBe(false)
    expect(result.error).toContain("non-empty string")
  })

  it("should handle invalid line numbers", () => {
    const result = parseSourceCitation("src/main.py:0")
    expect(result.isValid).toBe(false)
    expect(result.error).toContain("positive integer")
  })

  it("should handle invalid line ranges", () => {
    const result = parseSourceCitation("src/main.py:20-10")
    expect(result.isValid).toBe(false)
    expect(result.error).toContain("greater than or equal to start line")
  })

  it("should handle non-string input", () => {
    const result = parseSourceCitation(null as any)
    expect(result.isValid).toBe(false)
    expect(result.error).toContain("non-empty string")
  })
})

describe("parseSourceCitations", () => {
  it("should parse multiple valid citations", () => {
    const citations = ["src/main.py:10-20", "docs/readme.md", "test.js:5"]
    const result = parseSourceCitations(citations)
    
    expect(result.total).toBe(3)
    expect(result.valid).toHaveLength(3)
    expect(result.invalid).toHaveLength(0)
  })

  it("should separate valid and invalid citations", () => {
    const citations = ["src/main.py:10-20", "", "invalid:0-5"]
    const result = parseSourceCitations(citations)
    
    expect(result.total).toBe(3)
    expect(result.valid).toHaveLength(1)
    expect(result.invalid).toHaveLength(2)
  })
})

describe("getFileExtension", () => {
  it("should extract file extensions correctly", () => {
    expect(getFileExtension("file.py")).toBe("py")
    expect(getFileExtension("path/to/file.tsx")).toBe("tsx")
    expect(getFileExtension("file.test.js")).toBe("js")
    expect(getFileExtension("README.md")).toBe("md")
  })

  it("should handle files without extensions", () => {
    expect(getFileExtension("README")).toBe("")
    expect(getFileExtension("path/to/file")).toBe("")
  })

  it("should handle edge cases", () => {
    expect(getFileExtension("")).toBe("")
    expect(getFileExtension(".gitignore")).toBe("gitignore")
    expect(getFileExtension("file.")).toBe("")
  })
})

describe("getFileTypeInfo", () => {
  it("should return correct info for known file types", () => {
    const pyInfo = getFileTypeInfo("src/main.py")
    expect(pyInfo.name).toBe("Python")
    expect(pyInfo.supportsHighlighting).toBe(true)
    expect(pyInfo.color).toBe("text-green-500")

    const tsInfo = getFileTypeInfo("src/component.tsx")
    expect(tsInfo.name).toBe("TypeScript React")
    expect(tsInfo.supportsHighlighting).toBe(true)
  })

  it("should return default info for unknown file types", () => {
    const unknownInfo = getFileTypeInfo("file.unknown")
    expect(unknownInfo.name).toBe("File")
    expect(unknownInfo.supportsHighlighting).toBe(false)
  })
})

describe("formatSourceCitation", () => {
  it("should format citations with line numbers", () => {
    const citation = {
      raw: "src/main.py:10-20",
      filePath: "src/main.py",
      startLine: 10,
      endLine: 20,
      isValid: true,
    }
    
    const formatted = formatSourceCitation(citation)
    expect(formatted).toBe("src/main.py:10-20")
  })

  it("should format citations without line numbers when disabled", () => {
    const citation = {
      raw: "src/main.py:10-20",
      filePath: "src/main.py",
      startLine: 10,
      endLine: 20,
      isValid: true,
    }
    
    const formatted = formatSourceCitation(citation, { showLineNumbers: false })
    expect(formatted).toBe("src/main.py")
  })

  it("should truncate long file paths", () => {
    const citation = {
      raw: "very/long/path/to/some/deeply/nested/file.py",
      filePath: "very/long/path/to/some/deeply/nested/file.py",
      isValid: true,
    }
    
    const formatted = formatSourceCitation(citation, { maxPathLength: 20 })
    expect(formatted).toContain("...")
    expect(formatted.length).toBeLessThanOrEqual(20)
  })
})

describe("sanitizeContent", () => {
  it("should sanitize HTML entities", () => {
    const malicious = '<script>alert("xss")</script>'
    const sanitized = sanitizeContent(malicious)
    expect(sanitized).not.toContain("<script>")
    expect(sanitized).toContain("&lt;script&gt;")
  })

  it("should handle empty content", () => {
    expect(sanitizeContent("")).toBe("")
    expect(sanitizeContent(null as any)).toBe("")
  })

  it("should preserve safe content", () => {
    const safe = "This is safe content with no HTML"
    expect(sanitizeContent(safe)).toBe(safe)
  })

  it("should sanitize all dangerous characters", () => {
    const dangerous = '&<>"\'/'
    const sanitized = sanitizeContent(dangerous)
    expect(sanitized).toBe("&amp;&lt;&gt;&quot;&#x27;&#x2F;")
  })
})
