/**
 * @vitest-environment jsdom
 */

import { describe, it, expect, vi, beforeEach } from "vitest"
import { render, screen, fireEvent, waitFor } from "@testing-library/react"
import userEvent from "@testing-library/user-event"

import { MarkdownRenderer } from "../MarkdownRenderer"

// Mock the hooks
vi.mock("@/hooks/useCopyToClipboard", () => ({
  useCopyToClipboard: () => ({
    copyToClipboard: vi.fn().mockResolvedValue(undefined),
  }),
}))

vi.mock("@/hooks/useToast", () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}))

describe("MarkdownRenderer", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it("should render basic markdown content", () => {
    const content = "# Hello World\n\nThis is a **bold** text."
    render(<MarkdownRenderer content={content} />)
    
    expect(screen.getByRole("heading", { level: 1 })).toHaveTextContent("Hello World")
    expect(screen.getByText("bold")).toBeInTheDocument()
  })

  it("should render code blocks with syntax highlighting", () => {
    const content = "```python\nprint('Hello World')\n```"
    render(<MarkdownRenderer content={content} />)
    
    const codeBlock = screen.getByText("print('Hello World')")
    expect(codeBlock).toBeInTheDocument()
    expect(codeBlock.closest("pre")).toBeInTheDocument()
  })

  it("should show copy button on code blocks", async () => {
    const content = "```python\nprint('Hello World')\n```"
    render(<MarkdownRenderer content={content} />)
    
    const codeContainer = screen.getByText("print('Hello World')").closest("div")
    expect(codeContainer).toBeInTheDocument()
    
    // Copy button should be present (though may be hidden initially)
    const copyButton = screen.getByLabelText(/copy.*to clipboard/i)
    expect(copyButton).toBeInTheDocument()
  })

  it("should handle inline code", () => {
    const content = "Use `console.log()` to debug."
    render(<MarkdownRenderer content={content} />)
    
    const inlineCode = screen.getByText("console.log()")
    expect(inlineCode.tagName).toBe("CODE")
  })

  it("should render tables correctly", () => {
    const content = `
| Name | Age |
|------|-----|
| John | 30  |
| Jane | 25  |
`
    render(<MarkdownRenderer content={content} />)
    
    expect(screen.getByRole("table")).toBeInTheDocument()
    expect(screen.getByText("Name")).toBeInTheDocument()
    expect(screen.getByText("John")).toBeInTheDocument()
  })

  it("should render blockquotes", () => {
    const content = "> This is a quote"
    render(<MarkdownRenderer content={content} />)
    
    const blockquote = screen.getByText("This is a quote").closest("blockquote")
    expect(blockquote).toBeInTheDocument()
  })

  it("should render links with security attributes", () => {
    const content = "[Example](https://example.com)"
    render(<MarkdownRenderer content={content} />)
    
    const link = screen.getByRole("link", { name: "Example" })
    expect(link).toHaveAttribute("href", "https://example.com")
    expect(link).toHaveAttribute("target", "_blank")
    expect(link).toHaveAttribute("rel", "noopener noreferrer")
  })

  it("should show loading state", () => {
    render(<MarkdownRenderer content="# Test" isLoading={true} />)
    
    // Should show skeleton loaders
    const skeletons = screen.getAllByTestId(/skeleton/i)
    expect(skeletons.length).toBeGreaterThan(0)
  })

  it("should show error state", () => {
    const errorMessage = "Failed to render content"
    render(<MarkdownRenderer content="# Test" error={errorMessage} />)
    
    expect(screen.getByText(errorMessage)).toBeInTheDocument()
    expect(screen.getByRole("alert")).toBeInTheDocument()
  })

  it("should handle empty content", () => {
    render(<MarkdownRenderer content="" />)
    
    expect(screen.getByText("No content to display")).toBeInTheDocument()
  })

  it("should call onRender callback when content is rendered", async () => {
    const onRender = vi.fn()
    render(<MarkdownRenderer content="# Test" onRender={onRender} />)
    
    await waitFor(() => {
      expect(onRender).toHaveBeenCalled()
    })
  })

  it("should disable syntax highlighting when option is false", () => {
    const content = "```python\nprint('Hello')\n```"
    render(
      <MarkdownRenderer 
        content={content} 
        options={{ enableSyntaxHighlighting: false }} 
      />
    )
    
    // Code should still be rendered but without syntax highlighting classes
    expect(screen.getByText("print('Hello')")).toBeInTheDocument()
  })

  it("should disable GFM when option is false", () => {
    const content = "- [x] Task completed\n- [ ] Task pending"
    render(
      <MarkdownRenderer 
        content={content} 
        options={{ enableGfm: false }} 
      />
    )
    
    // Without GFM, checkboxes won't be rendered as interactive elements
    expect(screen.getByText(/Task completed/)).toBeInTheDocument()
  })

  it("should apply custom CSS classes", () => {
    const content = "# Test"
    const customClass = "custom-markdown"
    render(<MarkdownRenderer content={content} className={customClass} />)
    
    const container = screen.getByRole("article")
    expect(container).toHaveClass(customClass)
  })

  it("should handle XSS protection", () => {
    const maliciousContent = '<script>alert("xss")</script>'
    render(
      <MarkdownRenderer 
        content={maliciousContent} 
        options={{ enableXssProtection: true }} 
      />
    )
    
    // Script tags should be escaped
    expect(screen.queryByText('alert("xss")')).not.toBeInTheDocument()
  })

  it("should use custom components when provided", () => {
    const content = "# Custom Heading"
    const CustomH1 = ({ children }: { children: React.ReactNode }) => (
      <h1 data-testid="custom-heading">{children}</h1>
    )
    
    render(
      <MarkdownRenderer 
        content={content} 
        options={{ 
          components: { 
            h1: CustomH1 
          } 
        }} 
      />
    )
    
    expect(screen.getByTestId("custom-heading")).toBeInTheDocument()
  })

  it("should handle copy functionality for code blocks", async () => {
    const user = userEvent.setup()
    const content = "```python\nprint('Hello World')\n```"
    
    render(<MarkdownRenderer content={content} />)
    
    const copyButton = screen.getByLabelText(/copy.*to clipboard/i)
    await user.click(copyButton)
    
    // Toast should be called (mocked)
    // Copy function should be called (mocked)
  })
})
