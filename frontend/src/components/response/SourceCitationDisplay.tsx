"use client"

import * as React from "react"
import { 
  Ch<PERSON>ronDown, 
  ChevronUp, 
  ExternalLink, 
  FileText, 
  AlertTriangle,
  <PERSON>h,
  Folder
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Too<PERSON>ip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Skeleton } from "@/components/ui/skeleton"

import { cn } from "@/lib/utils"
import { useToast } from "@/hooks/useToast"

import type { SourceCitationDisplayProps, ParsedSourceCitation } from "./types"
import { 
  parseSourceCitations, 
  getFileTypeInfo, 
  formatSourceCitation,
  createResponseDisplayError 
} from "./utils"

/**
 * Individual source citation item component
 */
interface CitationItemProps {
  citation: ParsedSourceCitation
  index: number
  showLineNumbers: boolean
  showFileTypes: boolean
  enableNavigation: boolean
  onSourceClick?: (citation: ParsedSourceCitation) => void
  renderCitation?: (citation: ParsedSourceCitation, index: number) => React.ReactNode
}

function CitationItem({
  citation,
  index,
  showLineNumbers,
  showFileTypes,
  enableNavigation,
  onSourceClick,
  renderCitation,
}: CitationItemProps) {
  const { toast } = useToast()
  const fileTypeInfo = getFileTypeInfo(citation.filePath)

  // Handle citation click
  const handleClick = React.useCallback(() => {
    if (!enableNavigation) return

    try {
      onSourceClick?.(citation)
    } catch (err) {
      toast({
        title: "Navigation failed",
        description: "Could not navigate to source file",
        variant: "destructive",
      })
    }
  }, [citation, enableNavigation, onSourceClick, toast])

  // Custom render function takes precedence
  if (renderCitation) {
    return <>{renderCitation(citation, index)}</>
  }

  // Invalid citation display
  if (!citation.isValid) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge 
              variant="destructive" 
              className="font-mono text-xs cursor-help"
            >
              <AlertTriangle className="w-3 h-3 mr-1" />
              {citation.raw}
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <p className="text-sm">{citation.error}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  }

  // Valid citation display
  const formattedCitation = formatSourceCitation(citation, { 
    showLineNumbers, 
    maxPathLength: 40 
  })

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className={cn(
              "h-auto p-2 font-mono text-xs justify-start gap-2",
              enableNavigation && "hover:bg-accent cursor-pointer",
              !enableNavigation && "cursor-default"
            )}
            onClick={enableNavigation ? handleClick : undefined}
            disabled={!enableNavigation}
          >
            {/* File type icon */}
            {showFileTypes && (
              <div className={cn("flex-shrink-0", fileTypeInfo.color)}>
                {fileTypeInfo.icon === "file-code" && <FileText className="w-3 h-3" />}
                {fileTypeInfo.icon === "file-text" && <FileText className="w-3 h-3" />}
                {fileTypeInfo.icon === "file" && <Folder className="w-3 h-3" />}
              </div>
            )}

            {/* File path */}
            <span className="truncate flex-1 text-left">
              {citation.filePath}
            </span>

            {/* Line numbers */}
            {showLineNumbers && (citation.startLine || citation.endLine) && (
              <div className="flex items-center gap-1 text-muted-foreground">
                <Hash className="w-3 h-3" />
                <span>
                  {citation.startLine && citation.endLine && citation.startLine !== citation.endLine
                    ? `${citation.startLine}-${citation.endLine}`
                    : citation.startLine || citation.endLine}
                </span>
              </div>
            )}

            {/* Navigation indicator */}
            {enableNavigation && (
              <ExternalLink className="w-3 h-3 text-muted-foreground" />
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <div className="space-y-1">
            <p className="font-medium">{fileTypeInfo.name}</p>
            <p className="text-xs text-muted-foreground">{citation.raw}</p>
            {enableNavigation && (
              <p className="text-xs">Click to navigate to file</p>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

/**
 * SourceCitationDisplay Component
 * 
 * Interactive component for displaying source file citations with:
 * - Clickable file navigation
 * - Line number highlighting
 * - File type indicators
 * - Expandable/collapsible display for many citations
 * - Error handling for invalid citations
 * - Accessibility support
 * 
 * @example
 * ```tsx
 * <SourceCitationDisplay
 *   sources={["src/main.py:10-20", "docs/readme.md", "invalid:citation"]}
 *   options={{
 *     maxVisible: 3,
 *     showFileTypes: true,
 *     showLineNumbers: true,
 *     enableNavigation: true
 *   }}
 *   onSourceClick={(citation) => console.log('Navigate to:', citation)}
 * />
 * ```
 */
export function SourceCitationDisplay({
  sources,
  options = {},
  isLoading = false,
  className,
  onSourceClick,
  onToggleExpanded,
  renderCitation,
}: SourceCitationDisplayProps) {
  const [isExpanded, setIsExpanded] = React.useState(false)
  const [parseError, setParseError] = React.useState<string | null>(null)

  // Default options
  const {
    maxVisible = 3,
    showFileTypes = true,
    showLineNumbers = true,
    enableNavigation = true,
  } = options

  // Parse citations
  const parsedCitations = React.useMemo(() => {
    try {
      setParseError(null)
      return parseSourceCitations(sources)
    } catch (err) {
      const error = createResponseDisplayError(
        "CITATION_PARSE_ERROR",
        "Failed to parse source citations",
        err as Error,
        { sourceCount: sources.length }
      )
      setParseError(error.message)
      return { valid: [], invalid: [], total: 0 }
    }
  }, [sources])

  // Handle expand/collapse
  const handleToggleExpanded = React.useCallback(() => {
    const newExpanded = !isExpanded
    setIsExpanded(newExpanded)
    onToggleExpanded?.(newExpanded)
  }, [isExpanded, onToggleExpanded])

  // Determine which citations to show
  const visibleCitations = React.useMemo(() => {
    const allCitations = [...parsedCitations.valid, ...parsedCitations.invalid]
    if (isExpanded || allCitations.length <= maxVisible) {
      return allCitations
    }
    return allCitations.slice(0, maxVisible)
  }, [parsedCitations, isExpanded, maxVisible])

  const hasMore = parsedCitations.valid.length + parsedCitations.invalid.length > maxVisible
  const hiddenCount = (parsedCitations.valid.length + parsedCitations.invalid.length) - maxVisible

  // Loading state
  if (isLoading) {
    return (
      <div className={cn("space-y-2", className)}>
        <Skeleton className="h-4 w-20" />
        <div className="flex flex-wrap gap-2">
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-8 w-24" />
          <Skeleton className="h-8 w-28" />
        </div>
      </div>
    )
  }

  // Parse error state
  if (parseError) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          {parseError}
        </AlertDescription>
      </Alert>
    )
  }

  // No sources
  if (!sources.length) {
    return null
  }

  return (
    <div className={cn("space-y-3", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-muted-foreground">
            Sources
          </span>
          <Badge variant="secondary" className="text-xs">
            {parsedCitations.total}
          </Badge>
          {parsedCitations.invalid.length > 0 && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Badge variant="destructive" className="text-xs">
                    {parsedCitations.invalid.length} invalid
                  </Badge>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Some citations could not be parsed</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>

        {/* Expand/collapse button */}
        {hasMore && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleToggleExpanded}
            className="h-auto p-1 text-xs"
          >
            {isExpanded ? (
              <>
                <ChevronUp className="w-3 h-3 mr-1" />
                Show less
              </>
            ) : (
              <>
                <ChevronDown className="w-3 h-3 mr-1" />
                Show {hiddenCount} more
              </>
            )}
          </Button>
        )}
      </div>

      {/* Citations list */}
      <Collapsible open={isExpanded || !hasMore}>
        <div className="flex flex-wrap gap-2">
          {visibleCitations.map((citation, index) => (
            <CitationItem
              key={`${citation.raw}-${index}`}
              citation={citation}
              index={index}
              showLineNumbers={showLineNumbers}
              showFileTypes={showFileTypes}
              enableNavigation={enableNavigation}
              onSourceClick={onSourceClick}
              renderCitation={renderCitation}
            />
          ))}
        </div>

        {hasMore && (
          <CollapsibleContent>
            <div className="flex flex-wrap gap-2 mt-2">
              {parsedCitations.valid
                .concat(parsedCitations.invalid)
                .slice(maxVisible)
                .map((citation, index) => (
                  <CitationItem
                    key={`${citation.raw}-${index + maxVisible}`}
                    citation={citation}
                    index={index + maxVisible}
                    showLineNumbers={showLineNumbers}
                    showFileTypes={showFileTypes}
                    enableNavigation={enableNavigation}
                    onSourceClick={onSourceClick}
                    renderCitation={renderCitation}
                  />
                ))}
            </div>
          </CollapsibleContent>
        )}
      </Collapsible>
    </div>
  )
}
