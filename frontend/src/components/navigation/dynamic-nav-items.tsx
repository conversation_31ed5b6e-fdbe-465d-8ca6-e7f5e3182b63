/**
 * Dynamic Navigation Items
 * 
 * Provides navigation items with dynamic badges that update based on real-time data.
 * This component enhances the static navigation with live information like repository counts.
 */

"use client"

import * as React from "react"
import type { NavItems } from "./types"
import { NavItemss } from "./nav-items"
import { useRepositoryStats } from "@/hooks/api"

/**
 * Hook to get dynamic navigation items with live badges
 */
export function useDynamicNavItems(): NavItems[] {
  const { data: repositoryStats } = useRepositoryStats({
    refetchInterval: 30000, // Refresh every 30 seconds
    staleTime: 20000, // Consider data stale after 20 seconds
  })
  
  return React.useMemo(() => {
    return NavItemss.map((item) => {
      // Add dynamic badge for repositories
      if (item.path === "/app/repositories" && repositoryStats) {
        return {
          ...item,
          badge: repositoryStats.total_repositories.toString(),
        }
      }
      
      // For other items, you could add more dynamic badges here
      // For example, active ingestions count, error counts, etc.
      
      return item
    })
  }, [repositoryStats])
}

/**
 * Enhanced navigation items with dynamic badges
 */
export function DynamicNavItems() {
  const navItems = useDynamicNavItems()
  return navItems
}
