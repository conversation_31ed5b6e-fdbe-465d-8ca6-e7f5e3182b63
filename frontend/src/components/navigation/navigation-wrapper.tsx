/**
 * Navigation Wrapper Component
 * 
 * Client-side wrapper for the navigation that provides dynamic navigation items
 * with live badges and real-time updates.
 */

"use client"

import * as React from "react"
import { AppSidebar } from "./app-sidebar"
import { useDynamicNavItems } from "./dynamic-nav-items"

/**
 * Navigation wrapper that provides dynamic navigation items
 */
export function NavigationWrapper() {
  const navItems = useDynamicNavItems()
  
  return <AppSidebar navItems={navItems} />
}
