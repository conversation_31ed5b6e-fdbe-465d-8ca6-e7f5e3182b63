"use client"

import * as React from "react"
import { Check, ChevronDown, ChevronUp, ExternalLink, GitBranch, Search, X } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"

/**
 * Represents a repository option
 */
export interface Repository {
  url: string
  name: string
  owner: string
  description?: string
  isPrivate?: boolean
  lastUpdated?: Date
}

/**
 * Repository form data structure
 */
export interface RepositoryFormData {
  url: string
  branch?: string
  force_refresh?: boolean
  auth_token?: string
}

/**
 * Props for the RepositorySelector component
 */
export interface RepositorySelectorProps {
  /**
   * Current repository form data
   */
  value?: RepositoryFormData

  /**
   * Callback fired when repository selection changes
   */
  onChange?: (data: RepositoryFormData) => void

  /**
   * Whether the input is disabled
   */
  disabled?: boolean

  /**
   * Custom CSS class name
   */
  className?: string

  /**
   * Recently used repositories
   */
  recentRepositories?: Repository[]

  /**
   * Suggested repositories
   */
  suggestedRepositories?: Repository[]

  /**
   * Placeholder text for the input
   */
  placeholder?: string

  /**
   * Whether to show validation feedback
   */
  showValidation?: boolean

  /**
   * Whether to show advanced options (branch, auth, force refresh)
   */
  showAdvancedOptions?: boolean

  /**
   * Available branches for the repository (if known)
   */
  availableBranches?: string[]

  /**
   * Whether to show authentication options
   */
  showAuthOptions?: boolean
}

/**
 * RepositorySelector Component
 * 
 * GitHub repository URL input with validation and selection features:
 * - URL validation for GitHub repositories
 * - Recent repositories dropdown
 * - Repository suggestions
 * - Visual feedback for valid/invalid URLs
 * - Accessibility support
 * 
 * @example
 * ```tsx
 * <RepositorySelector
 *   value="https://github.com/owner/repo"
 *   onChange={(url) => console.log('Selected:', url)}
 *   recentRepositories={[
 *     { url: "https://github.com/owner/repo1", name: "repo1", owner: "owner" },
 *     { url: "https://github.com/owner/repo2", name: "repo2", owner: "owner" }
 *   ]}
 * />
 * ```
 */
export function RepositorySelector({
  value = { url: "", branch: "", force_refresh: false, auth_token: "" },
  onChange,
  disabled = false,
  className,
  recentRepositories = [],
  suggestedRepositories = [],
  placeholder = "https://github.com/owner/repository",
  showValidation = true,
  showAdvancedOptions = false,
  availableBranches = [],
  showAuthOptions = false,
}: RepositorySelectorProps) {
  const [isOpen, setIsOpen] = React.useState(false)
  const [formData, setFormData] = React.useState<RepositoryFormData>(value)
  const [showAdvanced, setShowAdvanced] = React.useState(showAdvancedOptions)
  
  // Validate GitHub URL
  const validateGitHubUrl = React.useCallback((url: string): boolean => {
    if (!url.trim()) return true // Empty is valid (optional field)
    
    try {
      const urlObj = new URL(url)
      return urlObj.hostname === "github.com" && 
             urlObj.pathname.split("/").filter(Boolean).length >= 2
    } catch {
      return false
    }
  }, [])
  
  // Parse repository info from URL
  const parseRepositoryInfo = React.useCallback((url: string): Repository | null => {
    try {
      const urlObj = new URL(url)
      if (urlObj.hostname !== "github.com") return null
      
      const pathParts = urlObj.pathname.split("/").filter(Boolean)
      if (pathParts.length < 2) return null
      
      const [owner, name] = pathParts
      return {
        url,
        owner,
        name,
      }
    } catch {
      return null
    }
  }, [])
  
  const isValid = validateGitHubUrl(formData.url)
  const repositoryInfo = parseRepositoryInfo(formData.url)

  // Handle form data changes
  const handleFormDataChange = React.useCallback((updates: Partial<RepositoryFormData>) => {
    const newFormData = { ...formData, ...updates }
    setFormData(newFormData)
    onChange?.(newFormData)
  }, [formData, onChange])

  // Handle URL input changes
  const handleUrlChange = React.useCallback((newUrl: string) => {
    handleFormDataChange({ url: newUrl })
  }, [handleFormDataChange])

  // Handle branch selection
  const handleBranchChange = React.useCallback((newBranch: string) => {
    handleFormDataChange({ branch: newBranch })
  }, [handleFormDataChange])

  // Handle repository selection from dropdown
  const handleRepositorySelect = React.useCallback((repository: Repository) => {
    handleFormDataChange({ url: repository.url })
    setIsOpen(false)
  }, [handleFormDataChange])

  // Clear selection
  const handleClear = React.useCallback(() => {
    const clearedData: RepositoryFormData = {
      url: "",
      branch: "",
      force_refresh: false,
      auth_token: ""
    }
    setFormData(clearedData)
    onChange?.(clearedData)
  }, [onChange])

  // Sync with external value changes
  React.useEffect(() => {
    if (JSON.stringify(value) !== JSON.stringify(formData)) {
      setFormData(value)
    }
  }, [value]) // Intentionally not including formData to avoid infinite loop
  
  // Combine recent and suggested repositories
  const allRepositories = React.useMemo(() => {
    const recent = recentRepositories.map(repo => ({ ...repo, type: "recent" as const }))
    const suggested = suggestedRepositories.map(repo => ({ ...repo, type: "suggested" as const }))
    
    // Remove duplicates based on URL
    const seen = new Set<string>()
    return [...recent, ...suggested].filter(repo => {
      if (seen.has(repo.url)) return false
      seen.add(repo.url)
      return true
    })
  }, [recentRepositories, suggestedRepositories])
  
  return (
    <div className={className}>
      <Label htmlFor="repository-input" className="text-sm font-medium">
        Repository (Optional)
      </Label>
      
      <div className="mt-2 space-y-2">
        <div className="relative">
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Input
                id="repository-input"
                type="url"
                placeholder={placeholder}
                value={formData.url}
                onChange={(e) => handleUrlChange(e.target.value)}
                disabled={disabled}
                className={cn(
                  "pr-20",
                  showValidation && formData.url && !isValid && "border-destructive focus-visible:ring-destructive/20",
                  showValidation && formData.url && isValid && "border-green-500 focus-visible:ring-green-500/20"
                )}
                aria-describedby="repository-description repository-validation"
              />
              
              {/* Clear button */}
              {formData.url && (
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={handleClear}
                  disabled={disabled}
                  className="absolute right-10 top-0 h-9 w-9"
                  aria-label="Clear repository"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
              
              {/* External link button */}
              {formData.url && isValid && (
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={() => window.open(formData.url, "_blank", "noopener,noreferrer")}
                  disabled={disabled}
                  className="absolute right-1 top-0 h-9 w-9"
                  aria-label="Open repository in new tab"
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
              )}
            </div>
            
            {/* Repository selector dropdown */}
            {allRepositories.length > 0 && (
              <Popover open={isOpen} onOpenChange={setIsOpen}>
                <PopoverTrigger asChild>
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    disabled={disabled}
                    aria-label="Select from recent repositories"
                    className="h-9 w-9 shrink-0"
                  >
                    <Search className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-0" align="end">
                  <Command>
                    <CommandInput placeholder="Search repositories..." />
                    <CommandList>
                      <CommandEmpty>No repositories found.</CommandEmpty>
                      
                      {recentRepositories.length > 0 && (
                        <CommandGroup heading="Recent">
                          {recentRepositories.map((repo) => (
                            <CommandItem
                              key={repo.url}
                              value={`${repo.owner}/${repo.name}`}
                              onSelect={() => handleRepositorySelect(repo)}
                              className="flex items-center justify-between"
                            >
                              <div className="flex items-center gap-2">
                                <GitBranch className="h-4 w-4" />
                                <div>
                                  <div className="font-medium">{repo.owner}/{repo.name}</div>
                                  {repo.description && (
                                    <div className="text-sm text-muted-foreground truncate">
                                      {repo.description}
                                    </div>
                                  )}
                                </div>
                              </div>
                              {repo.isPrivate && (
                                <Badge variant="secondary" className="text-xs">
                                  Private
                                </Badge>
                              )}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      )}
                      
                      {suggestedRepositories.length > 0 && (
                        <>
                          {recentRepositories.length > 0 && <Separator />}
                          <CommandGroup heading="Suggested">
                            {suggestedRepositories.map((repo) => (
                              <CommandItem
                                key={repo.url}
                                value={`${repo.owner}/${repo.name}`}
                                onSelect={() => handleRepositorySelect(repo)}
                                className="flex items-center justify-between"
                              >
                                <div className="flex items-center gap-2">
                                  <GitBranch className="h-4 w-4" />
                                  <div>
                                    <div className="font-medium">{repo.owner}/{repo.name}</div>
                                    {repo.description && (
                                      <div className="text-sm text-muted-foreground truncate">
                                        {repo.description}
                                      </div>
                                    )}
                                  </div>
                                </div>
                                {repo.isPrivate && (
                                  <Badge variant="secondary" className="text-xs">
                                    Private
                                  </Badge>
                                )}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </>
                      )}
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
            )}
          </div>
        </div>
        
        {/* Repository info display */}
        {repositoryInfo && isValid && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <GitBranch className="h-4 w-4" />
            <span>{repositoryInfo.owner}/{repositoryInfo.name}</span>
            <Check className="h-4 w-4 text-green-500" />
          </div>
        )}
        
        {/* Validation feedback */}
        {showValidation && formData.url && !isValid && (
          <p id="repository-validation" className="text-sm text-destructive">
            Please enter a valid GitHub repository URL (e.g., https://github.com/owner/repo)
          </p>
        )}

        {/* Advanced Options Toggle */}
        {(showAdvancedOptions || formData.url) && (
          <div className="flex items-center gap-2">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="h-auto p-1 text-xs text-muted-foreground hover:text-foreground"
            >
              {showAdvanced ? "Hide" : "Show"} Advanced Options
              {showAdvanced ? <ChevronUp className="ml-1 h-3 w-3" /> : <ChevronDown className="ml-1 h-3 w-3" />}
            </Button>
          </div>
        )}

        {/* Advanced Options */}
        {showAdvanced && (
          <div className="space-y-3 rounded-md border p-3 bg-muted/30">
            <div className="text-sm font-medium text-foreground">Advanced Options</div>

            {/* Branch Selection */}
            <div className="space-y-2">
              <Label htmlFor="branch-select" className="text-sm">
                Branch (optional)
              </Label>
              <div className="flex gap-2">
                <Input
                  id="branch-select"
                  placeholder="main, develop, feature/..."
                  value={formData.branch || ""}
                  onChange={(e) => handleBranchChange(e.target.value)}
                  disabled={disabled}
                  className="flex-1"
                />
                {availableBranches.length > 0 && (
                  <Select value={formData.branch || ""} onValueChange={handleBranchChange}>
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">Default</SelectItem>
                      {availableBranches.map((branch) => (
                        <SelectItem key={branch} value={branch}>
                          {branch}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                Specify a branch to ingest. Leave empty to use the default branch.
              </p>
            </div>

            {/* Authentication Token */}
            {showAuthOptions && (
              <div className="space-y-2">
                <Label htmlFor="auth-token" className="text-sm">
                  Authentication Token (optional)
                </Label>
                <Input
                  id="auth-token"
                  type="password"
                  placeholder="ghp_xxxxxxxxxxxxxxxxxxxx"
                  value={formData.auth_token || ""}
                  onChange={(e) => handleFormDataChange({ auth_token: e.target.value })}
                  disabled={disabled}
                />
                <p className="text-xs text-muted-foreground">
                  GitHub personal access token for private repositories.
                </p>
              </div>
            )}

            {/* Force Refresh */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="force-refresh"
                checked={formData.force_refresh || false}
                onCheckedChange={(checked) => handleFormDataChange({ force_refresh: !!checked })}
                disabled={disabled}
              />
              <Label htmlFor="force-refresh" className="text-sm">
                Force refresh existing data
              </Label>
            </div>
            <p className="text-xs text-muted-foreground">
              Re-process the repository even if it has been ingested before.
            </p>
          </div>
        )}

        {/* Description */}
        <p id="repository-description" className="text-sm text-muted-foreground">
          Specify a particular repository to focus the query on. Leave empty to search across all ingested repositories.
        </p>
      </div>
    </div>
  )
}
