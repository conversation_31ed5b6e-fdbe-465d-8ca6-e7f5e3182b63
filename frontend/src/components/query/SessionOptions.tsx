"use client"

import * as React from "react"
import { MessageCircle, Plus, RotateCcw, <PERSON><PERSON><PERSON>, Trash2, User } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"

import { useSession } from "@/providers/session-provider"
import { useToast } from "@/hooks/useToast"
import { cn } from "@/lib/utils"

/**
 * Props for the SessionOptions component
 */
export interface SessionOptionsProps {
  /**
   * Whether to show advanced session options
   */
  showAdvanced?: boolean
  
  /**
   * Custom CSS class name
   */
  className?: string
  
  /**
   * Whether to show session statistics
   */
  showStats?: boolean
  
  /**
   * Whether to allow session management actions
   */
  allowManagement?: boolean
}

/**
 * SessionOptions Component
 * 
 * Session management UI integrated with the existing SessionProvider:
 * - Display current session information
 * - Create new sessions
 * - Clear conversation history
 * - User ID management
 * - Session statistics
 * - Accessibility support
 * 
 * @example
 * ```tsx
 * <SessionOptions
 *   showAdvanced={true}
 *   showStats={true}
 *   allowManagement={true}
 * />
 * ```
 */
export function SessionOptions({
  showAdvanced = false,
  className,
  showStats = true,
  allowManagement = true,
}: SessionOptionsProps) {
  const session = useSession()
  const { toast } = useToast()
  const [userIdInput, setUserIdInput] = React.useState(session.session.userId || "")
  const [isEditingUserId, setIsEditingUserId] = React.useState(false)
  
  // Handle creating new session
  const handleNewSession = React.useCallback(async () => {
    try {
      await session.createSession(userIdInput || undefined)
      toast({
        title: "New session created",
        description: "Started a fresh conversation session",
      })
    } catch (error) {
      toast({
        title: "Failed to create session",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      })
    }
  }, [session, userIdInput, toast])
  
  // Handle clearing conversation history
  const handleClearHistory = React.useCallback(() => {
    session.clearHistory()
    toast({
      title: "Conversation cleared",
      description: "All messages have been removed from this session",
    })
  }, [session, toast])
  
  // Handle ending current session
  const handleEndSession = React.useCallback(() => {
    session.endSession()
    toast({
      title: "Session ended",
      description: "The current session has been terminated",
    })
  }, [session, toast])
  
  // Handle user ID update
  const handleUpdateUserId = React.useCallback(() => {
    if (userIdInput.trim() !== session.session.userId) {
      session.setUserId(userIdInput.trim() || null)
      toast({
        title: "User ID updated",
        description: userIdInput.trim() ? `Set to: ${userIdInput.trim()}` : "Cleared user ID",
      })
    }
    setIsEditingUserId(false)
  }, [session, userIdInput, toast])
  
  // Format session duration
  const formatDuration = React.useCallback((startTime: Date) => {
    const now = new Date()
    const diff = now.getTime() - startTime.getTime()
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(minutes / 60)
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`
    }
    return `${minutes}m`
  }, [])
  
  // Sync user ID input with session
  React.useEffect(() => {
    if (!isEditingUserId) {
      setUserIdInput(session.session.userId || "")
    }
  }, [session.session.userId, isEditingUserId])
  
  return (
    <Card className={className}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-base flex items-center gap-2">
              <MessageCircle className="h-4 w-4" />
              Session Management
            </CardTitle>
            <CardDescription>
              Manage your conversation session and context
            </CardDescription>
          </div>
          <Badge variant={session.isActive ? "default" : "secondary"}>
            {session.isActive ? "Active" : "Inactive"}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Current Session Info */}
        {session.isActive && (
          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <Label className="text-muted-foreground">Session ID</Label>
                <p className="font-mono text-xs truncate" title={session.session.sessionId || ""}>
                  {session.session.sessionId || "None"}
                </p>
              </div>
              <div>
                <Label className="text-muted-foreground">Duration</Label>
                <p className="text-xs">
                  {session.session.createdAt 
                    ? formatDuration(session.session.createdAt)
                    : "Unknown"
                  }
                </p>
              </div>
            </div>
            
            {showStats && (
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <Label className="text-muted-foreground">Messages</Label>
                  <p className="text-xs">{session.messageCount}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Last Activity</Label>
                  <p className="text-xs">
                    {session.lastActivity 
                      ? new Date(session.lastActivity).toLocaleTimeString()
                      : "None"
                    }
                  </p>
                </div>
              </div>
            )}
          </div>
        )}
        
        {/* User ID Management */}
        {showAdvanced && (
          <>
            <Separator />
            <div className="space-y-2">
              <Label htmlFor="user-id-input">User ID (Optional)</Label>
              <div className="flex gap-2">
                <Input
                  id="user-id-input"
                  placeholder="Enter user identifier"
                  value={userIdInput}
                  onChange={(e) => setUserIdInput(e.target.value)}
                  disabled={!isEditingUserId}
                  className={cn(!isEditingUserId && "bg-muted")}
                />
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={() => {
                    if (isEditingUserId) {
                      handleUpdateUserId()
                    } else {
                      setIsEditingUserId(true)
                    }
                  }}
                  aria-label={isEditingUserId ? "Save user ID" : "Edit user ID"}
                >
                  {isEditingUserId ? <User className="h-4 w-4" /> : <Settings className="h-4 w-4" />}
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                Used for personalization and tracking across sessions
              </p>
            </div>
          </>
        )}
        
        {/* Session Actions */}
        {allowManagement && (
          <>
            <Separator />
            <div className="space-y-2">
              <Label className="text-sm font-medium">Actions</Label>
              <div className="flex flex-wrap gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleNewSession}
                  className="h-8"
                >
                  <Plus className="mr-2 h-3 w-3" />
                  New Session
                </Button>
                
                {session.isActive && session.messageCount > 0 && (
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        className="h-8"
                      >
                        <RotateCcw className="mr-2 h-3 w-3" />
                        Clear History
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Clear Conversation History</AlertDialogTitle>
                        <AlertDialogDescription>
                          This will remove all messages from the current session. 
                          This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={handleClearHistory}>
                          Clear History
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                )}
                
                {session.isActive && (
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        className="h-8 text-destructive hover:text-destructive"
                      >
                        <Trash2 className="mr-2 h-3 w-3" />
                        End Session
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>End Current Session</AlertDialogTitle>
                        <AlertDialogDescription>
                          This will terminate the current session and clear all conversation data. 
                          This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction 
                          onClick={handleEndSession}
                          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                          End Session
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                )}
              </div>
            </div>
          </>
        )}
        
        {/* No Active Session */}
        {!session.isActive && (
          <div className="text-center py-4 text-muted-foreground">
            <MessageCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No active session</p>
            <p className="text-xs">Create a new session to start a conversation</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
