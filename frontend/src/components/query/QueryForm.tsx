"use client"

import * as React from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Loader2, Send, AlertCircle } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"

import { useQueryMutation } from "@/hooks/api"
import { useSession } from "@/providers/session-provider"
import { useToast } from "@/hooks/useToast"
import type { QueryRequest } from "@/types/api"
import { QueryRequestSchema } from "@/types/api/schemas"

/**
 * Form validation schema for query submission
 * Extends the base QueryRequest schema with additional UI validation
 */
const QueryFormSchema = QueryRequestSchema.extend({
  query: z.string()
    .min(1, "Query is required")
    .max(5000, "Query must be less than 5000 characters")
    .refine((val) => val.trim().length > 0, "Query cannot be empty"),
  
  repository: z.string()
    .url("Must be a valid URL")
    .optional()
    .or(z.literal("")),
    
  context_filters: z.record(z.string(), z.unknown())
    .optional()
    .default({}),
})

type QueryFormData = z.infer<typeof QueryFormSchema>

/**
 * Props for the QueryForm component
 */
export interface QueryFormProps {
  /**
   * Callback fired when a query is successfully submitted
   */
  onQuerySuccess?: (response: any) => void
  
  /**
   * Callback fired when query submission fails
   */
  onQueryError?: (error: Error) => void
  
  /**
   * Initial values for the form
   */
  initialValues?: Partial<QueryFormData>
  
  /**
   * Whether to show advanced options (context filters, repository)
   */
  showAdvancedOptions?: boolean
  
  /**
   * Custom CSS class name
   */
  className?: string
}

/**
 * QueryForm Component
 * 
 * Main form component for submitting natural language queries to the multi-agent system.
 * Supports all QueryRequest parameters with comprehensive validation, session integration,
 * and accessibility features.
 * 
 * Features:
 * - Real-time validation with Zod schema
 * - Session management integration
 * - Character count and limits
 * - Advanced options (context filters, repository)
 * - Loading states and error handling
 * - Keyboard navigation and accessibility
 * 
 * @example
 * ```tsx
 * <QueryForm
 *   onQuerySuccess={(response) => console.log('Success:', response)}
 *   onQueryError={(error) => console.error('Error:', error)}
 *   showAdvancedOptions={true}
 * />
 * ```
 */
export function QueryForm({
  onQuerySuccess,
  onQueryError,
  initialValues = {},
  showAdvancedOptions = false,
  className,
}: QueryFormProps) {
  const { toast } = useToast()
  const session = useSession()
  
  // Form setup with validation
  const form = useForm<QueryFormData>({
    resolver: zodResolver(QueryFormSchema),
    defaultValues: {
      query: "",
      session_id: session.session.sessionId || undefined,
      user_id: session.session.userId || undefined,
      repository: "",
      context_filters: {},
      ...initialValues,
    },
  })
  
  // Query mutation hook
  const queryMutation = useQueryMutation({
    onSuccess: (response, variables) => {
      // Add message to session history
      session.addMessage({
        type: "user",
        content: variables.query,
        metadata: {
          timestamp: new Date(),
          query_params: variables,
        },
      })
      
      session.addMessage({
        type: "assistant",
        content: response.result_markdown,
        metadata: {
          timestamp: new Date(),
          agent_type: response.agent_type,
          confidence: response.confidence,
          sources: response.sources,
          processing_time: response.processing_time,
        },
      })
      
      // Reset form
      form.reset({
        query: "",
        session_id: session.session.sessionId,
        user_id: session.session.userId,
        repository: form.getValues("repository"),
        context_filters: form.getValues("context_filters"),
      })
      
      // Show success toast
      toast({
        title: "Query submitted successfully",
        description: `Processed by ${response.agent_type} agent`,
      })
      
      // Call success callback
      onQuerySuccess?.(response)
    },
    
    onError: (error, variables) => {
      // Show error toast
      toast({
        title: "Query submission failed",
        description: error.message,
        variant: "destructive",
      })
      
      // Call error callback
      onQueryError?.(error)
    },
  })
  
  // Form submission handler
  const onSubmit = async (data: QueryFormData) => {
    // Ensure we have a session
    if (!session.isActive) {
      await session.createSession(data.user_id)
    }
    
    // Prepare request data
    const request: QueryRequest = {
      query: data.query.trim(),
      session_id: session.session.sessionId || undefined,
      user_id: data.user_id || session.session.userId || undefined,
      repository: data.repository || undefined,
      context_filters: Object.keys(data.context_filters || {}).length > 0 
        ? data.context_filters 
        : undefined,
    }
    
    // Submit query
    queryMutation.mutate(request)
  }
  
  // Watch query field for character count
  const queryValue = form.watch("query")
  const characterCount = queryValue?.length || 0
  const isNearLimit = characterCount > 4500
  const isAtLimit = characterCount >= 5000
  
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Submit Query</CardTitle>
        <CardDescription>
          Ask questions about your codebase using natural language. 
          The system will route your query to the most appropriate agent.
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Main Query Input */}
            <FormField
              control={form.control}
              name="query"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Query</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="How does the authentication system work? What are the main components of the user management module?"
                      className="min-h-[120px] resize-none"
                      {...field}
                      aria-describedby="query-description query-character-count"
                    />
                  </FormControl>
                  <div className="flex justify-between items-center">
                    <FormDescription id="query-description">
                      Describe what you want to know about the codebase
                    </FormDescription>
                    <span 
                      id="query-character-count"
                      className={`text-sm ${
                        isAtLimit 
                          ? "text-destructive" 
                          : isNearLimit 
                            ? "text-orange-500" 
                            : "text-muted-foreground"
                      }`}
                      aria-live="polite"
                    >
                      {characterCount}/5000
                    </span>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {/* Advanced Options */}
            {showAdvancedOptions && (
              <>
                <Separator />
                
                <div className="space-y-4">
                  <h4 className="text-sm font-medium">Advanced Options</h4>
                  
                  {/* Repository Selection */}
                  <FormField
                    control={form.control}
                    name="repository"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Repository (Optional)</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="https://github.com/owner/repository"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Specify a particular repository to focus the query on
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </>
            )}
            
            {/* Error Display */}
            {queryMutation.error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {queryMutation.error.message}
                </AlertDescription>
              </Alert>
            )}
            
            {/* Submit Button */}
            <div className="flex justify-end">
              <Button
                type="submit"
                disabled={queryMutation.isPending || isAtLimit || !form.formState.isValid}
                className="min-w-[120px]"
              >
                {queryMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <Send className="mr-2 h-4 w-4" />
                    Submit Query
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
