"use client"

import * as React from "react"
import { Plus, X, AlertCircle } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

/**
 * Represents a single context filter key-value pair
 */
export interface ContextFilter {
  id: string
  key: string
  value: string
  error?: string
}

/**
 * Props for the ContextFiltersInput component
 */
export interface ContextFiltersInputProps {
  /**
   * Current context filters value
   */
  value?: Record<string, unknown>
  
  /**
   * Callback fired when context filters change
   */
  onChange?: (filters: Record<string, unknown>) => void
  
  /**
   * Whether the input is disabled
   */
  disabled?: boolean
  
  /**
   * Custom CSS class name
   */
  className?: string
  
  /**
   * Maximum number of filters allowed
   */
  maxFilters?: number
  
  /**
   * Predefined filter suggestions
   */
  suggestions?: Array<{
    key: string
    value: string
    description: string
  }>
}

/**
 * ContextFiltersInput Component
 * 
 * Flexible key-value pair input component for context_filters with features like:
 * - Dynamic add/remove of filter pairs
 * - JSON value validation
 * - Predefined suggestions
 * - Error handling and validation
 * - Accessibility support
 * 
 * @example
 * ```tsx
 * <ContextFiltersInput
 *   value={{ file_types: ["py", "ts"], max_results: 10 }}
 *   onChange={(filters) => console.log('Filters:', filters)}
 *   suggestions={[
 *     { key: "file_types", value: '["py", "ts"]', description: "Filter by file extensions" },
 *     { key: "max_results", value: "10", description: "Limit number of results" }
 *   ]}
 * />
 * ```
 */
export function ContextFiltersInput({
  value = {},
  onChange,
  disabled = false,
  className,
  maxFilters = 10,
  suggestions = [],
}: ContextFiltersInputProps) {
  // Convert value object to filter array for editing
  const [filters, setFilters] = React.useState<ContextFilter[]>(() => {
    return Object.entries(value).map(([key, val], index) => ({
      id: `filter-${index}`,
      key,
      value: typeof val === "string" ? val : JSON.stringify(val),
    }))
  })
  
  // Generate unique ID for new filters
  const generateId = React.useCallback(() => {
    return `filter-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }, [])
  
  // Convert filters array back to object and notify parent
  const updateParent = React.useCallback((newFilters: ContextFilter[]) => {
    const filtersObject: Record<string, unknown> = {}
    
    newFilters.forEach(filter => {
      if (filter.key.trim() && filter.value.trim()) {
        try {
          // Try to parse as JSON first
          const parsedValue = JSON.parse(filter.value)
          filtersObject[filter.key.trim()] = parsedValue
        } catch {
          // If not valid JSON, store as string
          filtersObject[filter.key.trim()] = filter.value.trim()
        }
      }
    })
    
    onChange?.(filtersObject)
  }, [onChange])
  
  // Add new filter
  const addFilter = React.useCallback(() => {
    if (filters.length >= maxFilters) return
    
    const newFilter: ContextFilter = {
      id: generateId(),
      key: "",
      value: "",
    }
    
    const newFilters = [...filters, newFilter]
    setFilters(newFilters)
  }, [filters, maxFilters, generateId])
  
  // Remove filter by ID
  const removeFilter = React.useCallback((id: string) => {
    const newFilters = filters.filter(filter => filter.id !== id)
    setFilters(newFilters)
    updateParent(newFilters)
  }, [filters, updateParent])
  
  // Update filter key or value
  const updateFilter = React.useCallback((id: string, field: "key" | "value", newValue: string) => {
    const newFilters = filters.map(filter => {
      if (filter.id === id) {
        const updatedFilter = { ...filter, [field]: newValue }
        
        // Validate JSON for value field
        if (field === "value" && newValue.trim()) {
          try {
            JSON.parse(newValue)
            updatedFilter.error = undefined
          } catch {
            // Allow non-JSON strings, but mark complex values that might need JSON
            if (newValue.includes("{") || newValue.includes("[") || newValue.includes('"')) {
              updatedFilter.error = "Invalid JSON format"
            } else {
              updatedFilter.error = undefined
            }
          }
        }
        
        return updatedFilter
      }
      return filter
    })
    
    setFilters(newFilters)
    updateParent(newFilters)
  }, [filters, updateParent])
  
  // Add suggestion as new filter
  const addSuggestion = React.useCallback((suggestion: typeof suggestions[0]) => {
    if (filters.length >= maxFilters) return
    
    const newFilter: ContextFilter = {
      id: generateId(),
      key: suggestion.key,
      value: suggestion.value,
    }
    
    const newFilters = [...filters, newFilter]
    setFilters(newFilters)
    updateParent(newFilters)
  }, [filters, maxFilters, generateId, updateParent])
  
  // Sync with external value changes
  React.useEffect(() => {
    const externalFilters = Object.entries(value).map(([key, val], index) => ({
      id: `filter-${index}`,
      key,
      value: typeof val === "string" ? val : JSON.stringify(val),
    }))
    
    // Only update if different from current state
    const currentKeys = filters.map(f => f.key).sort()
    const externalKeys = externalFilters.map(f => f.key).sort()
    
    if (JSON.stringify(currentKeys) !== JSON.stringify(externalKeys)) {
      setFilters(externalFilters)
    }
  }, [value]) // Intentionally not including filters to avoid infinite loop
  
  return (
    <Card className={className}>
      <CardHeader className="pb-4">
        <CardTitle className="text-base">Context Filters</CardTitle>
        <CardDescription>
          Add key-value pairs to customize query behavior. Values can be strings, numbers, arrays, or objects.
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Existing Filters */}
        {filters.length > 0 && (
          <div className="space-y-3">
            {filters.map((filter) => (
              <div key={filter.id} className="flex gap-2 items-start">
                <div className="flex-1 grid grid-cols-2 gap-2">
                  <div>
                    <Label htmlFor={`${filter.id}-key`} className="sr-only">
                      Filter key
                    </Label>
                    <Input
                      id={`${filter.id}-key`}
                      placeholder="Key (e.g., file_types)"
                      value={filter.key}
                      onChange={(e) => updateFilter(filter.id, "key", e.target.value)}
                      disabled={disabled}
                      className={cn(
                        filter.error && "border-destructive focus-visible:ring-destructive/20"
                      )}
                    />
                  </div>
                  <div>
                    <Label htmlFor={`${filter.id}-value`} className="sr-only">
                      Filter value
                    </Label>
                    <Input
                      id={`${filter.id}-value`}
                      placeholder='Value (e.g., ["py", "ts"] or "10")'
                      value={filter.value}
                      onChange={(e) => updateFilter(filter.id, "value", e.target.value)}
                      disabled={disabled}
                      className={cn(
                        filter.error && "border-destructive focus-visible:ring-destructive/20"
                      )}
                    />
                  </div>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={() => removeFilter(filter.id)}
                  disabled={disabled}
                  aria-label={`Remove filter ${filter.key || "empty"}`}
                  className="mt-0 h-9 w-9 shrink-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        )}
        
        {/* Error Display */}
        {filters.some(f => f.error) && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Some filter values have invalid JSON format. Please check the highlighted fields.
            </AlertDescription>
          </Alert>
        )}
        
        {/* Add Filter Button */}
        <div className="flex justify-between items-center">
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addFilter}
            disabled={disabled || filters.length >= maxFilters}
            className="h-8"
          >
            <Plus className="mr-2 h-3 w-3" />
            Add Filter
          </Button>
          
          {filters.length >= maxFilters && (
            <span className="text-sm text-muted-foreground">
              Maximum {maxFilters} filters allowed
            </span>
          )}
        </div>
        
        {/* Suggestions */}
        {suggestions.length > 0 && filters.length < maxFilters && (
          <div className="space-y-2">
            <Label className="text-sm font-medium">Common Filters:</Label>
            <div className="flex flex-wrap gap-2">
              {suggestions.map((suggestion, index) => (
                <Badge
                  key={index}
                  variant="secondary"
                  className="cursor-pointer hover:bg-secondary/80"
                  onClick={() => addSuggestion(suggestion)}
                  title={suggestion.description}
                >
                  {suggestion.key}: {suggestion.value}
                </Badge>
              ))}
            </div>
          </div>
        )}
        
        {/* Help Text */}
        {filters.length === 0 && (
          <div className="text-sm text-muted-foreground text-center py-4 border-2 border-dashed border-muted rounded-lg">
            No context filters added. Click "Add Filter" to customize query behavior.
          </div>
        )}
      </CardContent>
    </Card>
  )
}
