"use client"

import * as React from "react"
import { forwardRef } from "react"
import { Textarea } from "@/components/ui/textarea"
import { cn } from "@/lib/utils"

/**
 * Props for the QueryTextarea component
 */
export interface QueryTextareaProps extends React.ComponentProps<typeof Textarea> {
  /**
   * Maximum character limit
   */
  maxLength?: number
  
  /**
   * Whether to show character count
   */
  showCharacterCount?: boolean
  
  /**
   * Character count warning threshold (percentage of maxLength)
   */
  warningThreshold?: number
  
  /**
   * Callback fired when character count changes
   */
  onCharacterCountChange?: (count: number, isNearLimit: boolean, isAtLimit: boolean) => void
  
  /**
   * Whether to auto-resize the textarea
   */
  autoResize?: boolean
  
  /**
   * Minimum height for auto-resize
   */
  minHeight?: number
  
  /**
   * Maximum height for auto-resize
   */
  maxHeight?: number
}

/**
 * QueryTextarea Component
 * 
 * Enhanced textarea component specifically designed for query input with features like:
 * - Character counting and limits
 * - Auto-resize functionality
 * - Accessibility enhancements
 * - Keyboard shortcuts
 * - Visual feedback for character limits
 * 
 * @example
 * ```tsx
 * <QueryTextarea
 *   placeholder="Enter your query..."
 *   maxLength={5000}
 *   showCharacterCount={true}
 *   autoResize={true}
 *   onCharacterCountChange={(count, isNearLimit, isAtLimit) => {
 *     console.log(`Characters: ${count}, Near limit: ${isNearLimit}, At limit: ${isAtLimit}`)
 *   }}
 * />
 * ```
 */
export const QueryTextarea = forwardRef<HTMLTextAreaElement, QueryTextareaProps>(
  ({
    className,
    maxLength = 5000,
    showCharacterCount = true,
    warningThreshold = 0.9,
    onCharacterCountChange,
    autoResize = true,
    minHeight = 120,
    maxHeight = 400,
    value,
    onChange,
    ...props
  }, ref) => {
    const textareaRef = React.useRef<HTMLTextAreaElement>(null)
    const [characterCount, setCharacterCount] = React.useState(0)
    
    // Combine refs
    React.useImperativeHandle(ref, () => textareaRef.current!, [])
    
    // Calculate character count and limits
    const currentValue = value?.toString() || ""
    const count = currentValue.length
    const warningLimit = Math.floor(maxLength * warningThreshold)
    const isNearLimit = count >= warningLimit
    const isAtLimit = count >= maxLength
    
    // Update character count when value changes
    React.useEffect(() => {
      if (count !== characterCount) {
        setCharacterCount(count)
        onCharacterCountChange?.(count, isNearLimit, isAtLimit)
      }
    }, [count, characterCount, isNearLimit, isAtLimit, onCharacterCountChange])
    
    // Auto-resize functionality
    const handleAutoResize = React.useCallback(() => {
      const textarea = textareaRef.current
      if (!textarea || !autoResize) return
      
      // Reset height to auto to get the correct scrollHeight
      textarea.style.height = "auto"
      
      // Calculate new height within bounds
      const newHeight = Math.min(
        Math.max(textarea.scrollHeight, minHeight),
        maxHeight
      )
      
      textarea.style.height = `${newHeight}px`
    }, [autoResize, minHeight, maxHeight])
    
    // Handle input changes
    const handleChange = React.useCallback((event: React.ChangeEvent<HTMLTextAreaElement>) => {
      const newValue = event.target.value
      
      // Prevent input if at character limit
      if (newValue.length > maxLength) {
        return
      }
      
      // Call original onChange
      onChange?.(event)
      
      // Auto-resize after state update
      setTimeout(handleAutoResize, 0)
    }, [onChange, maxLength, handleAutoResize])
    
    // Handle keyboard shortcuts
    const handleKeyDown = React.useCallback((event: React.KeyboardEvent<HTMLTextAreaElement>) => {
      // Ctrl/Cmd + Enter to submit (if parent form exists)
      if ((event.ctrlKey || event.metaKey) && event.key === "Enter") {
        event.preventDefault()
        
        // Find and submit parent form
        const form = textareaRef.current?.closest("form")
        if (form) {
          const submitButton = form.querySelector('button[type="submit"]') as HTMLButtonElement
          if (submitButton && !submitButton.disabled) {
            submitButton.click()
          }
        }
      }
      
      // Call original onKeyDown
      props.onKeyDown?.(event)
    }, [props])
    
    // Auto-resize on mount and value changes
    React.useEffect(() => {
      handleAutoResize()
    }, [handleAutoResize, value])
    
    // Character count color based on limits
    const getCharacterCountColor = () => {
      if (isAtLimit) return "text-destructive"
      if (isNearLimit) return "text-orange-500"
      return "text-muted-foreground"
    }
    
    return (
      <div className="relative">
        <Textarea
          ref={textareaRef}
          className={cn(
            "resize-none transition-all duration-200",
            autoResize && "overflow-hidden",
            isAtLimit && "border-destructive focus-visible:ring-destructive/20",
            isNearLimit && !isAtLimit && "border-orange-500 focus-visible:ring-orange-500/20",
            className
          )}
          style={{
            minHeight: autoResize ? `${minHeight}px` : undefined,
            maxHeight: autoResize ? `${maxHeight}px` : undefined,
          }}
          value={value}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          maxLength={maxLength}
          aria-describedby={showCharacterCount ? "character-count" : undefined}
          {...props}
        />
        
        {showCharacterCount && (
          <div className="flex justify-between items-center mt-2">
            <div className="text-sm text-muted-foreground">
              {props.placeholder && (
                <span>Tip: Use Ctrl+Enter to submit</span>
              )}
            </div>
            <span
              id="character-count"
              className={cn("text-sm font-medium", getCharacterCountColor())}
              aria-live="polite"
              aria-label={`Character count: ${count} of ${maxLength}`}
            >
              {count.toLocaleString()}/{maxLength.toLocaleString()}
            </span>
          </div>
        )}
        
        {/* Visual indicator for character limit */}
        {showCharacterCount && (
          <div className="mt-1 h-1 bg-muted rounded-full overflow-hidden">
            <div
              className={cn(
                "h-full transition-all duration-300",
                isAtLimit 
                  ? "bg-destructive" 
                  : isNearLimit 
                    ? "bg-orange-500" 
                    : "bg-primary"
              )}
              style={{
                width: `${Math.min((count / maxLength) * 100, 100)}%`,
              }}
              aria-hidden="true"
            />
          </div>
        )}
      </div>
    )
  }
)

QueryTextarea.displayName = "QueryTextarea"
