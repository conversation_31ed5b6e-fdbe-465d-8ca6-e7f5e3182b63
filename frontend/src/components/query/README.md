# Query Interface Components

A comprehensive set of React components for building query interfaces in the LLM RAG Codebase Query System.

## Overview

This package provides three main component groups:

- **Query Components** - Form components for submitting queries
- **Conversation Components** - Display and manage conversation history  
- **Example Components** - Showcase example queries for each agent type

## Quick Start

```tsx
import { QueryForm, ConversationHistory, QueryExamples } from "@/components"
import { SessionProvider, QueryProvider } from "@/providers"
import { queryExamples } from "@/data/queryExamples"

export function App() {
  return (
    <QueryProvider>
      <SessionProvider>
        <div className="space-y-6">
          <QueryForm showAdvancedOptions={true} />
          <ConversationHistory maxHeight={600} />
          <QueryExamples examples={queryExamples} groupByAgent={true} />
        </div>
      </SessionProvider>
    </QueryProvider>
  )
}
```

## Components

### QueryForm

Main form component for submitting natural language queries.

```tsx
<QueryForm
  onQuerySuccess={(response) => console.log('Success:', response)}
  onQueryError={(error) => console.error('Error:', error)}
  showAdvancedOptions={true}
/>
```

**Key Features:**
- Real-time validation with Zod schema
- Character count with visual feedback
- Session integration
- Advanced options (repository, context filters)
- Accessibility support

### ConversationHistory

Display conversation message history with rich formatting.

```tsx
<ConversationHistory
  maxHeight={600}
  showTimestamps={true}
  autoScroll={true}
  onMessageSelect={(message, index) => console.log('Selected:', message)}
/>
```

**Key Features:**
- Integration with SessionProvider
- Auto-scroll to new messages
- Message threading and timestamps
- Responsive design

### QueryExamples

Showcase example queries with search and filtering.

```tsx
<QueryExamples
  examples={queryExamples}
  onQueryInsert={(query) => setQueryValue(query)}
  showSearch={true}
  groupByAgent={true}
/>
```

**Key Features:**
- Agent-specific example categorization
- Search and filtering functionality
- Grid and list view modes
- Example insertion into query form

## Architecture

### Design Principles

All components follow SOLID principles:

- **Single Responsibility**: Each component has a focused purpose
- **Open/Closed**: Extensible through props without modification
- **Liskov Substitution**: Components can be replaced with compatible implementations
- **Interface Segregation**: Props are specific to component needs
- **Dependency Inversion**: Components depend on abstractions

### Provider Integration

Components integrate with existing providers:

```tsx
import { SessionProvider } from "@/providers/session-provider"
import { QueryProvider } from "@/providers/query-provider"

// Wrap your app with providers
<QueryProvider>
  <SessionProvider>
    {/* Your components */}
  </SessionProvider>
</QueryProvider>
```

### API Integration

Components use existing API hooks:

```tsx
import { useQueryMutation } from "@/hooks/api"

// Used internally by QueryForm
const queryMutation = useQueryMutation({
  onSuccess: (response) => {
    // Handle success
  },
  onError: (error) => {
    // Handle error
  }
})
```

## Validation

Form components use Zod for validation:

```tsx
import { z } from "zod"
import { QueryRequestSchema } from "@/types/api/schemas"

const QueryFormSchema = QueryRequestSchema.extend({
  query: z.string()
    .min(1, "Query is required")
    .max(5000, "Query must be less than 5000 characters"),
})
```

## Accessibility

All components implement comprehensive accessibility features:

- **Keyboard Navigation**: Full keyboard support
- **Screen Reader Support**: Proper ARIA labels and descriptions
- **Focus Management**: Logical focus order and visible indicators
- **Color Contrast**: WCAG AA compliance
- **Live Regions**: Dynamic content announcements

## Testing

Comprehensive test coverage includes:

```bash
# Run all tests
pnpm test

# Run specific component tests
pnpm test components/query
pnpm test components/conversation
pnpm test components/examples

# Run integration tests
pnpm test integration/QueryInterface
```

## Styling

Components use Tailwind CSS with design system tokens:

```tsx
// Custom styling example
<QueryForm className="custom-query-form" />
```

```css
.custom-query-form {
  --primary-color: #your-brand-color;
  --border-radius: 12px;
}
```

## Performance

Optimizations include:

- **Memoization**: React.memo and useMemo for expensive operations
- **Code Splitting**: Lazy loading where appropriate
- **Debounced Search**: Search input debouncing
- **Virtual Scrolling**: For large conversation histories

## Examples

### Basic Usage

```tsx
import { QueryForm } from "@/components/query"

export function SimpleQuery() {
  return (
    <QueryForm
      onQuerySuccess={(response) => {
        console.log("Query successful:", response)
      }}
      onQueryError={(error) => {
        console.error("Query failed:", error)
      }}
    />
  )
}
```

### Advanced Usage

```tsx
import { QueryForm, ConversationHistory } from "@/components"
import { useToast } from "@/hooks/useToast"

export function AdvancedQuery() {
  const { toast } = useToast()

  return (
    <div className="space-y-6">
      <QueryForm
        showAdvancedOptions={true}
        initialValues={{
          repository: "https://github.com/my-org/my-repo"
        }}
        onQuerySuccess={(response) => {
          toast({
            title: "Query Processed",
            description: `Handled by ${response.agent_type} agent`,
          })
        }}
      />
      
      <ConversationHistory
        maxHeight={800}
        showSessionInfo={true}
        showActions={true}
      />
    </div>
  )
}
```

### Custom Message Renderer

```tsx
import { ConversationHistory, MessageCard } from "@/components/conversation"

export function CustomConversation() {
  const customRenderer = (message, index) => (
    <MessageCard
      key={index}
      message={message}
      index={index}
      showConfidence={true}
      onCopy={(content) => navigator.clipboard.writeText(content)}
    />
  )

  return (
    <ConversationHistory
      messageRenderer={customRenderer}
      maxHeight={600}
    />
  )
}
```

## API Reference

For detailed API documentation, see:

- [Component Documentation](../../docs/components/query-interface.md)
- [Usage Guide](../../docs/guides/query-interface-usage.md)

## Contributing

When contributing to these components:

1. Follow the existing code style and patterns
2. Add comprehensive tests for new features
3. Update documentation for API changes
4. Ensure accessibility compliance
5. Test with screen readers and keyboard navigation

## License

This code is part of the LLM RAG Codebase Query System and follows the project's licensing terms.
