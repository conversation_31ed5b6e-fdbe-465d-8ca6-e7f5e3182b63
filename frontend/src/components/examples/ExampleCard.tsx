"use client"

import * as React from "react"
import { ArrowRight, <PERSON><PERSON>, <PERSON>, Copy, Eye, Lightbulb, Zap } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

import { useCopyToClipboard } from "@/hooks/useCopyToClipboard"
import { useToast } from "@/hooks/useToast"
import { cn } from "@/lib/utils"
import type { QueryExample } from "./QueryExamples"

/**
 * Props for the ExampleCard component
 */
export interface ExampleCardProps {
  /**
   * The query example to display
   */
  example: QueryExample
  
  /**
   * Display variant
   */
  variant?: "default" | "compact" | "detailed"
  
  /**
   * Whether to show the full query text
   */
  showFullQuery?: boolean
  
  /**
   * Whether to show expected response preview
   */
  showExpectedResponse?: boolean
  
  /**
   * Custom CSS class name
   */
  className?: string
  
  /**
   * Callback fired when the example is selected
   */
  onSelect?: (example: QueryExample) => void
  
  /**
   * Callback fired when the query is inserted
   */
  onQueryInsert?: (query: string) => void
  
  /**
   * Callback fired when the example is copied
   */
  onCopy?: (example: QueryExample) => void
}

/**
 * ExampleCard Component
 * 
 * Individual example display component with features:
 * - Agent type identification with icons and colors
 * - Difficulty level indicators
 * - Tag display and categorization
 * - Query preview and full view
 * - Expected response preview
 * - Copy to clipboard functionality
 * - Insert query action
 * - Responsive design variants
 * 
 * @example
 * ```tsx
 * <ExampleCard
 *   example={{
 *     id: "1",
 *     title: "Authentication Analysis",
 *     query: "How does the authentication system work?",
 *     description: "Analyze the authentication flow and security measures",
 *     agentType: "TECHNICAL_ARCHITECT",
 *     category: "Security",
 *     tags: ["auth", "security", "jwt"],
 *     difficulty: "intermediate"
 *   }}
 *   variant="detailed"
 *   onQueryInsert={(query) => setQueryValue(query)}
 * />
 * ```
 */
export function ExampleCard({
  example,
  variant = "default",
  showFullQuery = false,
  showExpectedResponse = false,
  className,
  onSelect,
  onQueryInsert,
  onCopy,
}: ExampleCardProps) {
  const { toast } = useToast()
  const { copyToClipboard } = useCopyToClipboard()
  const [isPreviewOpen, setIsPreviewOpen] = React.useState(false)
  
  // Handle query insertion
  const handleQueryInsert = React.useCallback((e: React.MouseEvent) => {
    e.stopPropagation()
    onQueryInsert?.(example.query)
    toast({
      title: "Query inserted",
      description: "The example query has been added to the form",
    })
  }, [example.query, onQueryInsert, toast])
  
  // Handle copy to clipboard
  const handleCopy = React.useCallback(async (e: React.MouseEvent) => {
    e.stopPropagation()
    try {
      await copyToClipboard(example.query)
      toast({
        title: "Copied to clipboard",
        description: "Query has been copied to your clipboard",
      })
      onCopy?.(example)
    } catch (error) {
      toast({
        title: "Failed to copy",
        description: "Could not copy query to clipboard",
        variant: "destructive",
      })
    }
  }, [example, copyToClipboard, toast, onCopy])
  
  // Handle card selection
  const handleSelect = React.useCallback(() => {
    onSelect?.(example)
  }, [example, onSelect])
  
  // Get agent display info
  const getAgentInfo = React.useCallback((agentType: string) => {
    switch (agentType) {
      case "TECHNICAL_ARCHITECT":
        return {
          name: "Technical Architect",
          icon: Zap,
          color: "text-blue-500",
          bgColor: "bg-blue-500/10",
          borderColor: "border-blue-500/20",
        }
      case "TASK_PLANNER":
        return {
          name: "Task Planner",
          icon: Clock,
          color: "text-green-500",
          bgColor: "bg-green-500/10",
          borderColor: "border-green-500/20",
        }
      case "RAG_RETRIEVAL":
        return {
          name: "RAG Retrieval",
          icon: Bot,
          color: "text-purple-500",
          bgColor: "bg-purple-500/10",
          borderColor: "border-purple-500/20",
        }
      default:
        return {
          name: "General",
          icon: Lightbulb,
          color: "text-gray-500",
          bgColor: "bg-gray-500/10",
          borderColor: "border-gray-500/20",
        }
    }
  }, [])
  
  // Get difficulty color
  const getDifficultyColor = React.useCallback((difficulty: string) => {
    switch (difficulty) {
      case "beginner":
        return "text-green-600 bg-green-100 dark:bg-green-900/20"
      case "intermediate":
        return "text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20"
      case "advanced":
        return "text-red-600 bg-red-100 dark:bg-red-900/20"
      default:
        return "text-gray-600 bg-gray-100 dark:bg-gray-900/20"
    }
  }, [])
  
  const agentInfo = getAgentInfo(example.agentType)
  const AgentIcon = agentInfo.icon
  const difficultyColor = getDifficultyColor(example.difficulty)
  
  // Truncate query for preview
  const queryPreview = example.query.length > 100 
    ? `${example.query.substring(0, 100)}...`
    : example.query
  
  if (variant === "compact") {
    return (
      <Card 
        className={cn(
          "group cursor-pointer transition-all duration-200 hover:shadow-md",
          agentInfo.borderColor,
          className
        )}
        onClick={handleSelect}
      >
        <CardContent className="p-4">
          <div className="flex items-start justify-between gap-3">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-2">
                <div className={cn("p-1 rounded", agentInfo.bgColor)}>
                  <AgentIcon className={cn("h-3 w-3", agentInfo.color)} />
                </div>
                <h4 className="font-medium text-sm truncate">{example.title}</h4>
              </div>
              <p className="text-xs text-muted-foreground line-clamp-2">
                {example.description}
              </p>
            </div>
            <Button
              size="sm"
              variant="ghost"
              onClick={handleQueryInsert}
              className="opacity-0 group-hover:opacity-100 transition-opacity shrink-0"
            >
              <ArrowRight className="h-3 w-3" />
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }
  
  return (
    <Card 
      className={cn(
        "group cursor-pointer transition-all duration-200 hover:shadow-md hover:border-primary/20",
        className
      )}
      onClick={handleSelect}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between gap-3">
          <div className="flex items-center gap-3">
            <div className={cn("p-2 rounded-lg", agentInfo.bgColor)}>
              <AgentIcon className={cn("h-4 w-4", agentInfo.color)} />
            </div>
            <div>
              <CardTitle className="text-base">{example.title}</CardTitle>
              <CardDescription className="text-sm">
                {example.description}
              </CardDescription>
            </div>
          </div>
          
          <div className="flex items-center gap-1">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    size="icon"
                    variant="ghost"
                    onClick={handleCopy}
                    className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Copy query</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            {example.expectedResponse && (
              <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
                <DialogTrigger asChild>
                  <Button
                    size="icon"
                    variant="ghost"
                    onClick={(e) => e.stopPropagation()}
                    className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <Eye className="h-3 w-3" />
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>{example.title}</DialogTitle>
                    <DialogDescription>
                      Example query and expected response
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">Query:</h4>
                      <div className="p-3 bg-muted rounded-lg text-sm font-mono">
                        {example.query}
                      </div>
                    </div>
                    {example.expectedResponse && (
                      <div>
                        <h4 className="font-medium mb-2">Expected Response:</h4>
                        <div className="p-3 bg-muted rounded-lg text-sm">
                          {example.expectedResponse}
                        </div>
                      </div>
                    )}
                  </div>
                </DialogContent>
              </Dialog>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-3">
        {/* Query Preview */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Query:</span>
            {example.query.length > 100 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  setIsPreviewOpen(true)
                }}
                className="text-xs h-6"
              >
                View Full
              </Button>
            )}
          </div>
          <div className="p-3 bg-muted rounded-lg text-sm font-mono">
            {showFullQuery ? example.query : queryPreview}
          </div>
        </div>
        
        {/* Metadata */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {agentInfo.name}
            </Badge>
            <Badge className={cn("text-xs", difficultyColor)}>
              {example.difficulty}
            </Badge>
            <Badge variant="secondary" className="text-xs">
              {example.category}
            </Badge>
          </div>
        </div>
        
        {/* Tags */}
        {example.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {example.tags.slice(0, 4).map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {example.tags.length > 4 && (
              <Badge variant="outline" className="text-xs">
                +{example.tags.length - 4}
              </Badge>
            )}
          </div>
        )}
        
        <Separator />
        
        {/* Actions */}
        <div className="flex justify-end">
          <Button
            size="sm"
            onClick={handleQueryInsert}
            className="w-full sm:w-auto"
          >
            <ArrowRight className="mr-2 h-3 w-3" />
            Use This Query
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
