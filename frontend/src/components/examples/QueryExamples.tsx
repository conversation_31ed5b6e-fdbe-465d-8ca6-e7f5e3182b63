"use client"

import * as React from "react"
import { Search, Filter, Book<PERSON>pen, Lightbulb, Grid, List } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group"

import { cn } from "@/lib/utils"
import type { AgentType } from "@/types/api"

/**
 * Represents a query example
 */
export interface QueryExample {
  id: string
  title: string
  query: string
  description: string
  agentType: AgentType
  category: string
  tags: string[]
  difficulty: "beginner" | "intermediate" | "advanced"
  expectedResponse?: string
}

/**
 * Props for the QueryExamples component
 */
export interface QueryExamplesProps {
  /**
   * Available query examples
   */
  examples?: QueryExample[]
  
  /**
   * Callback fired when an example is selected
   */
  onExampleSelect?: (example: QueryExample) => void
  
  /**
   * Callback fired when an example query is inserted
   */
  onQueryInsert?: (query: string) => void
  
  /**
   * Custom CSS class name
   */
  className?: string
  
  /**
   * Whether to show search functionality
   */
  showSearch?: boolean
  
  /**
   * Whether to show filtering options
   */
  showFilters?: boolean
  
  /**
   * Default view mode
   */
  defaultViewMode?: "grid" | "list"
  
  /**
   * Whether to group examples by agent type
   */
  groupByAgent?: boolean
}

/**
 * QueryExamples Component
 * 
 * Main container for example queries with features:
 * - Search and filtering functionality
 * - Agent-specific example categorization
 * - Grid and list view modes
 * - Example insertion into query form
 * - Difficulty levels and tags
 * - Responsive design
 * 
 * @example
 * ```tsx
 * <QueryExamples
 *   examples={exampleQueries}
 *   onExampleSelect={(example) => console.log('Selected:', example)}
 *   onQueryInsert={(query) => setQueryValue(query)}
 *   showSearch={true}
 *   showFilters={true}
 *   groupByAgent={true}
 * />
 * ```
 */
export function QueryExamples({
  examples = [],
  onExampleSelect,
  onQueryInsert,
  className,
  showSearch = true,
  showFilters = true,
  defaultViewMode = "grid",
  groupByAgent = true,
}: QueryExamplesProps) {
  const [searchQuery, setSearchQuery] = React.useState("")
  const [selectedAgent, setSelectedAgent] = React.useState<AgentType | "all">("all")
  const [selectedCategory, setSelectedCategory] = React.useState<string>("all")
  const [selectedDifficulty, setSelectedDifficulty] = React.useState<string>("all")
  const [viewMode, setViewMode] = React.useState<"grid" | "list">(defaultViewMode)
  
  // Get unique values for filters
  const agentTypes = React.useMemo(() => {
    const types = new Set(examples.map(ex => ex.agentType))
    return Array.from(types)
  }, [examples])
  
  const categories = React.useMemo(() => {
    const cats = new Set(examples.map(ex => ex.category))
    return Array.from(cats)
  }, [examples])
  
  const difficulties = React.useMemo(() => {
    const diffs = new Set(examples.map(ex => ex.difficulty))
    return Array.from(diffs)
  }, [examples])
  
  // Filter examples based on search and filters
  const filteredExamples = React.useMemo(() => {
    return examples.filter(example => {
      // Search filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase()
        const matchesSearch = 
          example.title.toLowerCase().includes(query) ||
          example.description.toLowerCase().includes(query) ||
          example.query.toLowerCase().includes(query) ||
          example.tags.some(tag => tag.toLowerCase().includes(query))
        
        if (!matchesSearch) return false
      }
      
      // Agent type filter
      if (selectedAgent !== "all" && example.agentType !== selectedAgent) {
        return false
      }
      
      // Category filter
      if (selectedCategory !== "all" && example.category !== selectedCategory) {
        return false
      }
      
      // Difficulty filter
      if (selectedDifficulty !== "all" && example.difficulty !== selectedDifficulty) {
        return false
      }
      
      return true
    })
  }, [examples, searchQuery, selectedAgent, selectedCategory, selectedDifficulty])
  
  // Group examples by agent type if enabled
  const groupedExamples = React.useMemo(() => {
    if (!groupByAgent) {
      return [{ agentType: "all" as const, examples: filteredExamples }]
    }
    
    const groups = agentTypes.map(agentType => ({
      agentType,
      examples: filteredExamples.filter(ex => ex.agentType === agentType)
    })).filter(group => group.examples.length > 0)
    
    return groups
  }, [filteredExamples, groupByAgent, agentTypes])
  
  // Handle example selection
  const handleExampleSelect = React.useCallback((example: QueryExample) => {
    onExampleSelect?.(example)
  }, [onExampleSelect])
  
  // Handle query insertion
  const handleQueryInsert = React.useCallback((query: string) => {
    onQueryInsert?.(query)
  }, [onQueryInsert])
  
  // Clear all filters
  const clearFilters = React.useCallback(() => {
    setSearchQuery("")
    setSelectedAgent("all")
    setSelectedCategory("all")
    setSelectedDifficulty("all")
  }, [])
  
  // Get agent display info
  const getAgentInfo = React.useCallback((agentType: AgentType) => {
    switch (agentType) {
      case "TECHNICAL_ARCHITECT":
        return {
          name: "Technical Architect",
          description: "Architecture analysis, design patterns, and code structure",
          color: "bg-blue-500",
        }
      case "TASK_PLANNER":
        return {
          name: "Task Planner",
          description: "Project breakdown, task estimation, and dependency analysis",
          color: "bg-green-500",
        }
      case "RAG_RETRIEVAL":
        return {
          name: "RAG Retrieval",
          description: "Code search, documentation queries, and specific file lookups",
          color: "bg-purple-500",
        }
      default:
        return {
          name: "General",
          description: "General purpose queries",
          color: "bg-gray-500",
        }
    }
  }, [])
  
  return (
    <Card className={className}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-base flex items-center gap-2">
              <BookOpen className="h-4 w-4" />
              Query Examples
            </CardTitle>
            <CardDescription>
              Explore example queries to understand each agent's capabilities
            </CardDescription>
          </div>
          
          <div className="flex items-center gap-2">
            {/* View mode toggle */}
            <ToggleGroup
              type="single"
              value={viewMode}
              onValueChange={(value) => value && setViewMode(value as "grid" | "list")}
              className="h-8"
            >
              <ToggleGroupItem value="grid" aria-label="Grid view" size="sm">
                <Grid className="h-4 w-4" />
              </ToggleGroupItem>
              <ToggleGroupItem value="list" aria-label="List view" size="sm">
                <List className="h-4 w-4" />
              </ToggleGroupItem>
            </ToggleGroup>
            
            <Badge variant="outline" className="text-xs">
              {filteredExamples.length} examples
            </Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Search and Filters */}
        {(showSearch || showFilters) && (
          <div className="space-y-3">
            {/* Search */}
            {showSearch && (
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search examples..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            )}
            
            {/* Filters */}
            {showFilters && (
              <div className="flex flex-wrap gap-2">
                <Select value={selectedAgent} onValueChange={(value) => setSelectedAgent(value as AgentType | "all")}>
                  <SelectTrigger className="w-[140px] h-8">
                    <SelectValue placeholder="Agent" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Agents</SelectItem>
                    {agentTypes.map(type => (
                      <SelectItem key={type} value={type}>
                        {getAgentInfo(type).name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-[120px] h-8">
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories.map(category => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <Select value={selectedDifficulty} onValueChange={setSelectedDifficulty}>
                  <SelectTrigger className="w-[120px] h-8">
                    <SelectValue placeholder="Difficulty" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Levels</SelectItem>
                    {difficulties.map(difficulty => (
                      <SelectItem key={difficulty} value={difficulty}>
                        {difficulty.charAt(0).toUpperCase() + difficulty.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                {(searchQuery || selectedAgent !== "all" || selectedCategory !== "all" || selectedDifficulty !== "all") && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={clearFilters}
                    className="h-8"
                  >
                    Clear Filters
                  </Button>
                )}
              </div>
            )}
          </div>
        )}
        
        {/* Examples Display */}
        {filteredExamples.length > 0 ? (
          groupByAgent ? (
            <Tabs defaultValue={groupedExamples[0]?.agentType} className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                {agentTypes.map(agentType => {
                  const agentInfo = getAgentInfo(agentType)
                  const count = filteredExamples.filter(ex => ex.agentType === agentType).length
                  return (
                    <TabsTrigger key={agentType} value={agentType} className="text-xs">
                      {agentInfo.name} ({count})
                    </TabsTrigger>
                  )
                })}
              </TabsList>
              
              {agentTypes.map(agentType => {
                const agentExamples = filteredExamples.filter(ex => ex.agentType === agentType)
                const agentInfo = getAgentInfo(agentType)
                
                return (
                  <TabsContent key={agentType} value={agentType} className="space-y-4">
                    <div className="text-center py-2">
                      <h3 className="font-medium">{agentInfo.name}</h3>
                      <p className="text-sm text-muted-foreground">{agentInfo.description}</p>
                    </div>
                    
                    {/* Examples will be rendered by ExampleCard component */}
                    <div className={cn(
                      viewMode === "grid" 
                        ? "grid grid-cols-1 md:grid-cols-2 gap-4"
                        : "space-y-3"
                    )}>
                      {agentExamples.map(example => (
                        <div key={example.id} className="p-4 border rounded-lg hover:bg-muted/50 cursor-pointer">
                          <h4 className="font-medium mb-2">{example.title}</h4>
                          <p className="text-sm text-muted-foreground mb-2">{example.description}</p>
                          <div className="flex items-center justify-between">
                            <div className="flex gap-1">
                              {example.tags.slice(0, 2).map(tag => (
                                <Badge key={tag} variant="secondary" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                            <Button
                              size="sm"
                              onClick={() => handleQueryInsert(example.query)}
                            >
                              Use Query
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </TabsContent>
                )
              })}
            </Tabs>
          ) : (
            <div className={cn(
              viewMode === "grid" 
                ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
                : "space-y-3"
            )}>
              {filteredExamples.map(example => (
                <div key={example.id} className="p-4 border rounded-lg hover:bg-muted/50 cursor-pointer">
                  <h4 className="font-medium mb-2">{example.title}</h4>
                  <p className="text-sm text-muted-foreground mb-2">{example.description}</p>
                  <div className="flex items-center justify-between">
                    <Badge variant="outline" className="text-xs">
                      {getAgentInfo(example.agentType).name}
                    </Badge>
                    <Button
                      size="sm"
                      onClick={() => handleQueryInsert(example.query)}
                    >
                      Use Query
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )
        ) : (
          <div className="text-center py-8">
            <Lightbulb className="h-12 w-12 text-muted-foreground/50 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-muted-foreground mb-2">
              No examples found
            </h3>
            <p className="text-sm text-muted-foreground">
              Try adjusting your search or filter criteria
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
