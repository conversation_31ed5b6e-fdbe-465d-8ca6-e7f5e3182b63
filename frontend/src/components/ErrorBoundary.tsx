/**
 * Error Boundary Component
 * 
 * This component provides a React error boundary that catches JavaScript errors
 * anywhere in the child component tree, logs those errors, and displays a
 * fallback UI instead of the component tree that crashed.
 */

"use client"

import React from "react"
import type { ErrorInfo, ReactNode } from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { logError, createBaseError } from "@/utils/errorUtils"
import { ErrorCategory, ErrorSeverity } from "@/types/errors"
import type { ErrorContext } from "@/types/errors"

/**
 * Props for the ErrorBoundary component
 */
interface ErrorBoundaryProps {
  /**
   * Child components to wrap with error boundary
   */
  children: ReactNode

  /**
   * Optional fallback component to render on error
   */
  fallback?: ReactNode

  /**
   * Optional callback when error occurs
   */
  onError?: (error: Error, errorInfo: ErrorInfo) => void

  /**
   * Optional context information for error reporting
   */
  context?: Partial<ErrorContext>

  /**
   * Whether to show error details in development
   */
  showErrorDetails?: boolean
}

/**
 * State for the ErrorBoundary component
 */
interface ErrorBoundaryState {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
}

/**
 * Error Boundary Component
 * 
 * Catches React errors and displays a user-friendly fallback UI.
 * Logs errors for debugging and monitoring purposes.
 * 
 * @example
 * ```tsx
 * <ErrorBoundary context={{ component: "QueryForm" }}>
 *   <QueryForm />
 * </ErrorBoundary>
 * ```
 */
export class ErrorBoundary extends React.Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    }
  }

  /**
   * Static method to update state when an error occurs
   */
  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
    }
  }

  /**
   * Lifecycle method called when an error occurs
   */
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    })

    // Log the error
    const appError = createBaseError(
      error.message,
      ErrorCategory.UNKNOWN,
      ErrorSeverity.HIGH,
      {
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        ...this.props.context,
      }
    )

    logError(appError, {
      component: this.props.context?.component,
      action: this.props.context?.action,
      errorInfo,
    })

    // Call optional error callback
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }
  }

  /**
   * Resets the error boundary state
   */
  resetError = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    })
  }

  /**
   * Reloads the page as a recovery mechanism
   */
  reloadPage = () => {
    window.location.reload()
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback
      }

      // Default fallback UI
      return (
        <div className="flex min-h-[400px] flex-col items-center justify-center p-8 text-center">
          <div className="mb-6 rounded-lg border border-red-200 bg-red-50 p-6 dark:border-red-800 dark:bg-red-950">
            <h2 className="mb-2 text-xl font-semibold text-red-800 dark:text-red-200">
              Something went wrong
            </h2>
            <p className="text-red-600 dark:text-red-300">
              An unexpected error occurred. Please try refreshing the page or contact support if the problem persists.
            </p>
          </div>

          <div className="flex gap-4">
            <Button onClick={this.resetError} variant="outline">
              Try Again
            </Button>
            <Button onClick={this.reloadPage}>
              Reload Page
            </Button>
          </div>

          {/* Show error details in development */}
          {this.props.showErrorDetails && 
           process.env.NODE_ENV === "development" && 
           this.state.error && (
            <details className="mt-8 w-full max-w-2xl">
              <summary className="cursor-pointer text-sm font-medium text-gray-600 dark:text-gray-400">
                Error Details (Development Only)
              </summary>
              <div className="mt-4 rounded-md bg-gray-100 p-4 text-left dark:bg-gray-800">
                <h3 className="mb-2 font-medium">Error Message:</h3>
                <pre className="mb-4 whitespace-pre-wrap text-sm text-red-600 dark:text-red-400">
                  {this.state.error.message}
                </pre>
                
                {this.state.error.stack && (
                  <>
                    <h3 className="mb-2 font-medium">Stack Trace:</h3>
                    <pre className="mb-4 whitespace-pre-wrap text-xs text-gray-600 dark:text-gray-400">
                      {this.state.error.stack}
                    </pre>
                  </>
                )}
                
                {this.state.errorInfo?.componentStack && (
                  <>
                    <h3 className="mb-2 font-medium">Component Stack:</h3>
                    <pre className="whitespace-pre-wrap text-xs text-gray-600 dark:text-gray-400">
                      {this.state.errorInfo.componentStack}
                    </pre>
                  </>
                )}
              </div>
            </details>
          )}
        </div>
      )
    }

    return this.props.children
  }
}

/**
 * Hook-based error boundary wrapper for functional components
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, "children">
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  )

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`

  return WrappedComponent
}
