import type { Metadata } from "next"

import { TailwindIndicator } from "@/utils/tailwind-indicator"

import { META_THEME_COLORS, config } from "@/lib/config"
import { fontVariables } from "@/lib/fonts"
import { cn } from "@/lib/utils"

import { Toaster } from "@/components/ui/sonner"
import { NavigationWrapper } from "@/components/navigation/navigation-wrapper"
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar"

import { Providers } from "@/app/providers"

import "@/styles/globals.css"

export const metadata: Metadata = {
  title: {
    default: config.app.name,
    template: `%s - ${config.app.name}`,
  },
  metadataBase: new URL(
    process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"
  ),
  keywords: ["Next.js", "React", "Tailwind CSS", "Components", "ued-debug"],
  authors: [
    {
      name: "ued-debug",
      url: "https://ued-debug.com",
    },
  ],
  creator: "ued-debug",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: process.env.NEXT_PUBLIC_APP_URL,
    title: config.app.name,
    siteName: config.app.name,
    images: [
      {
        url: `${process.env.NEXT_PUBLIC_APP_URL}/opengraph-image.png`,
        width: 1200,
        height: 630,
        alt: config.app.name,
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: config.app.name,
    images: [`${process.env.NEXT_PUBLIC_APP_URL}/opengraph-image.png`],
    creator: "@shadcn",
  },
  icons: {
    icon: "/favicon.ico",
    shortcut: "/favicon-16x16.png",
    apple: "/apple-touch-icon.png",
  }
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              try {
                if (localStorage.theme === 'dark' || ((!('theme' in localStorage) || localStorage.theme === 'system') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                  document.querySelector('meta[name="theme-color"]').setAttribute('content', '${META_THEME_COLORS.dark}')
                }
                if (localStorage.layout) {
                  document.documentElement.classList.add('layout-' + localStorage.layout)
                }
              } catch (_) {}
            `,
          }}
        />
        <meta name="theme-color" content={META_THEME_COLORS.light} />
      </head>
      <body
        className={cn(
          "text-foreground group/body overscroll-none font-sans antialiased [--footer-height:calc(var(--spacing)*14)] [--header-height:calc(var(--spacing)*14)] xl:[--footer-height:calc(var(--spacing)*24)]",
          fontVariables
        )}
      >
        <Providers>
          <div className="flex min-h-screen flex-col">
            <div className="flex flex-1">
              <SidebarProvider>
                <NavigationWrapper />
                <SidebarInset className="p-4 md:p-6 lg:p-8">
                  <div className="mx-auto max-w-full">{children}</div>
                </SidebarInset>
              </SidebarProvider>
            </div>
          </div>
          <TailwindIndicator />
          <Toaster position="top-center" />
        </Providers>
      </body>
    </html>
  )
}
