/**
 * Repository Management Layout
 * 
 * Layout component for the repository management section.
 * Provides consistent structure and metadata for repository pages.
 */

import type { Metadata } from "next"

export const metadata: Metadata = {
  title: "Repository Management",
  description: "Manage your ingested repositories, track progress, and perform bulk operations",
}

export default function RepositoriesLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="min-h-screen bg-background">
      {children}
    </div>
  )
}
