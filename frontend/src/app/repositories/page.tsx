/**
 * Repository Management Page
 * 
 * Main page for repository management that integrates all repository-related components:
 * - Repository input form for adding new repositories
 * - Repository list with search, filter, and pagination
 * - Real-time ingestion progress tracking
 * - Repository actions and bulk operations
 */

"use client"

import * as React from "react"
import { Plus, Settings } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Separator } from "@/components/ui/separator"

import { RepositorySelector } from "@/components/query/RepositorySelector"
import {
  RepositoryList,
  IngestionProgress,
  RepositoryActions,
  BatchOperations
} from "@/components/repository"
import { RepositoryErrorBoundary } from "@/components/error"
import { useIngestionMutation, useActiveIngestions, useRepositoryStats, useRepositoryList } from "@/hooks/api"
import { useToast } from "@/hooks/useToast"
import type { RepositoryMetadata, RepositoryId } from "@/types/api"

/**
 * Repository Management Page Component
 */
export default function RepositoriesPage() {
  const [selectedRepositoryIds, setSelectedRepositoryIds] = React.useState<RepositoryId[]>([])
  const [selectedRepository, setSelectedRepository] = React.useState<RepositoryMetadata | null>(null)
  const [addRepositoryOpen, setAddRepositoryOpen] = React.useState(false)
  const [activeTab, setActiveTab] = React.useState("repositories")
  
  const { toast } = useToast()
  
  // API hooks
  const ingestionMutation = useIngestionMutation({
    onSuccess: (data) => {
      toast({
        title: "Repository Ingestion Started",
        description: `Started ingesting ${data.repository}`,
      })
      setAddRepositoryOpen(false)
      setActiveTab("progress") // Switch to progress tab
    },
    onError: (error) => {
      toast({
        title: "Ingestion Failed",
        description: error.message,
        variant: "destructive",
      })
    },
  })
  
  const { data: activeIngestions } = useActiveIngestions()
  const { data: stats } = useRepositoryStats()
  const { data: repositoryListData } = useRepositoryList()
  
  // Handle repository form submission
  const handleRepositorySubmit = React.useCallback((formData: {
    url: string
    branch?: string
    force_refresh?: boolean
    auth_token?: string
  }) => {
    if (!formData.url) return
    
    ingestionMutation.mutate({
      repository_url: formData.url,
      branch: formData.branch || undefined,
      force_refresh: formData.force_refresh || false,
    })
  }, [ingestionMutation])
  
  // Handle repository selection changes
  const handleSelectionChange = React.useCallback((selectedIds: RepositoryId[]) => {
    setSelectedRepositoryIds(selectedIds)
  }, [])
  
  // Handle repository actions
  const handleRepositoryAction = React.useCallback((action: string, repository: RepositoryMetadata) => {
    switch (action) {
      case "view":
        setSelectedRepository(repository)
        break
      case "re-ingest":
        toast({
          title: "Re-ingestion Started",
          description: `Started re-ingesting ${repository.owner}/${repository.name}`,
        })
        break
      case "delete":
        toast({
          title: "Repository Deleted",
          description: `Deleted ${repository.owner}/${repository.name}`,
        })
        break
    }
  }, [toast])
  
  // Handle action completion
  const handleActionComplete = React.useCallback((action: string, repositoryId?: RepositoryId) => {
    // Clear selection after bulk operations
    if (!repositoryId) {
      setSelectedRepositoryIds([])
    }
    
    toast({
      title: "Action Completed",
      description: `${action} operation completed successfully`,
    })
  }, [toast])
  
  return (
    <RepositoryErrorBoundary
      showDetails={process.env.NODE_ENV === 'development'}
      onError={(error, errorInfo) => {
        console.error('Repository page error:', error, errorInfo)
      }}
    >
      <div className="container mx-auto py-6 space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Repository Management</h1>
          <p className="text-muted-foreground">
            Manage your ingested repositories, track progress, and perform bulk operations
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Dialog open={addRepositoryOpen} onOpenChange={setAddRepositoryOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Repository
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Add New Repository</DialogTitle>
              </DialogHeader>
              <div className="py-4">
                <RepositorySelector
                  onChange={handleRepositorySubmit}
                  showAdvancedOptions={true}
                  showAuthOptions={true}
                  disabled={ingestionMutation.isPending}
                />
                <div className="mt-4 flex justify-end gap-2">
                  <Button 
                    variant="outline" 
                    onClick={() => setAddRepositoryOpen(false)}
                    disabled={ingestionMutation.isPending}
                  >
                    Cancel
                  </Button>
                  <Button 
                    onClick={() => {
                      // This would be handled by the RepositorySelector onChange
                    }}
                    disabled={ingestionMutation.isPending}
                  >
                    Start Ingestion
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>
      
      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Repositories</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_repositories}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Files Processed</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_files_processed.toLocaleString()}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Embeddings Generated</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_embeddings.toLocaleString()}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Storage Used</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{Math.round(stats.storage_usage_mb)} MB</div>
            </CardContent>
          </Card>
        </div>
      )}
      
      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="repositories">
            Repositories
            {stats && (
              <span className="ml-2 rounded-full bg-muted px-2 py-0.5 text-xs">
                {stats.total_repositories}
              </span>
            )}
          </TabsTrigger>
          <TabsTrigger value="progress">
            Active Ingestions
            {activeIngestions && activeIngestions.length > 0 && (
              <span className="ml-2 rounded-full bg-primary px-2 py-0.5 text-xs text-primary-foreground">
                {activeIngestions.length}
              </span>
            )}
          </TabsTrigger>
          <TabsTrigger value="actions">
            Bulk Actions
            {selectedRepositoryIds.length > 0 && (
              <span className="ml-2 rounded-full bg-secondary px-2 py-0.5 text-xs">
                {selectedRepositoryIds.length}
              </span>
            )}
          </TabsTrigger>
        </TabsList>
        
        {/* Repository List Tab */}
        <TabsContent value="repositories" className="space-y-4">
          <RepositoryList
            onSelectionChange={handleSelectionChange}
            onRepositoryAction={handleRepositoryAction}
            showSelection={true}
            showControls={true}
          />
        </TabsContent>
        
        {/* Active Ingestions Tab */}
        <TabsContent value="progress" className="space-y-4">
          {activeIngestions && activeIngestions.length > 0 ? (
            <div className="grid gap-4">
              {activeIngestions.map((progress) => (
                <IngestionProgress
                  key={progress.repository_id}
                  repositoryId={progress.repository_id}
                  showDetails={true}
                  onComplete={(id) => {
                    toast({
                      title: "Ingestion Completed",
                      description: `Repository ${id} has been successfully ingested`,
                    })
                  }}
                />
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <p className="text-muted-foreground">No active ingestions</p>
                <p className="text-sm text-muted-foreground mt-1">
                  Add a new repository to start ingesting content
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
        
        {/* Bulk Actions Tab */}
        <TabsContent value="actions" className="space-y-4">
          <BatchOperations
            selectedRepositories={repositoryListData?.repositories.filter(repo =>
              selectedRepositoryIds.includes(repo.id)
            ) || []}
            onOperationComplete={(operation, results) => {
              handleActionComplete(operation)
              toast({
                title: "Batch Operation Completed",
                description: `${operation} completed: ${results.completed} successful, ${results.failed} failed`,
                variant: results.failed > 0 ? "destructive" : "default",
              })
            }}
            onClearSelection={() => setSelectedRepositoryIds([])}
          />

          <RepositoryActions
            selectedRepositoryIds={selectedRepositoryIds}
            onActionComplete={handleActionComplete}
            showBulkActions={true}
          />
          
          {selectedRepository && (
            <>
              <Separator />
              <Card>
                <CardHeader>
                  <CardTitle>Repository Details</CardTitle>
                  <CardDescription>
                    Detailed information about the selected repository
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <RepositoryActions
                    repository={selectedRepository}
                    onActionComplete={handleActionComplete}
                    onViewDetails={(repo) => {
                      // Could open a detailed view modal or navigate to details page
                      console.log("View details for:", repo)
                    }}
                  />
                </CardContent>
              </Card>
            </>
          )}
          
          {selectedRepositoryIds.length === 0 && !selectedRepository && (
            <Card>
              <CardContent className="p-8 text-center">
                <p className="text-muted-foreground">No repositories selected</p>
                <p className="text-sm text-muted-foreground mt-1">
                  Select repositories from the list to perform bulk actions
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
    </RepositoryErrorBoundary>
  )
}
