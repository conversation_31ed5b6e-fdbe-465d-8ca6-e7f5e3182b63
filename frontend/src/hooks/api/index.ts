/**
 * API Hooks Barrel Export
 * 
 * This module provides a centralized export point for all API-related hooks,
 * enabling clean imports throughout the application.
 */

// Query hooks
export {
  useQueryMutation,
  useQueryHistory,
  useQuerySuggestions,
  useQueryAnalytics,
  useQueryState,
  type UseQueryMutationOptions,
  type UseQueryHistoryOptions,
} from "./useQuery"

// Ingestion hooks
export {
  useIngestionMutation,
  useIngestionStatus,
  useIngestionHistory,
  useCancelIngestion,
  useIngestionState,
  type UseIngestionMutationOptions,
  type UseIngestionStatusOptions,
  type IngestionProgress,
} from "./useIngestion"

// Status hooks
export {
  useSystemStatus,
  useHealthCheck,
  useAgentStatus,
  useSystemHealth,
  useStatusAlerts,
  type UseSystemStatusOptions,
  type UseHealthCheckOptions,
  type SystemHealth,
  type AgentStatus,
  type StatusAlert,
} from "./useStatus"

// Repository management hooks
export {
  useRepositoryList,
  useRepositoryDetails,
  useRepositoryDelete,
  useRepositoryReIngest,
  useBatchOperation,
  useRepositoryStats,
  useIngestionProgress,
  useActiveIngestions,
  type UseRepositoryListOptions,
  type UseRepositoryDetailsOptions,
  type UseRepositoryDeleteOptions,
  type UseRepositoryReIngestOptions,
  type UseBatchOperationOptions,
} from "./useRepository"
