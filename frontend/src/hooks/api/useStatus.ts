/**
 * Status API Hooks
 * 
 * This module provides React Query hooks for system status monitoring,
 * health checks, and agent system status with background refetching.
 */

import React from "react"
import { useQuery, type UseQueryOptions } from "@tanstack/react-query"

import type { StatusResponse, ApiStatus } from "@/types/api"
import type { HealthResponse } from "@/lib/api"
import { apiClient } from "@/lib/api"
import { queryKeys } from "@/providers/query-provider"
import { useErrorHandler } from "@/hooks/useErrorHandler"

/**
 * System health status
 */
export interface SystemHealth {
  overall: ApiStatus
  api: ApiStatus
  agents: Record<string, ApiStatus>
  vectorStore: ApiStatus
  ingestionPipeline: ApiStatus
  activeSessions: number
  uptime?: number
  lastChecked: Date
}

/**
 * Agent status details
 */
export interface AgentStatus {
  name: string
  type: string
  status: ApiStatus
  lastActivity?: Date
  responseTime?: number
  errorRate?: number
  requestCount?: number
}

/**
 * Options for the system status hook
 */
export interface UseSystemStatusOptions extends Omit<UseQueryOptions<StatusResponse, Error>, "queryKey" | "queryFn"> {
  enabled?: boolean
  pollingInterval?: number
  enableBackgroundRefetch?: boolean
}

/**
 * Options for the health check hook
 */
export interface UseHealthCheckOptions extends Omit<UseQueryOptions<HealthResponse, Error>, "queryKey" | "queryFn"> {
  enabled?: boolean
  pollingInterval?: number
  enableBackgroundRefetch?: boolean
}

/**
 * Hook for monitoring system status
 * 
 * This hook provides comprehensive system status information including
 * API health, agent status, and infrastructure components with automatic
 * background refetching.
 * 
 * @param options - Configuration options for the query
 * @returns Query object with system status data
 * 
 * @example
 * ```tsx
 * const { data: status, isLoading, error } = useSystemStatus({
 *   pollingInterval: 30000, // Poll every 30 seconds
 *   enableBackgroundRefetch: true
 * })
 * 
 * if (status?.api === 'operational') {
 *   // System is healthy
 * }
 * ```
 */
export function useSystemStatus(options: UseSystemStatusOptions = {}) {
  const {
    enabled = true,
    pollingInterval = 30000, // 30 seconds default
    enableBackgroundRefetch = true,
    ...queryOptions
  } = options
  
  const { handleError } = useErrorHandler({
    showToast: false, // Don't show toast for status polling errors
    defaultContext: { component: "useSystemStatus" },
  })

  return useQuery({
    queryKey: queryKeys.status.system(),
    
    queryFn: async (): Promise<StatusResponse> => {
      return await apiClient.getStatus()
    },
    
    enabled,
    
    // Polling configuration
    refetchInterval: enableBackgroundRefetch ? pollingInterval : false,
    
    // Cache configuration
    staleTime: 10000, // 10 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
    
    // Background refetching
    refetchOnWindowFocus: enableBackgroundRefetch,
    refetchOnReconnect: true,
    refetchIntervalInBackground: enableBackgroundRefetch,
    
    // Error handling
    onError: (error) => {
      handleError(error, { component: "useSystemStatus" })
    },
    
    // Retry configuration
    retry: (failureCount, error) => {
      // Retry up to 3 times for status checks
      return failureCount < 3
    },
    
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
    
    ...queryOptions,
  })
}

/**
 * Hook for basic health checks
 * 
 * This hook provides basic API health information with lightweight polling
 * for monitoring API availability.
 * 
 * @param options - Configuration options for the query
 * @returns Query object with health data
 * 
 * @example
 * ```tsx
 * const { data: health, isLoading } = useHealthCheck({
 *   pollingInterval: 60000 // Poll every minute
 * })
 * ```
 */
export function useHealthCheck(options: UseHealthCheckOptions = {}) {
  const {
    enabled = true,
    pollingInterval = 60000, // 1 minute default
    enableBackgroundRefetch = true,
    ...queryOptions
  } = options
  
  const { handleError } = useErrorHandler({
    showToast: false,
    defaultContext: { component: "useHealthCheck" },
  })

  return useQuery({
    queryKey: queryKeys.status.health(),
    
    queryFn: async (): Promise<HealthResponse> => {
      try {
        return await apiClient.getHealth()
      } catch (error) {
        handleError(error as Error)
        throw error
      }
    },
    
    enabled,
    
    // Polling configuration
    refetchInterval: enableBackgroundRefetch ? pollingInterval : false,
    
    // Cache configuration
    staleTime: 30000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
    
    // Background refetching
    refetchOnWindowFocus: enableBackgroundRefetch,
    refetchOnReconnect: true,
    refetchIntervalInBackground: enableBackgroundRefetch,
    
    // Error handling
    onError: (error) => {
      handleError(error, { component: "useHealthCheck" })
    },
    
    // Retry configuration
    retry: 2,
    retryDelay: 1000,
    
    ...queryOptions,
  })
}

/**
 * Hook for monitoring agent system status
 * 
 * This hook provides detailed status information for individual agents
 * in the multi-agent system.
 * 
 * @param options - Configuration options for the query
 * @returns Query object with agent status data
 */
export function useAgentStatus(options: UseQueryOptions<AgentStatus[], Error> = {}) {
  const { handleError } = useErrorHandler({
    showToast: false,
    defaultContext: { component: "useAgentStatus" },
  })

  return useQuery({
    queryKey: queryKeys.status.agents(),
    
    queryFn: async (): Promise<AgentStatus[]> => {
      try {
        const status = await apiClient.getStatus()
        
        // Transform agent status data
        return Object.entries(status.agents).map(([type, statusString]) => ({
          name: type.replace("_", " ").toLowerCase(),
          type,
          status: statusString.includes("operational") ? "operational" as ApiStatus : "down" as ApiStatus,
          lastActivity: new Date(),
          responseTime: Math.random() * 1000, // Mock data
          errorRate: Math.random() * 0.1,
          requestCount: Math.floor(Math.random() * 1000),
        }))
      } catch (error) {
        handleError(error as Error)
        throw error
      }
    },
    
    // Cache configuration
    staleTime: 30000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
    
    // Background refetching
    refetchInterval: 30000, // 30 seconds
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
    
    // Error handling
    onError: (error) => {
      handleError(error, { component: "useAgentStatus" })
    },
    
    ...options,
  })
}

/**
 * Hook for system health summary
 * 
 * This hook provides a consolidated view of system health combining
 * multiple status sources into a single health indicator.
 * 
 * @param options - Configuration options
 * @returns Computed system health summary
 */
export function useSystemHealth(options: { enabled?: boolean } = {}) {
  const { enabled = true } = options
  
  const systemStatus = useSystemStatus({ enabled })
  const healthCheck = useHealthCheck({ enabled })
  
  const systemHealth = React.useMemo((): SystemHealth | undefined => {
    if (!systemStatus.data || !healthCheck.data) {
      return undefined
    }
    
    // Determine overall health based on component status
    const components = [
      systemStatus.data.api,
      systemStatus.data.vector_store,
      systemStatus.data.ingestion_pipeline,
    ]
    
    const agentStatuses = Object.values(systemStatus.data.agents)
    const allStatuses = [...components, ...agentStatuses]
    
    let overall: ApiStatus = "operational"
    if (allStatuses.some(status => status.includes("down") || status.includes("error"))) {
      overall = "down"
    } else if (allStatuses.some(status => status.includes("degraded") || status.includes("warning"))) {
      overall = "degraded"
    }
    
    return {
      overall,
      api: systemStatus.data.api as ApiStatus,
      agents: Object.fromEntries(
        Object.entries(systemStatus.data.agents).map(([key, value]) => [
          key,
          value.includes("operational") ? "operational" as ApiStatus : "down" as ApiStatus
        ])
      ),
      vectorStore: systemStatus.data.vector_store as ApiStatus,
      ingestionPipeline: systemStatus.data.ingestion_pipeline as ApiStatus,
      activeSessions: systemStatus.data.active_sessions,
      lastChecked: new Date(),
    }
  }, [systemStatus.data, healthCheck.data])
  
  return {
    data: systemHealth,
    isLoading: systemStatus.isLoading || healthCheck.isLoading,
    error: systemStatus.error || healthCheck.error,
    refetch: () => {
      systemStatus.refetch()
      healthCheck.refetch()
    },
  }
}

/**
 * Hook for status alerts and notifications
 * 
 * This hook monitors system status and triggers alerts when
 * components become unhealthy.
 * 
 * @param options - Configuration options
 * @returns Alert state and functions
 */
export function useStatusAlerts(options: {
  enabled?: boolean
  onAlert?: (alert: StatusAlert) => void
} = {}) {
  const { enabled = true, onAlert } = options
  const [alerts, setAlerts] = React.useState<StatusAlert[]>([])
  
  const systemHealth = useSystemHealth({ enabled })
  
  // Monitor for status changes and trigger alerts
  React.useEffect(() => {
    if (!systemHealth.data) return
    
    const newAlerts: StatusAlert[] = []
    
    // Check overall system health
    if (systemHealth.data.overall !== "operational") {
      newAlerts.push({
        id: "system-health",
        type: systemHealth.data.overall === "down" ? "error" : "warning",
        title: "System Health Alert",
        message: `System status is ${systemHealth.data.overall}`,
        timestamp: new Date(),
        component: "system",
      })
    }
    
    // Check individual agent status
    Object.entries(systemHealth.data.agents).forEach(([agent, status]) => {
      if (status !== "operational") {
        newAlerts.push({
          id: `agent-${agent}`,
          type: "error",
          title: "Agent Status Alert",
          message: `Agent ${agent} is ${status}`,
          timestamp: new Date(),
          component: agent,
        })
      }
    })
    
    // Update alerts and trigger callbacks
    setAlerts(newAlerts)
    newAlerts.forEach(alert => onAlert?.(alert))
  }, [systemHealth.data, onAlert])
  
  const dismissAlert = React.useCallback((alertId: string) => {
    setAlerts(prev => prev.filter(alert => alert.id !== alertId))
  }, [])
  
  const clearAllAlerts = React.useCallback(() => {
    setAlerts([])
  }, [])
  
  return {
    alerts,
    hasAlerts: alerts.length > 0,
    dismissAlert,
    clearAllAlerts,
  }
}

/**
 * Status alert interface
 */
export interface StatusAlert {
  id: string
  type: "info" | "warning" | "error"
  title: string
  message: string
  timestamp: Date
  component: string
}
