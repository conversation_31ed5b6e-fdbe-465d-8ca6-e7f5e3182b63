/**
 * Repository Management API Hooks
 * 
 * This module provides React Query hooks for repository management operations,
 * including listing, deleting, re-ingesting, and batch operations on repositories.
 */

import React from "react"
import { useMutation, useQuery, useQueryClient, type UseQueryOptions, type UseMutationOptions } from "@tanstack/react-query"

import type {
  RepositoryListRequest,
  RepositoryListResponse,
  RepositoryMetadata,
  RepositoryDeleteRequest,
  RepositoryDeleteResponse,
  RepositoryReIngestRequest,
  IngestionResponse,
  BatchOperationRequest,
  BatchOperationResponse,
  RepositoryStatsResponse,
  IngestionProgress,
  RepositoryId,
} from "@/types/api"
import { apiClient } from "@/lib/api"
import { queryKeys } from "@/providers/query-provider"
import { useErrorHandler } from "@/hooks/useErrorHandler"

/**
 * Options for the repository list hook
 */
export interface UseRepositoryListOptions extends Omit<UseQueryOptions<RepositoryListResponse, Error>, "queryKey" | "queryFn"> {
  page?: number
  page_size?: number
  status_filter?: string
  search_query?: string
  sort_by?: string
  sort_order?: "asc" | "desc"
}

/**
 * Options for the repository details hook
 */
export interface UseRepositoryDetailsOptions extends Omit<UseQueryOptions<RepositoryMetadata, Error>, "queryKey" | "queryFn"> {
  repository_id: RepositoryId
}

/**
 * Options for the repository delete mutation
 */
export interface UseRepositoryDeleteOptions extends Omit<UseMutationOptions<RepositoryDeleteResponse, Error, { repository_id: RepositoryId; force?: boolean }>, "mutationFn"> {
  onSuccess?: (data: RepositoryDeleteResponse, variables: { repository_id: RepositoryId; force?: boolean }, context?: any) => void
  onError?: (error: Error, variables: { repository_id: RepositoryId; force?: boolean }, context?: any) => void
  showToast?: boolean
}

/**
 * Options for the repository re-ingest mutation
 */
export interface UseRepositoryReIngestOptions extends Omit<UseMutationOptions<IngestionResponse, Error, { repository_id: RepositoryId } & RepositoryReIngestRequest>, "mutationFn"> {
  onSuccess?: (data: IngestionResponse, variables: { repository_id: RepositoryId } & RepositoryReIngestRequest, context?: any) => void
  onError?: (error: Error, variables: { repository_id: RepositoryId } & RepositoryReIngestRequest, context?: any) => void
  showToast?: boolean
}

/**
 * Options for the batch operation mutation
 */
export interface UseBatchOperationOptions extends Omit<UseMutationOptions<BatchOperationResponse, Error, BatchOperationRequest>, "mutationFn"> {
  onSuccess?: (data: BatchOperationResponse, variables: BatchOperationRequest, context?: any) => void
  onError?: (error: Error, variables: BatchOperationRequest, context?: any) => void
  showToast?: boolean
}

/**
 * Hook for listing repositories with filtering and pagination
 * 
 * @param options - Configuration options for the query
 * @returns Query object with repository list data and state
 * 
 * @example
 * ```tsx
 * const { data: repositories, isLoading, error } = useRepositoryList({
 *   page: 1,
 *   page_size: 20,
 *   status_filter: "completed",
 *   search_query: "react"
 * })
 * ```
 */
export function useRepositoryList(options: UseRepositoryListOptions = {}) {
  const { handleRepositoryError } = useErrorHandler()
  
  const {
    page = 1,
    page_size = 20,
    status_filter,
    search_query,
    sort_by = "updated_at",
    sort_order = "desc",
    ...queryOptions
  } = options

  return useQuery({
    queryKey: queryKeys.repositories.list({
      page,
      page_size,
      status_filter,
      search_query,
      sort_by,
      sort_order,
    }),
    queryFn: async (): Promise<RepositoryListResponse> => {
      try {
        const params = new URLSearchParams()
        params.append("page", page.toString())
        params.append("page_size", page_size.toString())
        if (status_filter) params.append("status_filter", status_filter)
        if (search_query) params.append("search_query", search_query)
        params.append("sort_by", sort_by)
        params.append("sort_order", sort_order)

        const response = await fetch(`${apiClient.baseURL}/api/repositories?${params}`)
        if (!response.ok) {
          throw new Error(`Failed to fetch repositories: ${response.statusText}`)
        }
        return await response.json()
      } catch (error) {
        handleRepositoryError(error, { context: { operation: "repository_list" } })
        throw error
      }
    },
    staleTime: 30000, // 30 seconds
    ...queryOptions,
  })
}

/**
 * Hook for getting detailed information about a specific repository
 * 
 * @param options - Configuration options for the query
 * @returns Query object with repository details and state
 * 
 * @example
 * ```tsx
 * const { data: repository, isLoading, error } = useRepositoryDetails({
 *   repository_id: "repo-123"
 * })
 * ```
 */
export function useRepositoryDetails(options: UseRepositoryDetailsOptions) {
  const { handleRepositoryError } = useErrorHandler()
  const { repository_id, ...queryOptions } = options

  return useQuery({
    queryKey: queryKeys.repositories.detail(repository_id),
    queryFn: async (): Promise<RepositoryMetadata> => {
      try {
        const response = await fetch(`${apiClient.baseURL}/api/repositories/${repository_id}`)
        if (!response.ok) {
          throw new Error(`Failed to fetch repository: ${response.statusText}`)
        }
        return await response.json()
      } catch (error) {
        handleRepositoryError(error, { context: { operation: "repository_details", repository_id } })
        throw error
      }
    },
    enabled: !!repository_id,
    staleTime: 60000, // 1 minute
    ...queryOptions,
  })
}

/**
 * Hook for deleting a repository
 * 
 * @param options - Configuration options for the mutation
 * @returns Mutation object with delete function and state
 * 
 * @example
 * ```tsx
 * const deleteMutation = useRepositoryDelete({
 *   onSuccess: (response) => {
 *     console.log('Repository deleted:', response.message)
 *   }
 * })
 * 
 * const handleDelete = () => {
 *   deleteMutation.mutate({
 *     repository_id: "repo-123",
 *     force: true
 *   })
 * }
 * ```
 */
export function useRepositoryDelete(options: UseRepositoryDeleteOptions = {}) {
  const queryClient = useQueryClient()
  const { handleRepositoryError } = useErrorHandler()
  const { showToast = true, ...mutationOptions } = options

  return useMutation({
    mutationFn: async ({ repository_id, force = false }: { repository_id: RepositoryId; force?: boolean }): Promise<RepositoryDeleteResponse> => {
      try {
        const response = await fetch(`${apiClient.baseURL}/api/repositories/${repository_id}?force=${force}`, {
          method: "DELETE",
        })
        if (!response.ok) {
          throw new Error(`Failed to delete repository: ${response.statusText}`)
        }
        return await response.json()
      } catch (error) {
        handleRepositoryError(error, { context: { operation: "repository_delete", repository_id } })
        throw error
      }
    },
    
    onSuccess: (data, variables, context) => {
      // Invalidate and refetch repository list
      queryClient.invalidateQueries({
        queryKey: queryKeys.repositories.lists()
      })
      
      // Remove the specific repository from cache
      queryClient.removeQueries({
        queryKey: queryKeys.repositories.detail(variables.repository_id)
      })
      
      if (showToast) {
        // Toast notification would be handled by the error handler or a toast context
      }
      
      options.onSuccess?.(data, variables, context)
    },
    
    onError: (error, variables, context) => {
      handleRepositoryError(error, { context: { operation: "repository_delete", repository_id: variables.repository_id } })
      options.onError?.(error, variables, context)
    },
    
    ...mutationOptions,
  })
}

/**
 * Hook for re-ingesting a repository
 * 
 * @param options - Configuration options for the mutation
 * @returns Mutation object with re-ingest function and state
 * 
 * @example
 * ```tsx
 * const reIngestMutation = useRepositoryReIngest({
 *   onSuccess: (response) => {
 *     console.log('Re-ingestion started:', response.status)
 *   }
 * })
 * 
 * const handleReIngest = () => {
 *   reIngestMutation.mutate({
 *     repository_id: "repo-123",
 *     branch: "develop",
 *     force_refresh: true
 *   })
 * }
 * ```
 */
export function useRepositoryReIngest(options: UseRepositoryReIngestOptions = {}) {
  const queryClient = useQueryClient()
  const { handleRepositoryError } = useErrorHandler()
  const { showToast = true, ...mutationOptions } = options

  return useMutation({
    mutationFn: async ({ repository_id, ...request }: { repository_id: RepositoryId } & RepositoryReIngestRequest): Promise<IngestionResponse> => {
      try {
        const response = await fetch(`${apiClient.baseURL}/api/repositories/${repository_id}/re-ingest`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(request),
        })
        if (!response.ok) {
          throw new Error(`Failed to re-ingest repository: ${response.statusText}`)
        }
        return await response.json()
      } catch (error) {
        handleRepositoryError(error, { context: { operation: "repository_re_ingest", repository_id } })
        throw error
      }
    },
    
    onSuccess: (data, variables, context) => {
      // Invalidate repository details to refresh status
      queryClient.invalidateQueries({
        queryKey: queryKeys.repositories.detail(variables.repository_id)
      })
      
      // Invalidate repository list to refresh status
      queryClient.invalidateQueries({
        queryKey: queryKeys.repositories.lists()
      })
      
      if (showToast) {
        // Toast notification would be handled by the error handler or a toast context
      }
      
      options.onSuccess?.(data, variables, context)
    },
    
    onError: (error, variables, context) => {
      handleRepositoryError(error, { context: { operation: "repository_re_ingest", repository_id: variables.repository_id } })
      options.onError?.(error, variables, context)
    },
    
    ...mutationOptions,
  })
}

/**
 * Hook for batch operations on multiple repositories
 *
 * @param options - Configuration options for the mutation
 * @returns Mutation object with batch operation function and state
 *
 * @example
 * ```tsx
 * const batchMutation = useBatchOperation({
 *   onSuccess: (response) => {
 *     console.log(`Batch operation completed: ${response.successful}/${response.total_requested} successful`)
 *   }
 * })
 *
 * const handleBatchDelete = () => {
 *   batchMutation.mutate({
 *     repository_ids: ["repo-1", "repo-2", "repo-3"],
 *     operation: "delete",
 *     force: false
 *   })
 * }
 * ```
 */
export function useBatchOperation(options: UseBatchOperationOptions = {}) {
  const queryClient = useQueryClient()
  const { handleRepositoryError } = useErrorHandler()
  const { showToast = true, ...mutationOptions } = options

  return useMutation({
    mutationFn: async (request: BatchOperationRequest): Promise<BatchOperationResponse> => {
      try {
        const response = await fetch(`${apiClient.baseURL}/api/repositories/batch`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(request),
        })
        if (!response.ok) {
          throw new Error(`Failed to perform batch operation: ${response.statusText}`)
        }
        return await response.json()
      } catch (error) {
        handleRepositoryError(error, { context: { operation: "batch_operation", batch_operation: request.operation } })
        throw error
      }
    },

    onSuccess: (data, variables, context) => {
      // Invalidate all repository-related queries
      queryClient.invalidateQueries({
        queryKey: queryKeys.repositories.all
      })

      // If it was a delete operation, remove specific repositories from cache
      if (variables.operation === "delete") {
        variables.repository_ids.forEach(id => {
          queryClient.removeQueries({
            queryKey: queryKeys.repositories.detail(id)
          })
        })
      }

      if (showToast) {
        // Toast notification would be handled by the error handler or a toast context
      }

      options.onSuccess?.(data, variables, context)
    },

    onError: (error, variables, context) => {
      handleRepositoryError(error, { context: { operation: "batch_operation", batch_operation: variables.operation } })
      options.onError?.(error, variables, context)
    },

    ...mutationOptions,
  })
}

/**
 * Hook for getting repository statistics
 *
 * @param options - Configuration options for the query
 * @returns Query object with repository stats and state
 *
 * @example
 * ```tsx
 * const { data: stats, isLoading, error } = useRepositoryStats({
 *   refetchInterval: 30000 // Refetch every 30 seconds
 * })
 * ```
 */
export function useRepositoryStats(options: Omit<UseQueryOptions<RepositoryStatsResponse, Error>, "queryKey" | "queryFn"> = {}) {
  const { handleRepositoryError } = useErrorHandler()

  return useQuery({
    queryKey: queryKeys.repositories.stats(),
    queryFn: async (): Promise<RepositoryStatsResponse> => {
      try {
        const response = await fetch(`${apiClient.baseURL}/api/repositories/stats`)
        if (!response.ok) {
          throw new Error(`Failed to fetch repository stats: ${response.statusText}`)
        }
        return await response.json()
      } catch (error) {
        handleRepositoryError(error, { context: { operation: "repository_stats" } })
        throw error
      }
    },
    staleTime: 60000, // 1 minute
    ...options,
  })
}
