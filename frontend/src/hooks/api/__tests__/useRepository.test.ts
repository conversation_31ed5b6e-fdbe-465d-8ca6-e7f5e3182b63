/**
 * Repository API Hooks Tests
 * 
 * Comprehensive tests for repository-related API hooks including
 * data fetching, mutations, error handling, and caching behavior.
 */

import { describe, it, expect, vi, beforeEach, afterEach } from "vitest"
import { renderHook, waitFor } from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { createTestQueryClient } from "@/test/utils"
import {
  useRepositoryList,
  useRepositoryDetails,
  useRepositoryDelete,
  useRepositoryReIngest,
  useBatchOperation,
  useRepositoryStats,
} from "../useRepository"
import type { 
  RepositoryListResponse, 
  RepositoryMetadata, 
  BatchOperationResponse,
  RepositoryStatsResponse 
} from "@/types/api"

// Mock fetch globally
const mockFetch = vi.fn()
global.fetch = mockFetch

// Mock error handler
vi.mock("@/hooks/useErrorHandler", () => ({
  useErrorHandler: () => ({
    handleRepositoryError: vi.fn(),
  }),
}))

describe("Repository API Hooks", () => {
  let queryClient: QueryClient

  const mockRepository: RepositoryMetadata = {
    id: "repo-123",
    url: new URL("https://github.com/test/repo"),
    name: "repo",
    owner: "test",
    branch: "main",
    commit_sha: "abc123",
    is_private: false,
    description: "Test repository",
    language: "TypeScript",
    size: 1024,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-02T00:00:00Z",
    ingested_at: "2024-01-02T00:00:00Z",
    status: "completed",
    processed_files: 50,
    chunks_created: 200,
    embeddings_generated: 200,
    processing_time: 120,
  }

  const mockRepositoryListResponse: RepositoryListResponse = {
    repositories: [mockRepository],
    total_count: 1,
    page: 1,
    page_size: 20,
    total_pages: 1,
  }

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )

  beforeEach(() => {
    queryClient = createTestQueryClient()
    vi.clearAllMocks()
  })

  afterEach(() => {
    queryClient.clear()
    vi.clearAllMocks()
  })

  describe("useRepositoryList", () => {
    it("should fetch repository list successfully", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockRepositoryListResponse,
      })

      const { result } = renderHook(() => useRepositoryList(), { wrapper })

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      expect(result.current.data).toEqual(mockRepositoryListResponse)
      expect(result.current.error).toBeNull()
    })

    it("should handle query parameters", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockRepositoryListResponse,
      })

      const { result } = renderHook(
        () => useRepositoryList({
          page: 2,
          page_size: 10,
          search_query: "test",
          status_filter: "completed",
          sort_by: "name",
          sort_order: "asc",
        }),
        { wrapper }
      )

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining("page=2"),
        expect.any(Object)
      )
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining("page_size=10"),
        expect.any(Object)
      )
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining("search_query=test"),
        expect.any(Object)
      )
    })

    it("should handle fetch errors", async () => {
      mockFetch.mockRejectedValueOnce(new Error("Network error"))

      const { result } = renderHook(() => useRepositoryList(), { wrapper })

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      expect(result.current.error).toBeTruthy()
      expect(result.current.data).toBeUndefined()
    })

    it("should handle HTTP errors", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: "Internal Server Error",
      })

      const { result } = renderHook(() => useRepositoryList(), { wrapper })

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      expect(result.current.error).toBeTruthy()
    })
  })

  describe("useRepositoryDetails", () => {
    it("should fetch repository details successfully", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockRepository,
      })

      const { result } = renderHook(
        () => useRepositoryDetails({ repository_id: "repo-123" }),
        { wrapper }
      )

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      expect(result.current.data).toEqual(mockRepository)
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining("/repositories/repo-123"),
        expect.any(Object)
      )
    })

    it("should be disabled when repository_id is not provided", () => {
      const { result } = renderHook(
        () => useRepositoryDetails({ repository_id: "" }),
        { wrapper }
      )

      expect(result.current.isLoading).toBe(false)
      expect(result.current.data).toBeUndefined()
      expect(mockFetch).not.toHaveBeenCalled()
    })
  })

  describe("useRepositoryDelete", () => {
    it("should delete repository successfully", async () => {
      const mockResponse = { success: true, repository_id: "repo-123" }
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      })

      const { result } = renderHook(() => useRepositoryDelete(), { wrapper })

      result.current.mutate({
        repository_id: "repo-123",
        force: false,
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockResponse)
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining("/repositories/repo-123"),
        expect.objectContaining({
          method: "DELETE",
        })
      )
    })

    it("should handle delete with force flag", async () => {
      const mockResponse = { success: true, repository_id: "repo-123" }
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      })

      const { result } = renderHook(() => useRepositoryDelete(), { wrapper })

      result.current.mutate({
        repository_id: "repo-123",
        force: true,
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining("force=true"),
        expect.any(Object)
      )
    })

    it("should invalidate queries on success", async () => {
      const mockResponse = { success: true, repository_id: "repo-123" }
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      })

      const invalidateQueriesSpy = vi.spyOn(queryClient, "invalidateQueries")

      const { result } = renderHook(() => useRepositoryDelete(), { wrapper })

      result.current.mutate({
        repository_id: "repo-123",
        force: false,
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(invalidateQueriesSpy).toHaveBeenCalledWith({
        queryKey: ["repositories"],
      })
    })
  })

  describe("useRepositoryReIngest", () => {
    it("should re-ingest repository successfully", async () => {
      const mockResponse = { 
        success: true, 
        repository_id: "repo-123",
        message: "Re-ingestion started"
      }
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      })

      const { result } = renderHook(() => useRepositoryReIngest(), { wrapper })

      result.current.mutate({
        repository_id: "repo-123",
        force_refresh: true,
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockResponse)
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining("/repositories/repo-123/re-ingest"),
        expect.objectContaining({
          method: "POST",
        })
      )
    })

    it("should handle re-ingest options", async () => {
      const mockResponse = { success: true, repository_id: "repo-123" }
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      })

      const { result } = renderHook(() => useRepositoryReIngest(), { wrapper })

      result.current.mutate({
        repository_id: "repo-123",
        force_refresh: true,
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          body: JSON.stringify({ force_refresh: true }),
        })
      )
    })
  })

  describe("useBatchOperation", () => {
    it("should execute batch operation successfully", async () => {
      const mockResponse: BatchOperationResponse = {
        operation: "delete",
        total_requested: 2,
        successful: 2,
        failed: 0,
        results: [
          { repository_id: "repo-1", success: true, message: "Deleted" },
          { repository_id: "repo-2", success: true, message: "Deleted" },
        ],
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      })

      const { result } = renderHook(() => useBatchOperation(), { wrapper })

      result.current.mutate({
        repository_ids: ["repo-1", "repo-2"],
        operation: "delete",
        force: false,
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockResponse)
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining("/repositories/batch"),
        expect.objectContaining({
          method: "POST",
          body: JSON.stringify({
            repository_ids: ["repo-1", "repo-2"],
            operation: "delete",
            force: false,
          }),
        })
      )
    })

    it("should handle different batch operations", async () => {
      const mockResponse: BatchOperationResponse = {
        operation: "re_ingest",
        total_requested: 1,
        successful: 1,
        failed: 0,
        results: [
          { repository_id: "repo-1", success: true, message: "Re-ingestion started" },
        ],
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      })

      const { result } = renderHook(() => useBatchOperation(), { wrapper })

      result.current.mutate({
        repository_ids: ["repo-1"],
        operation: "re_ingest",
        force: true,
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          body: JSON.stringify({
            repository_ids: ["repo-1"],
            operation: "re_ingest",
            force: true,
          }),
        })
      )
    })
  })

  describe("useRepositoryStats", () => {
    it("should fetch repository stats successfully", async () => {
      const mockStats: RepositoryStatsResponse = {
        total_repositories: 5,
        total_files_processed: 1000,
        total_embeddings: 5000,
        storage_usage_mb: 250.5,
        repositories_by_status: {
          completed: 3,
          processing: 1,
          failed: 1,
          pending: 0,
          cancelled: 0,
        },
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockStats,
      })

      const { result } = renderHook(() => useRepositoryStats(), { wrapper })

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      expect(result.current.data).toEqual(mockStats)
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining("/repositories/stats"),
        expect.any(Object)
      )
    })

    it("should handle custom options", async () => {
      const mockStats: RepositoryStatsResponse = {
        total_repositories: 0,
        total_files_processed: 0,
        total_embeddings: 0,
        storage_usage_mb: 0,
        repositories_by_status: {
          completed: 0,
          processing: 0,
          failed: 0,
          pending: 0,
          cancelled: 0,
        },
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockStats,
      })

      const { result } = renderHook(
        () => useRepositoryStats({ 
          refetchInterval: 10000,
          staleTime: 5000,
        }),
        { wrapper }
      )

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      expect(result.current.data).toEqual(mockStats)
    })
  })

  describe("Error Handling", () => {
    it("should handle network errors consistently", async () => {
      const networkError = new Error("Network error")
      mockFetch.mockRejectedValue(networkError)

      const { result } = renderHook(() => useRepositoryList(), { wrapper })

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      expect(result.current.error).toBeTruthy()
    })

    it("should handle HTTP error responses", async () => {
      mockFetch.mockResolvedValue({
        ok: false,
        status: 404,
        statusText: "Not Found",
      })

      const { result } = renderHook(() => useRepositoryList(), { wrapper })

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      expect(result.current.error).toBeTruthy()
    })

    it("should handle malformed JSON responses", async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => {
          throw new Error("Invalid JSON")
        },
      })

      const { result } = renderHook(() => useRepositoryList(), { wrapper })

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      expect(result.current.error).toBeTruthy()
    })
  })

  describe("Caching and Invalidation", () => {
    it("should cache repository list data", async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => mockRepositoryListResponse,
      })

      // First render
      const { result: result1 } = renderHook(() => useRepositoryList(), { wrapper })
      
      await waitFor(() => {
        expect(result1.current.isLoading).toBe(false)
      })

      // Second render should use cached data
      const { result: result2 } = renderHook(() => useRepositoryList(), { wrapper })
      
      expect(result2.current.isLoading).toBe(false)
      expect(result2.current.data).toEqual(mockRepositoryListResponse)
      
      // Should only have called fetch once
      expect(mockFetch).toHaveBeenCalledTimes(1)
    })

    it("should invalidate cache after mutations", async () => {
      // Setup initial data
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockRepositoryListResponse,
      })

      const { result: listResult } = renderHook(() => useRepositoryList(), { wrapper })
      
      await waitFor(() => {
        expect(listResult.current.isLoading).toBe(false)
      })

      // Setup delete mutation
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, repository_id: "repo-123" }),
      })

      const { result: deleteResult } = renderHook(() => useRepositoryDelete(), { wrapper })

      deleteResult.current.mutate({
        repository_id: "repo-123",
        force: false,
      })

      await waitFor(() => {
        expect(deleteResult.current.isSuccess).toBe(true)
      })

      // Cache should be invalidated
      expect(queryClient.getQueryState(["repositories"])).toMatchObject({
        isInvalidated: true,
      })
    })
  })
})
