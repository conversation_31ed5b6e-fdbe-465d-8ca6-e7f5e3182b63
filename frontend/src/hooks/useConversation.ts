/**
 * Conversation Management Hook
 * 
 * This module provides advanced conversation management utilities including
 * history tracking, context management, search, and conversation analytics.
 */

import React from "react"

import type { ConversationMessage, QueryResponse } from "@/lib/api/types"
import { useSession } from "@/providers/session-provider"
import { createMessageId } from "@/lib/api/types"

/**
 * Conversation context for maintaining query context
 */
export interface ConversationContext {
  previousQueries: string[]
  relatedTopics: string[]
  currentTopic?: string
  contextWindow: ConversationMessage[]
}

/**
 * Conversation analytics data
 */
export interface ConversationAnalytics {
  totalMessages: number
  userMessages: number
  assistantMessages: number
  averageResponseTime: number
  topicsDiscussed: string[]
  sessionDuration: number
  lastActivity: Date | null
}

/**
 * Search result for conversation history
 */
export interface ConversationSearchResult {
  message: ConversationMessage
  relevanceScore: number
  context: ConversationMessage[]
}

/**
 * Hook for advanced conversation management
 * 
 * Provides utilities for conversation history, context tracking,
 * search, and analytics beyond basic session management.
 * 
 * @returns Conversation management utilities
 */
export function useConversation() {
  const session = useSession()
  
  /**
   * Add a user message to the conversation
   */
  const addUserMessage = React.useCallback((content: string, metadata?: Record<string, unknown>) => {
    session.addMessage({
      type: "user",
      content,
      metadata,
    })
  }, [session])
  
  /**
   * Add an assistant response to the conversation
   */
  const addAssistantMessage = React.useCallback((
    response: QueryResponse,
    originalQuery?: string
  ) => {
    session.addMessage({
      type: "assistant",
      content: response.result_markdown,
      metadata: {
        agent_type: response.agent_type,
        confidence: response.confidence,
        sources: response.sources,
        processing_time: response.processing_time,
        structured: response.structured,
        original_query: originalQuery,
      },
    })
  }, [session])
  
  /**
   * Add a system message to the conversation
   */
  const addSystemMessage = React.useCallback((content: string, metadata?: Record<string, unknown>) => {
    session.addMessage({
      type: "system",
      content,
      metadata,
    })
  }, [session])
  
  /**
   * Get conversation context for the current session
   */
  const getConversationContext = React.useCallback((windowSize: number = 10): ConversationContext => {
    const history = session.session.conversationHistory
    const contextWindow = history.slice(-windowSize)
    
    // Extract previous queries
    const previousQueries = history
      .filter(msg => msg.type === "user")
      .map(msg => msg.content)
    
    // Extract topics from assistant responses
    const relatedTopics = history
      .filter(msg => msg.type === "assistant" && msg.metadata?.structured)
      .flatMap(msg => {
        const structured = msg.metadata?.structured as any
        return structured?.topics || structured?.components || []
      })
      .filter((topic, index, arr) => arr.indexOf(topic) === index) // Unique topics
    
    // Determine current topic from latest messages
    const recentMessages = history.slice(-3)
    const currentTopic = recentMessages
      .filter(msg => msg.type === "assistant")
      .map(msg => msg.metadata?.structured as any)
      .find(structured => structured?.topic || structured?.main_topic)
      ?.topic || structured?.main_topic
    
    return {
      previousQueries,
      relatedTopics,
      currentTopic,
      contextWindow,
    }
  }, [session.session.conversationHistory])
  
  /**
   * Search conversation history
   */
  const searchConversation = React.useCallback((
    query: string,
    options: {
      includeUserMessages?: boolean
      includeAssistantMessages?: boolean
      includeSystemMessages?: boolean
      maxResults?: number
      contextSize?: number
    } = {}
  ): ConversationSearchResult[] => {
    const {
      includeUserMessages = true,
      includeAssistantMessages = true,
      includeSystemMessages = false,
      maxResults = 10,
      contextSize = 2,
    } = options
    
    const history = session.session.conversationHistory
    const searchTerms = query.toLowerCase().split(/\s+/)
    
    // Filter messages by type
    const filteredMessages = history.filter(msg => {
      if (msg.type === "user" && !includeUserMessages) return false
      if (msg.type === "assistant" && !includeAssistantMessages) return false
      if (msg.type === "system" && !includeSystemMessages) return false
      return true
    })
    
    // Score messages based on relevance
    const scoredMessages = filteredMessages.map((message, index) => {
      const content = message.content.toLowerCase()
      let score = 0
      
      // Exact phrase match
      if (content.includes(query.toLowerCase())) {
        score += 10
      }
      
      // Individual term matches
      searchTerms.forEach(term => {
        const termCount = (content.match(new RegExp(term, "g")) || []).length
        score += termCount * 2
      })
      
      // Boost recent messages
      const recencyBoost = (history.length - index) / history.length
      score += recencyBoost
      
      // Get context messages
      const contextStart = Math.max(0, index - contextSize)
      const contextEnd = Math.min(history.length, index + contextSize + 1)
      const context = history.slice(contextStart, contextEnd)
      
      return {
        message,
        relevanceScore: score,
        context,
      }
    })
    
    // Sort by relevance and return top results
    return scoredMessages
      .filter(result => result.relevanceScore > 0)
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, maxResults)
  }, [session.session.conversationHistory])
  
  /**
   * Get conversation analytics
   */
  const getAnalytics = React.useCallback((): ConversationAnalytics => {
    const history = session.session.conversationHistory
    
    const userMessages = history.filter(msg => msg.type === "user").length
    const assistantMessages = history.filter(msg => msg.type === "assistant").length
    
    // Calculate average response time
    const responseTimes = history
      .filter(msg => msg.type === "assistant" && msg.metadata?.processing_time)
      .map(msg => msg.metadata?.processing_time as number)
    
    const averageResponseTime = responseTimes.length > 0
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
      : 0
    
    // Extract topics discussed
    const topicsDiscussed = history
      .filter(msg => msg.type === "assistant" && msg.metadata?.structured)
      .flatMap(msg => {
        const structured = msg.metadata?.structured as any
        return structured?.topics || structured?.components || []
      })
      .filter((topic, index, arr) => arr.indexOf(topic) === index)
    
    // Calculate session duration
    const sessionDuration = session.session.createdAt
      ? Date.now() - session.session.createdAt.getTime()
      : 0
    
    return {
      totalMessages: history.length,
      userMessages,
      assistantMessages,
      averageResponseTime,
      topicsDiscussed,
      sessionDuration,
      lastActivity: session.lastActivity,
    }
  }, [session.session.conversationHistory, session.session.createdAt, session.lastActivity])
  
  /**
   * Export conversation in different formats
   */
  const exportConversation = React.useCallback((format: "json" | "markdown" | "text" = "json") => {
    const history = session.session.conversationHistory
    
    switch (format) {
      case "markdown":
        return history.map(msg => {
          const timestamp = msg.timestamp.toLocaleString()
          const role = msg.type.charAt(0).toUpperCase() + msg.type.slice(1)
          return `## ${role} (${timestamp})\n\n${msg.content}\n`
        }).join("\n")
      
      case "text":
        return history.map(msg => {
          const timestamp = msg.timestamp.toLocaleString()
          return `[${timestamp}] ${msg.type.toUpperCase()}: ${msg.content}`
        }).join("\n\n")
      
      case "json":
      default:
        return JSON.stringify({
          sessionId: session.session.sessionId,
          userId: session.session.userId,
          createdAt: session.session.createdAt,
          messages: history,
          analytics: getAnalytics(),
        }, null, 2)
    }
  }, [session.session, getAnalytics])
  
  /**
   * Get conversation summary
   */
  const getSummary = React.useCallback(() => {
    const analytics = getAnalytics()
    const context = getConversationContext()
    
    return {
      messageCount: analytics.totalMessages,
      duration: analytics.sessionDuration,
      topicsDiscussed: analytics.topicsDiscussed,
      lastActivity: analytics.lastActivity,
      currentTopic: context.currentTopic,
      queryCount: analytics.userMessages,
      responseCount: analytics.assistantMessages,
      averageResponseTime: analytics.averageResponseTime,
    }
  }, [getAnalytics, getConversationContext])
  
  /**
   * Find related messages based on content similarity
   */
  const findRelatedMessages = React.useCallback((
    messageId: string,
    maxResults: number = 5
  ): ConversationMessage[] => {
    const history = session.session.conversationHistory
    const targetMessage = history.find(msg => msg.id === messageId)
    
    if (!targetMessage) return []
    
    const targetContent = targetMessage.content.toLowerCase()
    const targetWords = targetContent.split(/\s+/)
    
    // Score other messages based on word overlap
    const scoredMessages = history
      .filter(msg => msg.id !== messageId)
      .map(msg => {
        const content = msg.content.toLowerCase()
        const words = content.split(/\s+/)
        
        // Calculate Jaccard similarity
        const intersection = targetWords.filter(word => words.includes(word)).length
        const union = new Set([...targetWords, ...words]).size
        const similarity = intersection / union
        
        return { message: msg, similarity }
      })
      .filter(item => item.similarity > 0.1) // Minimum similarity threshold
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, maxResults)
      .map(item => item.message)
    
    return scoredMessages
  }, [session.session.conversationHistory])
  
  return {
    // Message management
    addUserMessage,
    addAssistantMessage,
    addSystemMessage,
    
    // Context and search
    getConversationContext,
    searchConversation,
    findRelatedMessages,
    
    // Analytics and export
    getAnalytics,
    getSummary,
    exportConversation,
    
    // Direct access to session utilities
    session: session.session,
    messageCount: session.messageCount,
    isActive: session.isActive,
    lastActivity: session.lastActivity,
  }
}
