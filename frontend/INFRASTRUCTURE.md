# Frontend Infrastructure Documentation

## Overview

This document describes the core infrastructure implemented for the LLM RAG Codebase Query System frontend. The infrastructure provides TypeScript API types, comprehensive testing framework, and unified error handling patterns following the 5-phase development methodology.

## 🏗️ Architecture

### Core Components

1. **API Types System** (`src/types/api/`)
   - TypeScript interfaces matching backend Pydantic models exactly
   - Zod schemas for runtime validation
   - Centralized export pattern for clean imports

2. **Error Handling System** (`src/types/errors.ts`, `src/utils/errorUtils.ts`)
   - Comprehensive error type definitions
   - Utility functions for error processing
   - React Error Boundary component
   - Custom error handling hook

3. **Testing Framework** (`src/test/`)
   - Vitest configuration with TypeScript support
   - React Testing Library integration
   - Mock utilities for API testing
   - Test setup with global configurations

## 📁 File Structure

```text
frontend/src/
├── types/
│   ├── api/
│   │   ├── index.ts          # Barrel export for all API types
│   │   ├── common.ts         # Common types and enums
│   │   ├── requests.ts       # Request interface definitions
│   │   ├── responses.ts      # Response interface definitions
│   │   └── schemas.ts        # Zod validation schemas
│   └── errors.ts             # Error type definitions
├── utils/
│   └── errorUtils.ts         # Error utility functions
├── hooks/
│   └── useErrorHandler.ts    # Error handling hook
├── components/
│   └── ErrorBoundary.tsx     # React error boundary
└── test/
    ├── setup.ts              # Global test configuration
    ├── utils.tsx             # Testing utilities
    ├── mocks/
    │   └── api.ts            # API mocking utilities
    └── infrastructure.test.ts # Infrastructure validation tests
```

## 🔧 API Types System

### Type Definitions

All API types are defined to match the backend Pydantic models exactly:

```typescript
// Example usage
import { QueryRequest, QueryResponse, AgentType } from '@/types/api'

const request: QueryRequest = {
  query: "How does authentication work?",
  session_id: "session-123",
  user_id: "user-456"
}
```

### Available Types

#### Request Types

- `QueryRequest` - Natural language query requests
- `IngestionRequest` - Repository ingestion requests

#### Response Types  

- `QueryResponse` - Multi-agent query responses
- `IngestionResponse` - Repository ingestion results
- `StatusResponse` - System health status

#### Common Types

- `AgentType` - Available agent types enum
- `ApiStatus` - API status values enum
- `IngestionStatus` - Ingestion status values enum
- `ContextFilters` - Flexible query filters
- `SourceCitation` - File reference format

### Runtime Validation

Zod schemas provide runtime validation:

```typescript
import { QueryRequestSchema } from '@/types/api/schemas'

const result = QueryRequestSchema.safeParse(data)
if (result.success) {
  // data is validated QueryRequest
  console.log(result.data.query)
}
```

## 🚨 Error Handling System

### Error Types

Comprehensive error type hierarchy:

```typescript
import { ErrorCategory, ErrorSeverity, HttpStatusCode } from '@/types/errors'

// Base error interface
interface BaseError {
  message: string
  category: ErrorCategory
  severity: ErrorSeverity
  timestamp: string
  details?: Record<string, unknown>
}

// Specialized error types
interface ApiError extends BaseError {
  status: HttpStatusCode
  detail: string
  url?: string
  method?: string
}
```

### Error Utilities

Utility functions for error processing:

```typescript
import { 
  createApiError, 
  formatErrorForUser, 
  isRetryableError 
} from '@/utils/errorUtils'

// Create API error from response
const apiError = await createApiError(response, url, method)

// Format for user display
const userMessage = formatErrorForUser(error)

// Check if error is retryable
const canRetry = isRetryableError(error)
```

### Error Handling Hook

Centralized error handling with React hook:

```typescript
import { useErrorHandler } from '@/hooks/useErrorHandler'

function MyComponent() {
  const { handleError, handleApiError, clearError, retry, canRetry } = useErrorHandler({
    showToast: true,
    defaultContext: { component: 'MyComponent' }
  })

  const handleSubmit = async () => {
    try {
      const response = await fetch('/api/query', { method: 'POST' })
      if (!response.ok) {
        await handleApiError(response, '/api/query', 'POST')
        return
      }
      // Handle success
    } catch (error) {
      handleError(error, { action: 'submit' })
    }
  }

  return (
    <div>
      {/* Component JSX */}
      {canRetry && <button onClick={retry}>Retry</button>}
    </div>
  )
}
```

### Error Boundary

React Error Boundary for catching component errors:

```typescript
import { ErrorBoundary } from '@/components/ErrorBoundary'

function App() {
  return (
    <ErrorBoundary 
      context={{ component: 'App' }}
      showErrorDetails={process.env.NODE_ENV === 'development'}
    >
      <MyComponent />
    </ErrorBoundary>
  )
}
```

## 🧪 Testing Framework

### Configuration

Vitest configured with:

- TypeScript support
- React Testing Library integration
- JSDoc environment
- Coverage reporting (80% minimum threshold)
- Path aliases for clean imports

### Testing Utilities

Custom render function with providers:

```typescript
import { render, screen, userEvent } from '@/test/utils'

test('component renders correctly', () => {
  render(<MyComponent />, {
    initialTheme: 'dark',
    withProviders: true
  })
  
  expect(screen.getByText('Hello')).toBeInTheDocument()
})
```

### API Mocking

Comprehensive API mocking utilities:

```typescript
import { 
  mockSuccessfulApiResponses, 
  mockFailedApiResponses,
  createMockApiClient 
} from '@/test/mocks/api'

test('handles API success', async () => {
  mockSuccessfulApiResponses()
  
  // Test component that makes API calls
  render(<QueryForm />)
  // ... test implementation
})

test('handles API errors', async () => {
  mockFailedApiResponses(500)
  
  // Test error handling
  render(<QueryForm />)
  // ... test implementation
})
```

## 📋 Usage Guidelines

### 1. API Integration

When integrating with backend APIs:

```typescript
import { QueryRequest, QueryResponse } from '@/types/api'
import { QueryRequestSchema } from '@/types/api/schemas'
import { useErrorHandler } from '@/hooks/useErrorHandler'

function useQueryApi() {
  const { handleApiError } = useErrorHandler()

  const submitQuery = async (request: QueryRequest) => {
    // Validate request
    const validation = QueryRequestSchema.safeParse(request)
    if (!validation.success) {
      throw new Error('Invalid request data')
    }

    const response = await fetch('/api/query', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request)
    })

    if (!response.ok) {
      await handleApiError(response, '/api/query', 'POST')
      return
    }

    return response.json() as Promise<QueryResponse>
  }

  return { submitQuery }
}
```

### 2. Error Handling

Always use the error handling infrastructure:

```typescript
import { useErrorHandler } from '@/hooks/useErrorHandler'
import { ErrorBoundary } from '@/components/ErrorBoundary'

// Wrap components with error boundaries
function FeatureComponent() {
  return (
    <ErrorBoundary context={{ component: 'FeatureComponent' }}>
      <MyFeature />
    </ErrorBoundary>
  )
}

// Use error handler in components
function MyFeature() {
  const { handleError } = useErrorHandler()

  const handleAction = async () => {
    try {
      // Risky operation
    } catch (error) {
      handleError(error, { action: 'handleAction' })
    }
  }
}
```

### 3. Testing

Follow testing patterns:

```typescript
import { render, screen, userEvent } from '@/test/utils'
import { mockSuccessfulApiResponses } from '@/test/mocks/api'

describe('MyComponent', () => {
  beforeEach(() => {
    mockSuccessfulApiResponses()
  })

  it('should handle user interaction', async () => {
    const user = userEvent.setup()
    
    render(<MyComponent />)
    
    await user.click(screen.getByRole('button', { name: 'Submit' }))
    
    expect(screen.getByText('Success')).toBeInTheDocument()
  })
})
```

## 🔍 Verification

The infrastructure has been verified through:

1. **TypeScript Compilation**: All core types compile without errors
2. **Runtime Validation**: Zod schemas validate correctly
3. **Test Framework**: 15 comprehensive tests pass
4. **Error Handling**: Error utilities work as expected
5. **API Mocking**: Mock utilities function properly

## 🚀 Next Steps

1. **API Client Implementation**: Build HTTP client using these types
2. **Form Validation**: Integrate Zod schemas with form libraries
3. **Error Monitoring**: Add error reporting service integration
4. **Performance Testing**: Add performance tests for large datasets
5. **E2E Testing**: Implement end-to-end tests using this infrastructure

## 📚 References

- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Zod Documentation](https://zod.dev/)
- [Vitest Guide](https://vitest.dev/guide/)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [React Error Boundaries](https://react.dev/reference/react/Component#catching-rendering-errors-with-an-error-boundary)
