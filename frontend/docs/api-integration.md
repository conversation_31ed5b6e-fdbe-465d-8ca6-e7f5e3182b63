# API Integration Layer Documentation

## Overview

The API Integration Layer provides a comprehensive, type-safe interface for communicating with the LLM RAG Codebase Query System backend. It implements the three core deliverables specified in the requirements:

1. **HTTP Client** - Type-safe API client with error handling and retry logic
2. **React Query Hooks** - Custom hooks for query, ingestion, and status operations
3. **Session Management** - Client-side session handling with persistence

## Architecture

### Core Components

```
src/lib/api/
├── client.ts          # HTTP API client with interceptors
├── endpoints.ts       # API endpoint definitions
├── types.ts          # Additional API types and utilities
├── errors.ts         # Error handling and retry strategies
├── interceptors.ts   # Request/response interceptors
└── index.ts          # Barrel export

src/hooks/api/
├── useQuery.ts       # Query-related hooks
├── useIngestion.ts   # Ingestion-related hooks
├── useStatus.ts      # Status monitoring hooks
└── index.ts          # Barrel export

src/providers/
├── query-provider.tsx    # React Query configuration
└── session-provider.tsx  # Session management context

src/lib/
├── session-storage.ts    # Session persistence utilities
└── useConversation.ts    # Advanced conversation management
```

## HTTP Client

### Basic Usage

```typescript
import { apiClient } from "@/lib/api"

// Submit a query
const response = await apiClient.query({
  query: "How does authentication work?",
  session_id: "session-123"
})

// Trigger ingestion
const ingestion = await apiClient.ingest({
  repository_url: "https://github.com/owner/repo",
  branch: "main"
})

// Check system status
const status = await apiClient.getStatus()
```

### Configuration

```typescript
import { createApiClient } from "@/lib/api"

const customClient = createApiClient({
  baseUrl: "https://api.example.com",
  timeout: 10000,
  retries: 3,
  enableLogging: true,
  enableRetry: true
})
```

### Interceptors

The client supports request and response interceptors for cross-cutting concerns:

```typescript
import { apiClient, authInterceptor, requestLoggingInterceptor } from "@/lib/api"

// Add custom interceptors
apiClient.addRequestInterceptor(authInterceptor)
apiClient.addRequestInterceptor(requestLoggingInterceptor)
```

**Built-in Interceptors:**
- `authInterceptor` - Adds authentication headers
- `requestIdInterceptor` - Adds unique request IDs
- `userAgentInterceptor` - Adds client information
- `performanceInterceptor` - Tracks request timing
- `errorTrackingInterceptor` - Logs API errors

### Error Handling

The client provides comprehensive error handling with categorization and retry strategies:

```typescript
import { 
  ApiErrorCategory, 
  isRetryableError, 
  getErrorRecoveryStrategy 
} from "@/lib/api"

try {
  const result = await apiClient.query(request)
} catch (error) {
  const category = categorizeApiError(error.status, error)
  const strategy = getErrorRecoveryStrategy(error)
  
  if (strategy.action === "retry") {
    // Handle retryable error
  } else {
    // Handle non-retryable error
  }
}
```

## React Query Hooks

### Query Operations

```typescript
import { useQueryMutation, useQueryHistory } from "@/hooks/api"

function QueryComponent() {
  const queryMutation = useQueryMutation({
    onSuccess: (response) => {
      console.log("Query successful:", response.result_markdown)
    },
    onError: (error) => {
      console.error("Query failed:", error)
    }
  })

  const { data: history } = useQueryHistory({
    sessionId: "session-123",
    enabled: !!sessionId
  })

  const handleSubmit = (query: string) => {
    queryMutation.mutate({
      query,
      session_id: "session-123"
    })
  }

  return (
    <div>
      {queryMutation.isPending && <div>Processing...</div>}
      {queryMutation.error && <div>Error: {queryMutation.error.message}</div>}
      {queryMutation.data && <div>{queryMutation.data.result_markdown}</div>}
    </div>
  )
}
```

### Ingestion Operations

```typescript
import { useIngestionMutation, useIngestionStatus } from "@/hooks/api"

function IngestionComponent() {
  const ingestionMutation = useIngestionMutation({
    onSuccess: (response) => {
      console.log("Ingestion completed:", response.status)
    }
  })

  const { data: progress } = useIngestionStatus({
    repository: "https://github.com/owner/repo",
    enabled: ingestionMutation.isSuccess,
    pollingInterval: 2000
  })

  return (
    <div>
      <button 
        onClick={() => ingestionMutation.mutate({
          repository_url: "https://github.com/owner/repo",
          branch: "main"
        })}
        disabled={ingestionMutation.isPending}
      >
        Start Ingestion
      </button>
      
      {progress && (
        <div>
          <div>Status: {progress.status}</div>
          <div>Progress: {progress.progress}%</div>
          <div>Files: {progress.filesProcessed}/{progress.totalFiles}</div>
        </div>
      )}
    </div>
  )
}
```

### Status Monitoring

```typescript
import { useSystemStatus, useHealthCheck, useSystemHealth } from "@/hooks/api"

function StatusComponent() {
  const { data: status } = useSystemStatus({
    pollingInterval: 30000,
    enableBackgroundRefetch: true
  })

  const { data: health } = useHealthCheck({
    pollingInterval: 60000
  })

  const { data: systemHealth } = useSystemHealth()

  return (
    <div>
      <div>Overall Health: {systemHealth?.overall}</div>
      <div>API Status: {status?.api}</div>
      <div>Active Sessions: {status?.active_sessions}</div>
      
      {Object.entries(status?.agents || {}).map(([agent, status]) => (
        <div key={agent}>{agent}: {status}</div>
      ))}
    </div>
  )
}
```

## Session Management

### Basic Usage

```typescript
import { useSession } from "@/providers/session-provider"

function ChatComponent() {
  const session = useSession()

  const handleCreateSession = async () => {
    await session.createSession("user-123")
  }

  const handleAddMessage = (content: string) => {
    session.addMessage({
      type: "user",
      content,
      metadata: { timestamp: new Date() }
    })
  }

  return (
    <div>
      <div>Session ID: {session.session.sessionId}</div>
      <div>Messages: {session.messageCount}</div>
      <div>Active: {session.isActive ? "Yes" : "No"}</div>
      
      <button onClick={handleCreateSession}>Create Session</button>
      <button onClick={() => handleAddMessage("Hello!")}>Add Message</button>
      <button onClick={session.clearHistory}>Clear History</button>
    </div>
  )
}
```

### Advanced Conversation Management

```typescript
import { useConversation } from "@/hooks/useConversation"

function ConversationComponent() {
  const conversation = useConversation()

  const handleSearch = (query: string) => {
    const results = conversation.searchConversation(query, {
      includeUserMessages: true,
      includeAssistantMessages: true,
      maxResults: 5
    })
    
    console.log("Search results:", results)
  }

  const analytics = conversation.getAnalytics()
  const context = conversation.getConversationContext()

  return (
    <div>
      <div>Total Messages: {analytics.totalMessages}</div>
      <div>Topics: {analytics.topicsDiscussed.join(", ")}</div>
      <div>Current Topic: {context.currentTopic}</div>
      
      <button onClick={() => handleSearch("authentication")}>
        Search Conversation
      </button>
    </div>
  )
}
```

### Session Persistence

```typescript
import { useSessionStorage } from "@/lib/session-storage"

function PersistenceComponent() {
  const storage = useSessionStorage({
    storageType: "localStorage",
    maxHistorySize: 100,
    enablePersistence: true
  })

  const handleExport = () => {
    const exported = storage.exportSession()
    if (exported) {
      // Download or save the exported data
      console.log("Exported session:", exported)
    }
  }

  const handleBackup = () => {
    const success = storage.backupSession()
    console.log("Backup created:", success)
  }

  return (
    <div>
      <button onClick={handleExport}>Export Session</button>
      <button onClick={handleBackup}>Backup Session</button>
    </div>
  )
}
```

## Configuration

### React Query Setup

The system is pre-configured with optimized React Query settings:

```typescript
// In your app providers
import { QueryProvider } from "@/providers/query-provider"

function App({ children }) {
  return (
    <QueryProvider>
      {children}
    </QueryProvider>
  )
}
```

### Session Provider Setup

```typescript
import { SessionProvider } from "@/providers/session-provider"

function App({ children }) {
  return (
    <SessionProvider 
      enablePersistence={true}
      maxHistorySize={100}
    >
      {children}
    </SessionProvider>
  )
}
```

## Performance Considerations

### Caching Strategy

- **Query responses**: 5-minute stale time, 30-minute cache time
- **Status data**: 30-second stale time with background refetching
- **Health checks**: 1-minute stale time with polling
- **Session data**: Persistent with localStorage backup

### Request Optimization

- Automatic request deduplication
- Intelligent retry logic with exponential backoff
- Request/response compression
- Connection pooling and keep-alive

### Memory Management

- Automatic cleanup of stale cache entries
- Session history trimming (configurable max size)
- Garbage collection of unused query data
- Memory leak prevention in long-running sessions

## Error Handling

### Error Categories

- **Network**: Connection failures, timeouts
- **Client**: 4xx HTTP errors (validation, authentication)
- **Server**: 5xx HTTP errors (internal server errors)
- **Rate Limit**: 429 errors with automatic retry
- **Unknown**: Unexpected errors

### Recovery Strategies

- **Retry**: Automatic retry with exponential backoff
- **Fallback**: Use cached data or default values
- **Fail**: Show error message to user

### User Feedback

All errors are automatically categorized and appropriate user messages are provided through the error handling system.

## Testing

### Unit Tests

```bash
# Run all API tests
pnpm test src/test/api/

# Run specific test suites
pnpm test src/test/api/client.test.ts
pnpm test src/test/api/hooks.test.tsx
pnpm test src/test/session/
```

### Performance Tests

```bash
# Run performance tests
pnpm test src/test/performance/
```

### Integration Tests

The system includes comprehensive integration tests that verify:
- End-to-end API workflows
- Error handling scenarios
- Session persistence
- Performance requirements (<3s response time)

## Troubleshooting

### Common Issues

1. **Request Timeouts**: Check network connectivity and increase timeout configuration
2. **Authentication Errors**: Verify API keys and authentication headers
3. **Session Not Persisting**: Check localStorage availability and permissions
4. **Performance Issues**: Review caching configuration and network conditions

### Debug Mode

Enable debug mode for detailed logging:

```typescript
const client = createApiClient({
  enableLogging: true
})
```

### Monitoring

Access performance and error metrics:

```typescript
import { getPerformanceMetrics, getErrorMetrics } from "@/lib/api"

const performance = getPerformanceMetrics()
const errors = getErrorMetrics()
```

## Migration Guide

### From Direct Fetch

```typescript
// Before
const response = await fetch("/api/query", {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({ query: "test" })
})

// After
const response = await apiClient.query({ query: "test" })
```

### From Custom Hooks

```typescript
// Before
const [data, setData] = useState(null)
const [loading, setLoading] = useState(false)

// After
const { data, isLoading } = useQueryMutation()
```

## Best Practices

1. **Always use TypeScript interfaces** for type safety
2. **Handle loading and error states** in UI components
3. **Use React Query hooks** instead of direct API calls
4. **Implement proper error boundaries** for error handling
5. **Configure appropriate cache times** based on data freshness needs
6. **Use session management** for conversation context
7. **Monitor performance metrics** in production
8. **Test error scenarios** thoroughly

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review test files for usage examples
3. Enable debug mode for detailed logging
4. Check browser developer tools for network issues
