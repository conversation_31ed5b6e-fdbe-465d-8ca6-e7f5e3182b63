# Query Interface Components

This document provides comprehensive documentation for the Query Interface components implemented for the LLM RAG Codebase Query System.

## Overview

The Query Interface consists of three main component groups that work together to provide a complete query experience:

1. **Query Input Components** - Form components for submitting queries
2. **Conversation Components** - Display and manage conversation history
3. **Example Components** - Showcase example queries for each agent type

## Architecture

### Component Hierarchy

```
QueryInterface/
├── Query Components/
│   ├── QueryForm (main container)
│   ├── QueryTextarea (enhanced input)
│   ├── ContextFiltersInput (key-value pairs)
│   ├── RepositorySelector (GitHub URL input)
│   └── SessionOptions (session management)
├── Conversation Components/
│   ├── ConversationHistory (main container)
│   ├── MessageCard (individual messages)
│   └── ConversationControls (session actions)
└── Example Components/
    ├── QueryExamples (main container)
    └── ExampleCard (individual examples)
```

### Design Principles

All components follow SOLID principles and maintain:

- **Single Responsibility**: Each component has a focused purpose
- **Open/Closed**: Extensible through props without modification
- **Liskov Substitution**: Components can be replaced with compatible implementations
- **Interface Segregation**: Props are specific to component needs
- **Dependency Inversion**: Components depend on abstractions (providers, hooks)

## Query Components

### QueryForm

Main form component for submitting natural language queries.

**Features:**
- Real-time validation with Zod schema
- Character count with visual feedback
- Session integration
- Advanced options (repository, context filters)
- Loading states and error handling
- Accessibility support

**Usage:**
```tsx
import { QueryForm } from "@/components/query"

<QueryForm
  onQuerySuccess={(response) => console.log('Success:', response)}
  onQueryError={(error) => console.error('Error:', error)}
  showAdvancedOptions={true}
  initialValues={{ query: "How does auth work?" }}
/>
```

**Props:**
- `onQuerySuccess?: (response: any) => void` - Success callback
- `onQueryError?: (error: Error) => void` - Error callback
- `initialValues?: Partial<QueryFormData>` - Initial form values
- `showAdvancedOptions?: boolean` - Show advanced options
- `className?: string` - Custom CSS class

### QueryTextarea

Enhanced textarea component with character counting and auto-resize.

**Features:**
- Character limit enforcement
- Visual progress indicator
- Auto-resize functionality
- Keyboard shortcuts (Ctrl+Enter to submit)
- Accessibility enhancements

**Usage:**
```tsx
import { QueryTextarea } from "@/components/query"

<QueryTextarea
  maxLength={5000}
  showCharacterCount={true}
  autoResize={true}
  onCharacterCountChange={(count, isNearLimit, isAtLimit) => {
    // Handle character count changes
  }}
/>
```

### ContextFiltersInput

Flexible key-value pair input for context_filters parameter.

**Features:**
- Dynamic add/remove of filter pairs
- JSON value validation
- Predefined suggestions
- Error handling

**Usage:**
```tsx
import { ContextFiltersInput } from "@/components/query"

<ContextFiltersInput
  value={{ file_types: ["py", "ts"], max_results: 10 }}
  onChange={(filters) => console.log('Filters:', filters)}
  suggestions={[
    { key: "file_types", value: '["py", "ts"]', description: "Filter by file extensions" }
  ]}
/>
```

### RepositorySelector

GitHub repository URL input with validation and suggestions.

**Features:**
- GitHub URL validation
- Recent repositories dropdown
- Repository suggestions
- Visual feedback for valid/invalid URLs

**Usage:**
```tsx
import { RepositorySelector } from "@/components/query"

<RepositorySelector
  value="https://github.com/owner/repo"
  onChange={(url) => console.log('Selected:', url)}
  recentRepositories={recentRepos}
  suggestedRepositories={suggestedRepos}
/>
```

### SessionOptions

Session management UI integrated with SessionProvider.

**Features:**
- Current session information display
- User ID management
- Session actions (new, clear, end)
- Session statistics

**Usage:**
```tsx
import { SessionOptions } from "@/components/query"

<SessionOptions
  showAdvanced={true}
  showStats={true}
  allowManagement={true}
/>
```

## Conversation Components

### ConversationHistory

Main container for displaying conversation message history.

**Features:**
- Integration with SessionProvider
- Auto-scroll to new messages
- Scroll management with controls
- Message threading and timestamps
- Responsive design

**Usage:**
```tsx
import { ConversationHistory } from "@/components/conversation"

<ConversationHistory
  maxHeight={600}
  showSessionInfo={true}
  autoScroll={true}
  showTimestamps={true}
  onMessageSelect={(message, index) => console.log('Selected:', message)}
/>
```

### MessageCard

Individual message display component with rich formatting.

**Features:**
- User/assistant message differentiation
- Markdown rendering for assistant responses
- Agent type identification
- Confidence scores and metadata
- Copy to clipboard functionality
- Message rating (thumbs up/down)

**Usage:**
```tsx
import { MessageCard } from "@/components/conversation"

<MessageCard
  message={{
    type: "assistant",
    content: "# Authentication System\n\nThe system uses JWT tokens...",
    metadata: {
      agent_type: "TECHNICAL_ARCHITECT",
      confidence: 0.95,
      sources: ["src/auth/jwt.py:1-50"]
    }
  }}
  index={0}
  showTimestamp={true}
  showActions={true}
  onCopy={(content) => console.log('Copied:', content)}
/>
```

### ConversationControls

Session management controls with export/import functionality.

**Features:**
- Create new session
- Clear conversation history
- Export/import conversation data
- Session settings management
- Confirmation dialogs for destructive actions

**Usage:**
```tsx
import { ConversationControls } from "@/components/conversation"

<ConversationControls
  showExportImport={true}
  showSettings={true}
  onExport={(data) => console.log('Exported:', data)}
  onImport={(data) => console.log('Imported:', data)}
/>
```

## Example Components

### QueryExamples

Main container for example queries with search and filtering.

**Features:**
- Agent-specific example categorization
- Search and filtering functionality
- Grid and list view modes
- Example insertion into query form

**Usage:**
```tsx
import { QueryExamples } from "@/components/examples"
import { queryExamples } from "@/data/queryExamples"

<QueryExamples
  examples={queryExamples}
  onExampleSelect={(example) => console.log('Selected:', example)}
  onQueryInsert={(query) => setQueryValue(query)}
  showSearch={true}
  showFilters={true}
  groupByAgent={true}
/>
```

### ExampleCard

Individual example display with agent identification and actions.

**Features:**
- Agent type identification with icons
- Difficulty level indicators
- Tag display and categorization
- Query preview and full view
- Copy to clipboard functionality

**Usage:**
```tsx
import { ExampleCard } from "@/components/examples"

<ExampleCard
  example={{
    id: "ta-001",
    title: "Authentication Analysis",
    query: "How does the authentication system work?",
    agentType: "TECHNICAL_ARCHITECT",
    category: "Security",
    tags: ["auth", "security", "jwt"],
    difficulty: "intermediate"
  }}
  variant="detailed"
  onQueryInsert={(query) => setQueryValue(query)}
/>
```

## Data Management

### Query Examples Data

The system includes curated examples for each agent type:

- **Technical Architect**: Architecture analysis, design patterns, code structure
- **Task Planner**: Project breakdown, task estimation, dependency analysis
- **RAG Retrieval**: Code search, documentation queries, specific file lookups

Examples are defined in `@/data/queryExamples.ts` with helper functions:

```tsx
import { 
  queryExamples, 
  getExamplesByAgent, 
  searchExamples 
} from "@/data/queryExamples"

// Get all examples
const allExamples = queryExamples

// Filter by agent type
const techArchExamples = getExamplesByAgent("TECHNICAL_ARCHITECT")

// Search examples
const authExamples = searchExamples("authentication")
```

## Integration Patterns

### Provider Integration

Components integrate with existing providers:

```tsx
import { SessionProvider } from "@/providers/session-provider"
import { QueryProvider } from "@/providers/query-provider"

<QueryProvider>
  <SessionProvider>
    <QueryForm />
    <ConversationHistory />
    <QueryExamples />
  </SessionProvider>
</QueryProvider>
```

### API Integration

Components use the existing API hooks:

```tsx
import { useQueryMutation } from "@/hooks/api"

const queryMutation = useQueryMutation({
  onSuccess: (response) => {
    // Handle success
  },
  onError: (error) => {
    // Handle error
  }
})
```

### Form Validation

All form components use Zod for validation:

```tsx
import { z } from "zod"
import { QueryRequestSchema } from "@/types/api/schemas"

const FormSchema = QueryRequestSchema.extend({
  // Additional validation rules
})
```

## Accessibility

All components implement comprehensive accessibility features:

- **Keyboard Navigation**: Full keyboard support for all interactions
- **Screen Reader Support**: Proper ARIA labels and descriptions
- **Focus Management**: Logical focus order and visible focus indicators
- **Color Contrast**: WCAG AA compliance for all text and interactive elements
- **Live Regions**: Dynamic content announcements

## Testing

Comprehensive test coverage includes:

- **Unit Tests**: Individual component functionality
- **Integration Tests**: Component interaction workflows
- **Accessibility Tests**: ARIA compliance and keyboard navigation
- **Validation Tests**: Form validation and error handling

Run tests with:
```bash
pnpm test components/query
pnpm test components/conversation
pnpm test components/examples
pnpm test integration/QueryInterface
```

## Performance Considerations

- **Code Splitting**: Components are lazy-loaded where appropriate
- **Memoization**: React.memo and useMemo for expensive operations
- **Virtual Scrolling**: For large conversation histories
- **Debounced Search**: Search input debouncing for performance

## Future Enhancements

Potential improvements for future iterations:

1. **Voice Input**: Speech-to-text for query input
2. **Query Templates**: Saved query templates for common patterns
3. **Collaborative Sessions**: Multi-user conversation sessions
4. **Advanced Filtering**: More sophisticated context filter options
5. **Query History**: Personal query history and favorites
6. **Offline Support**: Cached responses for offline viewing

## Troubleshooting

### Common Issues

**Form Validation Errors**
- Ensure Zod schemas match API requirements
- Check for proper error message display
- Verify ARIA attributes for accessibility

**Session Management Issues**
- Verify SessionProvider is properly configured
- Check session persistence settings
- Ensure proper error handling for session failures

**Performance Issues**
- Monitor component re-renders with React DevTools
- Check for unnecessary API calls
- Verify proper memoization usage

**Accessibility Issues**
- Test with screen readers
- Verify keyboard navigation
- Check color contrast ratios
- Validate ARIA attributes
