# Query Interface Usage Guide

This guide provides practical examples and best practices for using the Query Interface components in your application.

## Quick Start

### Basic Setup

1. **Install Dependencies**
   ```bash
   pnpm install
   ```

2. **Import Components**
   ```tsx
   import { QueryForm, ConversationHistory, QueryExamples } from "@/components"
   import { SessionProvider, QueryProvider } from "@/providers"
   ```

3. **Basic Implementation**
   ```tsx
   import React from "react"
   import { QueryForm, ConversationHistory, QueryExamples } from "@/components"
   import { SessionProvider, QueryProvider } from "@/providers"
   import { queryExamples } from "@/data/queryExamples"

   export function QueryInterface() {
     return (
       <QueryProvider>
         <SessionProvider>
           <div className="container mx-auto p-6 space-y-6">
             <QueryForm showAdvancedOptions={true} />
             <ConversationHistory maxHeight={600} />
             <QueryExamples 
               examples={queryExamples} 
               groupByAgent={true}
               showSearch={true}
             />
           </div>
         </SessionProvider>
       </QueryProvider>
     )
   }
   ```

## Common Use Cases

### 1. Simple Query Form

For a basic query interface without advanced options:

```tsx
import { QueryForm } from "@/components/query"

export function SimpleQueryForm() {
  const handleSuccess = (response) => {
    console.log("Query successful:", response)
  }

  const handleError = (error) => {
    console.error("Query failed:", error)
  }

  return (
    <QueryForm
      onQuerySuccess={handleSuccess}
      onQueryError={handleError}
      showAdvancedOptions={false}
    />
  )
}
```

### 2. Advanced Query Form with Custom Validation

```tsx
import { QueryForm } from "@/components/query"
import { useToast } from "@/hooks/useToast"

export function AdvancedQueryForm() {
  const { toast } = useToast()

  const handleSuccess = (response) => {
    toast({
      title: "Query Processed",
      description: `Handled by ${response.agent_type} agent`,
    })
  }

  const handleError = (error) => {
    toast({
      title: "Query Failed",
      description: error.message,
      variant: "destructive",
    })
  }

  return (
    <QueryForm
      onQuerySuccess={handleSuccess}
      onQueryError={handleError}
      showAdvancedOptions={true}
      initialValues={{
        query: "",
        repository: "https://github.com/my-org/my-repo",
      }}
    />
  )
}
```

### 3. Conversation History with Custom Message Renderer

```tsx
import { ConversationHistory, MessageCard } from "@/components/conversation"

export function CustomConversationHistory() {
  const customMessageRenderer = (message, index) => {
    return (
      <MessageCard
        key={index}
        message={message}
        index={index}
        showTimestamp={true}
        showActions={true}
        showConfidence={true}
        onCopy={(content) => {
          navigator.clipboard.writeText(content)
        }}
        onRate={(rating) => {
          console.log(`Message ${index} rated: ${rating}`)
        }}
      />
    )
  }

  return (
    <ConversationHistory
      maxHeight={800}
      showSessionInfo={true}
      autoScroll={true}
      messageRenderer={customMessageRenderer}
      onMessageSelect={(message, index) => {
        console.log("Selected message:", message, "at index:", index)
      }}
    />
  )
}
```

### 4. Filtered Query Examples

```tsx
import { QueryExamples } from "@/components/examples"
import { queryExamples, getExamplesByAgent } from "@/data/queryExamples"

export function FilteredQueryExamples() {
  const [selectedAgent, setSelectedAgent] = useState("TECHNICAL_ARCHITECT")
  
  const filteredExamples = getExamplesByAgent(selectedAgent)

  const handleQueryInsert = (query) => {
    // Insert query into form
    setQueryValue(query)
  }

  return (
    <div className="space-y-4">
      <div className="flex gap-2">
        <button 
          onClick={() => setSelectedAgent("TECHNICAL_ARCHITECT")}
          className={selectedAgent === "TECHNICAL_ARCHITECT" ? "active" : ""}
        >
          Technical Architect
        </button>
        <button 
          onClick={() => setSelectedAgent("TASK_PLANNER")}
          className={selectedAgent === "TASK_PLANNER" ? "active" : ""}
        >
          Task Planner
        </button>
        <button 
          onClick={() => setSelectedAgent("RAG_RETRIEVAL")}
          className={selectedAgent === "RAG_RETRIEVAL" ? "active" : ""}
        >
          RAG Retrieval
        </button>
      </div>
      
      <QueryExamples
        examples={filteredExamples}
        onQueryInsert={handleQueryInsert}
        showSearch={true}
        showFilters={false}
        groupByAgent={false}
      />
    </div>
  )
}
```

## Integration Patterns

### 1. State Management Integration

```tsx
import { useReducer } from "react"
import { QueryForm, ConversationHistory } from "@/components"

const queryReducer = (state, action) => {
  switch (action.type) {
    case "SET_QUERY":
      return { ...state, query: action.payload }
    case "SET_LOADING":
      return { ...state, loading: action.payload }
    case "ADD_MESSAGE":
      return { 
        ...state, 
        messages: [...state.messages, action.payload] 
      }
    default:
      return state
  }
}

export function StateManagedQueryInterface() {
  const [state, dispatch] = useReducer(queryReducer, {
    query: "",
    loading: false,
    messages: [],
  })

  const handleQuerySuccess = (response) => {
    dispatch({ type: "SET_LOADING", payload: false })
    dispatch({ 
      type: "ADD_MESSAGE", 
      payload: {
        type: "assistant",
        content: response.result_markdown,
        metadata: response,
      }
    })
  }

  return (
    <div className="space-y-6">
      <QueryForm
        onQuerySuccess={handleQuerySuccess}
        initialValues={{ query: state.query }}
      />
      <ConversationHistory />
    </div>
  )
}
```

### 2. Custom Hook Integration

```tsx
import { useQuery } from "@/hooks/useQuery"
import { QueryForm, ConversationHistory } from "@/components"

export function HookIntegratedInterface() {
  const {
    query,
    setQuery,
    submitQuery,
    isLoading,
    error,
    conversation,
  } = useQuery()

  return (
    <div className="space-y-6">
      <QueryForm
        initialValues={{ query }}
        onQuerySuccess={(response) => {
          // Handle through custom hook
        }}
      />
      
      {error && (
        <div className="alert alert-error">
          {error.message}
        </div>
      )}
      
      <ConversationHistory />
    </div>
  )
}
```

### 3. Theme Integration

```tsx
import { QueryForm } from "@/components/query"
import { useTheme } from "@/providers/theme-provider"

export function ThemedQueryInterface() {
  const { theme } = useTheme()

  return (
    <div className={`query-interface theme-${theme}`}>
      <QueryForm
        className={theme === "dark" ? "dark-mode" : "light-mode"}
        showAdvancedOptions={true}
      />
    </div>
  )
}
```

## Best Practices

### 1. Error Handling

```tsx
import { QueryForm } from "@/components/query"
import { useToast } from "@/hooks/useToast"

export function RobustQueryForm() {
  const { toast } = useToast()

  const handleError = (error) => {
    // Log error for debugging
    console.error("Query error:", error)

    // Show user-friendly message
    toast({
      title: "Query Failed",
      description: getErrorMessage(error),
      variant: "destructive",
    })

    // Report to error tracking service
    reportError(error)
  }

  const getErrorMessage = (error) => {
    if (error.code === "NETWORK_ERROR") {
      return "Please check your internet connection and try again."
    }
    if (error.code === "VALIDATION_ERROR") {
      return "Please check your query and try again."
    }
    return "An unexpected error occurred. Please try again."
  }

  return (
    <QueryForm
      onQueryError={handleError}
      showAdvancedOptions={true}
    />
  )
}
```

### 2. Performance Optimization

```tsx
import { memo, useMemo, useCallback } from "react"
import { ConversationHistory } from "@/components/conversation"

export const OptimizedConversationHistory = memo(function OptimizedConversationHistory({
  messages,
  onMessageSelect,
}) {
  const memoizedMessages = useMemo(() => {
    return messages.map((message, index) => ({
      ...message,
      id: `${message.type}-${index}`,
    }))
  }, [messages])

  const handleMessageSelect = useCallback((message, index) => {
    onMessageSelect?.(message, index)
  }, [onMessageSelect])

  return (
    <ConversationHistory
      messageRenderer={(message, index) => (
        <OptimizedMessageCard
          key={message.id}
          message={message}
          index={index}
          onSelect={handleMessageSelect}
        />
      )}
    />
  )
})
```

### 3. Accessibility Best Practices

```tsx
import { QueryForm } from "@/components/query"
import { useAnnouncer } from "@/hooks/useAnnouncer"

export function AccessibleQueryForm() {
  const { announce } = useAnnouncer()

  const handleSuccess = (response) => {
    announce(`Query processed successfully by ${response.agent_type} agent`)
  }

  const handleError = (error) => {
    announce(`Query failed: ${error.message}`, "assertive")
  }

  return (
    <QueryForm
      onQuerySuccess={handleSuccess}
      onQueryError={handleError}
      aria-label="Submit query to AI agents"
    />
  )
}
```

### 4. Testing Integration

```tsx
import { render, screen, userEvent } from "@testing-library/react"
import { QueryForm } from "@/components/query"

export function TestableQueryForm(props) {
  return (
    <QueryForm
      data-testid="query-form"
      {...props}
    />
  )
}

// Test usage
test("submits query successfully", async () => {
  const user = userEvent.setup()
  const onSuccess = jest.fn()

  render(<TestableQueryForm onQuerySuccess={onSuccess} />)

  const input = screen.getByRole("textbox", { name: /query/i })
  const button = screen.getByRole("button", { name: /submit/i })

  await user.type(input, "How does authentication work?")
  await user.click(button)

  expect(onSuccess).toHaveBeenCalled()
})
```

## Customization Examples

### 1. Custom Styling

```tsx
import { QueryForm } from "@/components/query"
import "./custom-query-form.css"

export function CustomStyledQueryForm() {
  return (
    <QueryForm
      className="custom-query-form"
      showAdvancedOptions={true}
    />
  )
}
```

```css
/* custom-query-form.css */
.custom-query-form {
  --primary-color: #your-brand-color;
  --border-radius: 12px;
  --spacing: 1.5rem;
}

.custom-query-form .query-textarea {
  border-radius: var(--border-radius);
  border-color: var(--primary-color);
}

.custom-query-form .submit-button {
  background-color: var(--primary-color);
  border-radius: var(--border-radius);
}
```

### 2. Custom Validation

```tsx
import { QueryForm } from "@/components/query"
import { z } from "zod"

const customValidationSchema = z.object({
  query: z.string()
    .min(10, "Query must be at least 10 characters")
    .max(1000, "Query must be less than 1000 characters")
    .refine(
      (val) => !val.includes("forbidden"),
      "Query contains forbidden content"
    ),
})

export function CustomValidatedQueryForm() {
  return (
    <QueryForm
      validationSchema={customValidationSchema}
      showAdvancedOptions={true}
    />
  )
}
```

This guide provides comprehensive examples for implementing and customizing the Query Interface components. For more detailed API documentation, refer to the component documentation files.
