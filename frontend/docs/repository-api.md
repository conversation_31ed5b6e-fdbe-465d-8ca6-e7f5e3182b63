# Repository Management API Reference

## Overview

This document provides detailed information about the Repository Management API endpoints, request/response formats, and integration patterns.

## Base Configuration

```typescript
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000"
const API_ENDPOINTS = {
  repositories: "/api/repositories",
  repositoryDetails: (id: string) => `/api/repositories/${id}`,
  repositoryDelete: (id: string) => `/api/repositories/${id}`,
  repositoryReIngest: (id: string) => `/api/repositories/${id}/re-ingest`,
  batchOperations: "/api/repositories/batch",
  repositoryStats: "/api/repositories/stats",
  ingestionProgress: (id: string) => `/api/repositories/${id}/progress`,
}
```

## Endpoints

### 1. List Repositories

**GET** `/api/repositories`

Retrieve a paginated list of repositories with optional filtering and sorting.

#### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `page` | number | No | Page number (default: 1) |
| `page_size` | number | No | Items per page (default: 20, max: 100) |
| `search_query` | string | No | Search repositories by name or description |
| `status_filter` | RepositoryStatus | No | Filter by repository status |
| `sort_by` | string | No | Sort field (name, updated_at, created_at) |
| `sort_order` | "asc" \| "desc" | No | Sort direction (default: desc) |

#### Response

```typescript
interface RepositoryListResponse {
  repositories: RepositoryMetadata[]
  total_count: number
  page: number
  page_size: number
  total_pages: number
}

interface RepositoryMetadata {
  id: string
  url: URL
  name: string
  owner: string
  branch: string
  commit_sha: string
  is_private: boolean
  description?: string
  language?: string
  size: number
  created_at: string
  updated_at: string
  ingested_at: string
  status: RepositoryStatus
  processed_files: number
  chunks_created: number
  embeddings_generated: number
  processing_time: number
}

type RepositoryStatus = "pending" | "processing" | "completed" | "failed" | "cancelled"
```

#### Example

```typescript
const { data } = await fetch(`${API_BASE_URL}/api/repositories?page=1&page_size=20&status_filter=completed`)
const repositories: RepositoryListResponse = await data.json()
```

### 2. Get Repository Details

**GET** `/api/repositories/{repository_id}`

Retrieve detailed information about a specific repository.

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `repository_id` | string | Yes | Unique repository identifier |

#### Response

```typescript
// Returns RepositoryMetadata object (same as in list response)
```

#### Example

```typescript
const response = await fetch(`${API_BASE_URL}/api/repositories/repo-123`)
const repository: RepositoryMetadata = await response.json()
```

### 3. Delete Repository

**DELETE** `/api/repositories/{repository_id}`

Delete a repository and all associated data.

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `repository_id` | string | Yes | Unique repository identifier |

#### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `force` | boolean | No | Force deletion even if processing (default: false) |

#### Response

```typescript
interface RepositoryDeleteResponse {
  success: boolean
  repository_id: string
  message: string
}
```

#### Example

```typescript
const response = await fetch(`${API_BASE_URL}/api/repositories/repo-123?force=true`, {
  method: "DELETE"
})
const result: RepositoryDeleteResponse = await response.json()
```

### 4. Re-ingest Repository

**POST** `/api/repositories/{repository_id}/re-ingest`

Start re-ingestion process for an existing repository.

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `repository_id` | string | Yes | Unique repository identifier |

#### Request Body

```typescript
interface RepositoryReIngestRequest {
  force_refresh?: boolean
  branch?: string
}
```

#### Response

```typescript
interface RepositoryReIngestResponse {
  success: boolean
  repository_id: string
  message: string
  estimated_completion?: string
}
```

#### Example

```typescript
const response = await fetch(`${API_BASE_URL}/api/repositories/repo-123/re-ingest`, {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({ force_refresh: true })
})
const result: RepositoryReIngestResponse = await response.json()
```

### 5. Batch Operations

**POST** `/api/repositories/batch`

Perform bulk operations on multiple repositories.

#### Request Body

```typescript
interface BatchOperationRequest {
  repository_ids: string[]
  operation: "delete" | "re_ingest"
  force?: boolean
}
```

#### Response

```typescript
interface BatchOperationResponse {
  operation: "delete" | "re_ingest"
  total_requested: number
  successful: number
  failed: number
  results: Array<{
    repository_id: string
    success: boolean
    message?: string
    error?: string
  }>
}
```

#### Example

```typescript
const response = await fetch(`${API_BASE_URL}/api/repositories/batch`, {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({
    repository_ids: ["repo-1", "repo-2", "repo-3"],
    operation: "delete",
    force: false
  })
})
const result: BatchOperationResponse = await response.json()
```

### 6. Repository Statistics

**GET** `/api/repositories/stats`

Get aggregate statistics about all repositories.

#### Response

```typescript
interface RepositoryStatsResponse {
  total_repositories: number
  total_files_processed: number
  total_embeddings: number
  storage_usage_mb: number
  repositories_by_status: {
    completed: number
    processing: number
    failed: number
    pending: number
    cancelled: number
  }
}
```

#### Example

```typescript
const response = await fetch(`${API_BASE_URL}/api/repositories/stats`)
const stats: RepositoryStatsResponse = await response.json()
```

### 7. Ingestion Progress

**GET** `/api/repositories/{repository_id}/progress`

Get real-time ingestion progress for a repository.

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `repository_id` | string | Yes | Unique repository identifier |

#### Response

```typescript
interface IngestionProgress {
  repository_id: string
  status: RepositoryStatus
  stage: IngestionStage
  current_step: string
  progress_percentage: number
  elapsed_time: number
  estimated_completion?: string
  files_discovered: number
  files_processed: number
  files_filtered: number
  chunks_created: number
  embeddings_generated: number
  errors: string[]
  warnings: string[]
}

type IngestionStage = 
  | "initializing"
  | "cloning"
  | "scanning_files"
  | "filtering_files"
  | "extracting_metadata"
  | "chunking_content"
  | "generating_embeddings"
  | "storing_vectors"
  | "finalizing"
  | "completed"
```

#### Example

```typescript
const response = await fetch(`${API_BASE_URL}/api/repositories/repo-123/progress`)
const progress: IngestionProgress = await response.json()
```

## Error Responses

All endpoints return consistent error responses:

```typescript
interface ApiError {
  error: string
  message: string
  details?: Record<string, any>
  timestamp: string
}
```

### HTTP Status Codes

| Code | Description | Common Causes |
|------|-------------|---------------|
| 400 | Bad Request | Invalid parameters, malformed request |
| 401 | Unauthorized | Missing or invalid authentication |
| 403 | Forbidden | Insufficient permissions |
| 404 | Not Found | Repository not found |
| 409 | Conflict | Repository already being processed |
| 429 | Too Many Requests | Rate limit exceeded |
| 500 | Internal Server Error | Server-side error |

### Error Examples

```typescript
// 400 Bad Request
{
  "error": "VALIDATION_ERROR",
  "message": "Invalid repository URL format",
  "details": {
    "field": "repository_url",
    "value": "invalid-url"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}

// 409 Conflict
{
  "error": "REPOSITORY_PROCESSING",
  "message": "Repository is currently being processed",
  "details": {
    "repository_id": "repo-123",
    "current_stage": "generating_embeddings"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Authentication

### GitHub Token Authentication

For private repositories, include GitHub personal access token:

```typescript
const headers = {
  "Authorization": `Bearer ${githubToken}`,
  "Content-Type": "application/json"
}
```

### API Key Authentication

For API access, include API key in headers:

```typescript
const headers = {
  "X-API-Key": process.env.API_KEY,
  "Content-Type": "application/json"
}
```

## Rate Limiting

- **Repository List**: 100 requests per minute
- **Repository Details**: 200 requests per minute
- **Mutations**: 50 requests per minute
- **Progress Tracking**: 300 requests per minute

Rate limit headers are included in responses:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642248600
```

## Webhooks (Future)

Planned webhook support for real-time updates:

```typescript
interface WebhookPayload {
  event: "repository.ingestion.started" | "repository.ingestion.completed" | "repository.ingestion.failed"
  repository_id: string
  timestamp: string
  data: Record<string, any>
}
```

## SDK Usage

### React Query Integration

```typescript
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"

// List repositories
const useRepositoryList = (params: RepositoryListParams) => {
  return useQuery({
    queryKey: ["repositories", params],
    queryFn: () => fetchRepositories(params),
    staleTime: 30000, // 30 seconds
  })
}

// Delete repository
const useRepositoryDelete = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: deleteRepository,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["repositories"] })
    },
  })
}
```

### Error Handling

```typescript
import { RepositoryErrorHandler } from "@/lib/errors/repository-errors"

try {
  const response = await fetch(url, options)
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`)
  }
  return await response.json()
} catch (error) {
  const repositoryError = RepositoryErrorHandler.parseError(error)
  
  if (repositoryError.retryable) {
    // Implement retry logic
    return RepositoryErrorHandler.createRetryFunction(originalFunction)()
  }
  
  throw repositoryError
}
```

## Testing

### Mock API Responses

```typescript
// Test utilities
export const mockApiResponses = {
  repositoryList: (overrides = {}) => ({
    repositories: [mockRepositoryData],
    total_count: 1,
    page: 1,
    page_size: 20,
    total_pages: 1,
    ...overrides
  }),
  
  repositoryProgress: (overrides = {}) => ({
    repository_id: "repo-123",
    status: "processing",
    stage: "chunking_content",
    progress_percentage: 65.5,
    ...overrides
  })
}

// Mock fetch for testing
global.fetch = vi.fn().mockImplementation((url) => {
  if (url.includes("/repositories")) {
    return Promise.resolve({
      ok: true,
      json: () => Promise.resolve(mockApiResponses.repositoryList())
    })
  }
})
```

## Performance Optimization

### Caching Strategy

```typescript
// Repository list - cache for 30 seconds
const repositoryListQuery = {
  staleTime: 30000,
  cacheTime: 300000, // 5 minutes
}

// Repository details - cache for 5 minutes
const repositoryDetailsQuery = {
  staleTime: 300000,
  cacheTime: 600000, // 10 minutes
}

// Progress data - no caching (real-time)
const progressQuery = {
  staleTime: 0,
  cacheTime: 0,
  refetchInterval: 2000, // Poll every 2 seconds
}
```

### Request Optimization

```typescript
// Debounced search
const debouncedSearch = useMemo(() => {
  return debounce((query: string) => {
    setSearchQuery(query)
  }, 300)
}, [])

// Batch requests
const batchRequests = async (repositoryIds: string[]) => {
  // Use batch endpoint instead of individual requests
  return await batchOperation({ repository_ids: repositoryIds })
}
```
