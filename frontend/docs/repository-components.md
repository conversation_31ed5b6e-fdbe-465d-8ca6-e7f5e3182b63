# Repository Components Reference

## Overview

This document provides detailed information about all repository management components, their props, usage patterns, and examples.

## Components

### RepositoryList

Main component for displaying a list of repositories with search, filtering, and pagination capabilities.

#### Props

```typescript
interface RepositoryListProps {
  className?: string
  onSelectionChange?: (selectedIds: string[]) => void
  onRepositoryAction?: (action: string, repository: RepositoryMetadata) => void
  showSelection?: boolean
  showControls?: boolean
  initialPageSize?: number
}
```

#### Usage

```tsx
import { RepositoryList } from "@/components/repository"

function RepositoryManagementPage() {
  const [selectedIds, setSelectedIds] = useState<string[]>([])
  
  const handleRepositoryAction = (action: string, repository: RepositoryMetadata) => {
    switch (action) {
      case "view":
        // Handle view action
        break
      case "re-ingest":
        // Handle re-ingest action
        break
      case "delete":
        // Handle delete action
        break
    }
  }
  
  return (
    <RepositoryList
      showSelection={true}
      showControls={true}
      onSelectionChange={setSelectedIds}
      onRepositoryAction={handleRepositoryAction}
      initialPageSize={20}
    />
  )
}
```

#### Features

- **Search**: Debounced search across repository names and descriptions
- **Filtering**: Filter by repository status (completed, processing, failed, etc.)
- **Sorting**: Sort by name, status, or last updated date
- **Pagination**: Configurable page sizes with navigation controls
- **Selection**: Individual and bulk selection with select-all functionality
- **Actions**: Dropdown menu with view, re-ingest, and delete options

#### Accessibility

- Full keyboard navigation support
- ARIA labels for all interactive elements
- Screen reader announcements for status changes
- High contrast mode support

---

### IngestionProgress

Real-time progress tracking component for repository ingestion operations.

#### Props

```typescript
interface IngestionProgressProps {
  repositoryId: RepositoryId
  className?: string
  onRetry?: (repositoryId: RepositoryId) => void
  onComplete?: (repositoryId: RepositoryId) => void
  showDetails?: boolean
  autoRefresh?: boolean
  refreshInterval?: number
}
```

#### Usage

```tsx
import { IngestionProgress } from "@/components/repository"

function ProgressTrackingPage() {
  const handleRetry = (repositoryId: string) => {
    // Implement retry logic
    console.log("Retrying ingestion for:", repositoryId)
  }
  
  const handleComplete = (repositoryId: string) => {
    // Handle completion
    toast.success(`Repository ${repositoryId} ingestion completed!`)
  }
  
  return (
    <IngestionProgress
      repositoryId="repo-123"
      showDetails={true}
      autoRefresh={true}
      refreshInterval={2000}
      onRetry={handleRetry}
      onComplete={handleComplete}
    />
  )
}
```

#### Features

- **Real-time Updates**: Automatic polling with configurable intervals
- **Progress Visualization**: Progress bar with percentage and stage information
- **Detailed Statistics**: File counts, processing times, and content metrics
- **Error Reporting**: Display errors and warnings with actionable suggestions
- **Retry Functionality**: Built-in retry mechanism for failed operations
- **Stage Tracking**: Visual indicators for each processing stage

#### Progress Stages

1. **Initializing**: Setting up the ingestion process
2. **Cloning**: Downloading repository content
3. **Scanning Files**: Discovering all files in the repository
4. **Filtering Files**: Applying file type and size filters
5. **Extracting Metadata**: Gathering file and repository metadata
6. **Chunking Content**: Breaking content into processable chunks
7. **Generating Embeddings**: Creating vector embeddings for content
8. **Storing Vectors**: Saving embeddings to the vector database
9. **Finalizing**: Completing the ingestion process

---

### RepositoryActions

Component for individual repository operations with confirmation dialogs.

#### Props

```typescript
interface RepositoryActionsProps {
  repository?: RepositoryMetadata
  selectedRepositoryIds?: RepositoryId[]
  className?: string
  onActionComplete?: (action: string, repositoryId?: RepositoryId) => void
  onViewDetails?: (repository: RepositoryMetadata) => void
  showBulkActions?: boolean
  disabled?: boolean
}
```

#### Usage

```tsx
import { RepositoryActions } from "@/components/repository"

function RepositoryDetailsPage({ repository }: { repository: RepositoryMetadata }) {
  const handleActionComplete = (action: string, repositoryId?: string) => {
    toast.success(`${action} completed successfully`)
    // Refresh data or navigate
  }
  
  const handleViewDetails = (repo: RepositoryMetadata) => {
    // Navigate to details page or open modal
    router.push(`/repositories/${repo.id}`)
  }
  
  return (
    <RepositoryActions
      repository={repository}
      onActionComplete={handleActionComplete}
      onViewDetails={handleViewDetails}
      disabled={repository.status === "processing"}
    />
  )
}
```

#### Features

- **Individual Actions**: View, re-ingest, and delete operations
- **Confirmation Dialogs**: Safe operation confirmation with detailed information
- **Force Options**: Override processing status for critical operations
- **Status Awareness**: Disable actions based on repository status
- **External Links**: Direct links to GitHub repository

---

### BatchOperations

Advanced bulk operations component with progress tracking and detailed confirmation.

#### Props

```typescript
interface BatchOperationsProps {
  selectedRepositories: RepositoryMetadata[]
  className?: string
  onOperationComplete?: (operation: BatchOperationType, results: BatchOperationStatus) => void
  onClearSelection?: () => void
  disabled?: boolean
}
```

#### Usage

```tsx
import { BatchOperations } from "@/components/repository"

function BulkActionsPage() {
  const [selectedRepos, setSelectedRepos] = useState<RepositoryMetadata[]>([])
  
  const handleOperationComplete = (operation: BatchOperationType, results: BatchOperationStatus) => {
    toast.success(`Batch ${operation} completed: ${results.completed} successful, ${results.failed} failed`)
    setSelectedRepos([]) // Clear selection
  }
  
  return (
    <BatchOperations
      selectedRepositories={selectedRepos}
      onOperationComplete={handleOperationComplete}
      onClearSelection={() => setSelectedRepos([])}
    />
  )
}
```

#### Features

- **Batch Operations**: Delete and re-ingest multiple repositories
- **Progress Tracking**: Real-time progress with success/failure counts
- **Detailed Confirmation**: Repository list with status information
- **Force Operations**: Override processing status for bulk operations
- **Result Reporting**: Detailed success and failure information

#### Operation Types

```typescript
type BatchOperationType = "delete" | "re_ingest"

interface BatchOperationStatus {
  operation: BatchOperationType
  total: number
  completed: number
  failed: number
  inProgress: boolean
  results: Array<{
    repository_id: RepositoryId
    status: "pending" | "success" | "error"
    error?: string
  }>
}
```

---

### RepositoryErrorBoundary

Error boundary component specifically designed for repository operations.

#### Props

```typescript
interface RepositoryErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<{
    error: RepositoryError
    retry: () => void
    reset: () => void
  }>
  onError?: (error: RepositoryError, errorInfo: React.ErrorInfo) => void
  maxRetries?: number
  showDetails?: boolean
}
```

#### Usage

```tsx
import { RepositoryErrorBoundary } from "@/components/error"

function App() {
  return (
    <RepositoryErrorBoundary
      maxRetries={3}
      showDetails={process.env.NODE_ENV === 'development'}
      onError={(error, errorInfo) => {
        // Log error to monitoring service
        console.error('Repository error:', error, errorInfo)
      }}
    >
      <RepositoryManagementPage />
    </RepositoryErrorBoundary>
  )
}
```

#### Features

- **Error Classification**: Repository-specific error types and messages
- **Retry Mechanisms**: Automatic and manual retry options
- **User-Friendly Messages**: Actionable error messages with suggestions
- **Development Tools**: Detailed error information in development mode

---

## Hooks

### useRepositoryErrorHandler

Hook for handling repository-specific errors with enhanced error messages.

```typescript
import { useRepositoryErrorHandler } from "@/components/error"

function MyComponent() {
  const { handleError, createRetryFunction } = useRepositoryErrorHandler()
  
  const performOperation = async () => {
    try {
      await repositoryOperation()
    } catch (error) {
      const repositoryError = handleError(error, {
        context: { operation: "repository_delete", repository_id: "repo-123" }
      })
      
      if (repositoryError.retryable) {
        // Show retry option
      }
    }
  }
  
  const retryableOperation = createRetryFunction(
    () => apiCall(),
    3, // max retries
    1000 // base delay
  )
}
```

## Styling and Theming

### CSS Classes

All components use Tailwind CSS classes and support custom styling:

```tsx
// Custom styling example
<RepositoryList 
  className="custom-repository-list border-2 border-blue-500"
/>

// Theme-aware styling
<IngestionProgress 
  className="dark:bg-gray-800 light:bg-white"
/>
```

### Theme Variables

Components respect the application theme:

```css
/* Custom CSS variables for repository components */
:root {
  --repository-primary: #3b82f6;
  --repository-success: #10b981;
  --repository-warning: #f59e0b;
  --repository-error: #ef4444;
}

[data-theme="dark"] {
  --repository-primary: #60a5fa;
  --repository-success: #34d399;
  --repository-warning: #fbbf24;
  --repository-error: #f87171;
}
```

## Performance Considerations

### Optimization Tips

1. **Memoization**: Use React.memo for expensive components
2. **Virtualization**: Consider virtual scrolling for large lists
3. **Debouncing**: Search inputs are automatically debounced
4. **Caching**: API responses are cached appropriately
5. **Lazy Loading**: Load repository details on demand

### Example Optimizations

```tsx
// Memoized repository item
const RepositoryItem = React.memo(({ repository }: { repository: RepositoryMetadata }) => {
  return (
    <div className="repository-item">
      {/* Repository content */}
    </div>
  )
})

// Debounced search
const useDebounceSearch = (value: string, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value)
  
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)
    
    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])
  
  return debouncedValue
}
```

## Testing

### Component Testing

```tsx
import { render, screen, userEvent } from "@/test/utils"
import { RepositoryList } from "@/components/repository"

describe("RepositoryList", () => {
  it("should render repository list", () => {
    render(<RepositoryList />)
    expect(screen.getByText("Repository")).toBeInTheDocument()
  })
  
  it("should handle repository selection", async () => {
    const user = userEvent.setup()
    const onSelectionChange = vi.fn()
    
    render(
      <RepositoryList 
        showSelection={true}
        onSelectionChange={onSelectionChange}
      />
    )
    
    const checkbox = screen.getByRole("checkbox")
    await user.click(checkbox)
    
    expect(onSelectionChange).toHaveBeenCalled()
  })
})
```

### Mock Data

```typescript
import { repositoryTestUtils } from "@/test/repository/repository.test.config"

// Create mock repository
const mockRepo = repositoryTestUtils.createMockRepository({
  name: "test-repo",
  status: "completed"
})

// Mock API responses
const mockApiResponse = repositoryTestUtils.mockApiHooks.repositoryList()
```

## Migration Guide

### From Previous Components

If migrating from older repository components:

1. **Props Changes**: Update component props to match new interfaces
2. **Event Handlers**: Update event handler signatures
3. **Styling**: Migrate custom styles to Tailwind classes
4. **API Integration**: Update to use new repository hooks

### Breaking Changes

- `onRepositorySelect` → `onSelectionChange`
- `repositoryData` → `repository`
- Status values have changed to match API
- Error handling now uses repository-specific types

## Best Practices

### Component Usage

1. **Always wrap with error boundaries** for production applications
2. **Use proper loading states** while data is being fetched
3. **Implement proper error handling** with user-friendly messages
4. **Follow accessibility guidelines** for keyboard navigation
5. **Test all user interactions** thoroughly

### Performance

1. **Memoize expensive calculations** using React.useMemo
2. **Debounce user inputs** to reduce API calls
3. **Use pagination** for large datasets
4. **Implement proper caching** strategies
5. **Monitor component re-renders** in development

### Accessibility

1. **Provide ARIA labels** for all interactive elements
2. **Ensure keyboard navigation** works properly
3. **Use semantic HTML** elements where possible
4. **Test with screen readers** regularly
5. **Maintain proper color contrast** ratios
