# Repository Management Documentation

## Overview

The Repository Management system provides a comprehensive interface for managing GitHub repository ingestion, tracking progress, and performing bulk operations. This system enables users to add repositories, monitor real-time ingestion progress, and manage their repository collection efficiently.

## Architecture

### Component Structure

```
src/components/repository/
├── RepositoryList.tsx          # Main repository listing with search/filter
├── IngestionProgress.tsx       # Real-time progress tracking
├── RepositoryActions.tsx       # Individual repository operations
├── BatchOperations.tsx         # Bulk operations with progress tracking
└── __tests__/                  # Comprehensive test suite
    ├── RepositoryList.test.tsx
    ├── IngestionProgress.test.tsx
    ├── RepositoryActions.test.tsx
    └── BatchOperations.test.tsx
```

### API Integration

```
src/hooks/api/useRepository.ts  # Repository-specific API hooks
src/lib/errors/repository-errors.ts  # Enhanced error handling
src/types/api.ts               # TypeScript definitions
```

### Page Integration

```
src/app/repositories/
├── page.tsx                   # Main repository management page
└── layout.tsx                 # Page layout and metadata
```

## Features

### 1. Repository Input & Validation

**Enhanced Repository Selector**
- GitHub URL validation with real-time feedback
- Branch selection with dropdown and manual input
- Authentication token support for private repositories
- Force refresh option for re-processing
- Advanced options toggle for clean UI

**Key Components:**
- Extends existing `RepositorySelector` component
- Integrates with form validation using Zod schemas
- Provides immediate feedback on URL format and accessibility

### 2. Repository List Management

**Comprehensive Repository Display**
- Sortable columns (name, status, last updated, file count)
- Search functionality with debounced input
- Status-based filtering (completed, processing, failed, etc.)
- Pagination with configurable page sizes
- Bulk selection with select-all functionality

**Repository Information:**
- Owner/repository name with description
- Branch and commit SHA display
- Processing status with colored badges
- File counts and processing statistics
- Last updated timestamps
- Private repository indicators

### 3. Real-time Progress Tracking

**Detailed Progress Display**
- Current processing stage with appropriate icons
- Progress percentage with visual progress bar
- Elapsed time and estimated completion
- File processing statistics (discovered, processed, filtered)
- Content statistics (chunks created, embeddings generated)
- Real-time error and warning reporting

**Progress Stages:**
- Initializing → Cloning → Scanning Files → Filtering Files
- Extracting Metadata → Chunking Content → Generating Embeddings
- Storing Vectors → Finalizing → Completed

### 4. Repository Actions

**Individual Operations:**
- View repository details
- Open repository in GitHub
- Re-ingest with force refresh option
- Delete with confirmation and force option

**Bulk Operations:**
- Multi-repository selection
- Batch re-ingestion with progress tracking
- Batch deletion with detailed confirmation
- Operation progress with success/failure reporting

### 5. Error Handling & Recovery

**Comprehensive Error Management:**
- Repository-specific error classification
- User-friendly error messages with actionable suggestions
- Automatic retry mechanisms with exponential backoff
- Error boundaries for graceful failure handling
- Detailed error logging for debugging

**Error Types:**
- Network errors with retry suggestions
- Authentication errors with token guidance
- Permission errors with access instructions
- Rate limiting with wait time indicators
- Server errors with support contact information

## Usage Guide

### Adding a New Repository

1. Click "Add Repository" button
2. Enter GitHub repository URL
3. Select branch (optional, defaults to main)
4. Configure authentication if needed for private repos
5. Enable force refresh if re-processing existing repo
6. Click "Start Ingestion"

### Monitoring Progress

1. Navigate to "Active Ingestions" tab
2. View real-time progress for each repository
3. Monitor file processing statistics
4. Check for errors or warnings
5. Use retry option if ingestion fails

### Managing Repositories

1. Use search to find specific repositories
2. Filter by status (completed, processing, failed)
3. Sort by name, status, or last updated
4. Select repositories for bulk operations
5. Use individual actions for specific repositories

### Bulk Operations

1. Select multiple repositories using checkboxes
2. Navigate to "Bulk Actions" tab
3. Choose operation (re-ingest or delete)
4. Review confirmation dialog with repository details
5. Confirm operation and monitor progress

## API Integration

### Repository Hooks

```typescript
// List repositories with filtering and pagination
const { data, isLoading, error } = useRepositoryList({
  page: 1,
  page_size: 20,
  search_query: "react",
  status_filter: "completed",
  sort_by: "updated_at",
  sort_order: "desc"
})

// Get repository details
const { data: repository } = useRepositoryDetails({
  repository_id: "repo-123"
})

// Delete repository
const deleteMutation = useRepositoryDelete({
  onSuccess: (data) => {
    toast.success(`Repository ${data.repository_id} deleted`)
  }
})

// Re-ingest repository
const reIngestMutation = useRepositoryReIngest({
  onSuccess: (data) => {
    toast.success("Re-ingestion started")
  }
})

// Batch operations
const batchMutation = useBatchOperation({
  onSuccess: (data) => {
    console.log(`${data.successful} operations completed`)
  }
})

// Repository statistics
const { data: stats } = useRepositoryStats()

// Ingestion progress tracking
const { data: progress } = useIngestionProgress("repo-123", {
  refetchInterval: 2000 // Poll every 2 seconds
})
```

### Error Handling

```typescript
import { useRepositoryErrorHandler } from "@/components/error"

const { handleError, createRetryFunction } = useRepositoryErrorHandler()

// Handle repository-specific errors
try {
  await repositoryOperation()
} catch (error) {
  const repositoryError = handleError(error, {
    context: { operation: "repository_delete", repository_id: "repo-123" }
  })
  
  if (repositoryError.retryable) {
    // Show retry option to user
  }
}

// Create retry function with exponential backoff
const retryableOperation = createRetryFunction(
  () => apiCall(),
  3, // max retries
  1000 // base delay
)
```

## Testing

### Running Tests

```bash
# Run all repository tests
pnpm test:repository

# Run specific test suites
pnpm test:repository:components
pnpm test:repository:hooks
pnpm test:repository:integration

# Run with coverage
pnpm test:coverage
```

### Test Coverage

The repository management system includes comprehensive tests:

- **Unit Tests**: All components and hooks (>90% coverage)
- **Integration Tests**: End-to-end user workflows
- **Error Handling Tests**: All error scenarios and recovery
- **Accessibility Tests**: ARIA compliance and keyboard navigation
- **Performance Tests**: API response times and rendering performance

### Test Structure

```typescript
// Component testing example
describe("RepositoryList", () => {
  it("should render repository list with data", () => {
    render(<RepositoryList />)
    expect(screen.getByText("repo1")).toBeInTheDocument()
  })

  it("should handle search input", async () => {
    const user = userEvent.setup()
    render(<RepositoryList showControls={true} />)
    
    const searchInput = screen.getByPlaceholderText("Search repositories...")
    await user.type(searchInput, "test query")
    
    await waitFor(() => {
      expect(mockUseRepositoryList).toHaveBeenCalledWith(
        expect.objectContaining({ search_query: "test query" })
      )
    })
  })
})
```

## Performance Considerations

### Optimization Strategies

1. **Pagination**: Large repository lists are paginated to maintain performance
2. **Debounced Search**: Search input is debounced to reduce API calls
3. **Selective Polling**: Progress polling only occurs for active ingestions
4. **Memoization**: Expensive calculations are memoized using React.useMemo
5. **Virtual Scrolling**: For very large lists (future enhancement)

### Caching Strategy

- Repository list data is cached for 30 seconds
- Repository details are cached for 5 minutes
- Statistics are cached for 1 minute with background refresh
- Progress data is not cached due to real-time nature

## Security Considerations

### Authentication

- GitHub personal access tokens are handled securely
- Tokens are not stored in localStorage or sessionStorage
- API calls include proper authentication headers
- Private repository access is properly validated

### Data Validation

- All user inputs are validated using Zod schemas
- Repository URLs are sanitized and validated
- API responses are type-checked at runtime
- XSS protection through proper escaping

## Accessibility

### ARIA Compliance

- All interactive elements have proper ARIA labels
- Tables include proper headers and descriptions
- Progress bars have accessible progress information
- Error messages are announced to screen readers

### Keyboard Navigation

- All functionality is accessible via keyboard
- Tab order is logical and intuitive
- Focus indicators are clearly visible
- Keyboard shortcuts for common actions

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Future Enhancements

### Planned Features

1. **Advanced Filtering**: Filter by language, size, date ranges
2. **Repository Templates**: Save common repository configurations
3. **Scheduled Re-ingestion**: Automatic periodic updates
4. **Webhook Integration**: Real-time updates from GitHub
5. **Export Functionality**: Export repository data and statistics
6. **Repository Groups**: Organize repositories into collections
7. **Advanced Analytics**: Detailed processing metrics and trends

### Performance Improvements

1. **Virtual Scrolling**: For handling thousands of repositories
2. **Background Sync**: Offline capability with sync when online
3. **Incremental Loading**: Load repository details on demand
4. **WebSocket Integration**: Real-time updates without polling

## Troubleshooting

### Common Issues

**Repository Not Found (404)**
- Verify repository URL is correct
- Check if repository is public or provide authentication
- Ensure repository hasn't been moved or deleted

**Authentication Failed (401/403)**
- Verify GitHub personal access token is valid
- Check token has required permissions (repo scope)
- Ensure token hasn't expired

**Ingestion Stuck in Processing**
- Check for large files that may cause timeouts
- Verify server resources are sufficient
- Use force re-ingest option to restart

**Search Not Working**
- Clear browser cache and cookies
- Check network connectivity
- Verify API endpoints are accessible

### Support

For additional support or bug reports:
1. Check the console for error messages
2. Review the error handling suggestions
3. Contact the development team with detailed error information
4. Include browser version and steps to reproduce

## Migration Guide

### From Previous Version

If upgrading from a previous repository management implementation:

1. **Data Migration**: Existing repository data will be automatically migrated
2. **API Changes**: Update any custom integrations to use new API endpoints
3. **Component Updates**: Replace old repository components with new ones
4. **Testing**: Run the full test suite to ensure compatibility

### Breaking Changes

- Repository status enum values have changed
- API response format includes additional metadata
- Error handling now uses repository-specific error types
- Progress tracking includes more detailed stage information

## Related Documentation

- [API Reference](./repository-api.md) - Detailed API endpoint documentation
- [Component Reference](./repository-components.md) - Component props and usage
- [Error Handling Guide](./repository-errors.md) - Error types and recovery strategies
- [Testing Guide](./repository-testing.md) - Testing patterns and examples
