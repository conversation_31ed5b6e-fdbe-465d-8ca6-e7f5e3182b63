"""
Main FastAPI application for LLM RAG Codebase Query System.
"""

from contextlib import asynccontextmanager
from datetime import datetime
import logging
from typing import Any

from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import uvicorn

from .agents import (
    AgentContext,
    AgentFactory,
    AgentRegistry,
    AgentType,
    ConversationContext,
    Message,
    MessageRole,
)
from .config import get_settings
from .ingestion.integration_example import IntegratedIngestionPipeline
from .models.repository import (
    BatchOperationRequest,
    BatchOperationResponse,
    IngestionProgress,
    RepositoryDeleteRequest,
    RepositoryDeleteResponse,
    RepositoryListRequest,
    RepositoryListResponse,
    RepositoryMetadata,
    RepositoryReIngestRequest,
    RepositoryStatsResponse,
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get settings
settings = get_settings()

# Global variables for shared services
agent_factory: AgentFactory | None = None
agent_registry: AgentRegistry | None = None
ingestion_pipeline: IntegratedIngestionPipeline | None = None

# Repository storage (in-memory for now, can be replaced with database)
repository_storage: dict[str, RepositoryMetadata] = {}

# Progress tracking storage for active ingestions
progress_storage: dict[str, IngestionProgress] = {}


# Pydantic Models for API
class QueryRequest(BaseModel):
    """Request model for query endpoint."""

    query: str = Field(..., description="Natural language query to process")
    session_id: str | None = Field(None, description="Session ID for conversation context")
    user_id: str | None = Field(None, description="User ID for personalization")
    context_filters: dict[str, Any] | None = Field(None, description="Additional context filters")
    repository: str | None = Field(None, description="Target repository for query")


class QueryResponse(BaseModel):
    """Response model for query endpoint."""

    result_markdown: str = Field(..., description="Formatted response in Markdown")
    structured: dict[str, Any] = Field(..., description="Structured response data")
    agent_type: str = Field(..., description="Primary agent that processed the query")
    confidence: float = Field(..., description="Confidence score of the response")
    sources: list[str] = Field(default_factory=list, description="Source citations")
    processing_time: float | None = Field(None, description="Processing time in seconds")
    session_id: str = Field(..., description="Session ID for conversation tracking")


class IngestionRequest(BaseModel):
    """Request model for ingestion endpoint."""

    repository_url: str = Field(..., description="GitHub repository URL to ingest")
    branch: str | None = Field("main", description="Branch to ingest")
    commit_sha: str | None = Field(None, description="Specific commit to ingest")
    force_refresh: bool = Field(False, description="Force refresh of existing data")


class IngestionResponse(BaseModel):
    """Response model for ingestion endpoint."""

    status: str = Field(..., description="Ingestion status")
    repository: str = Field(..., description="Repository that was ingested")
    processed_files: int = Field(..., description="Number of files processed")
    chunks_created: int = Field(..., description="Number of chunks created")
    embeddings_generated: int = Field(..., description="Number of embeddings generated")
    processing_time: float = Field(..., description="Total processing time in seconds")


class StatusResponse(BaseModel):
    """Response model for status endpoint."""

    api: str = Field(..., description="API status")
    agents: dict[str, str] = Field(..., description="Agent system status")
    vector_store: str = Field(..., description="Vector store status")
    ingestion_pipeline: str = Field(..., description="Ingestion pipeline status")
    active_sessions: int = Field(..., description="Number of active conversation sessions")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan events."""
    global agent_factory, agent_registry, ingestion_pipeline

    logger.info("Starting up LLM RAG Codebase Query System...")

    try:
        # Initialize shared services
        agent_factory = AgentFactory(settings)
        agent_registry = AgentRegistry(agent_factory)
        ingestion_pipeline = IntegratedIngestionPipeline(settings)

        # Initialize async services
        await agent_factory.initialize_shared_services()

        logger.info("✅ Application startup completed successfully")

        yield

    except Exception as e:
        logger.error(f"❌ Failed to start application: {e}")
        raise
    finally:
        # Cleanup
        logger.info("Shutting down application...")
        if agent_factory:
            await agent_factory.shutdown_shared_services()
        logger.info("✅ Application shutdown completed")


# Create FastAPI app with lifespan management
app = FastAPI(
    title="LLM RAG Codebase Query System",
    description="A sophisticated multi-agent RAG system for querying GitHub repository codebases",
    version="0.1.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://frontend:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Helper functions
async def get_or_create_session(session_id: str | None, user_id: str | None) -> ConversationContext:
    """Get existing session or create a new one."""
    if not agent_factory:
        raise HTTPException(status_code=500, detail="Agent factory not initialized")

    context_manager = agent_factory.context_manager

    if session_id:
        # Try to get existing session
        existing_context = await context_manager.get_context(session_id)
        if existing_context:
            return existing_context

    # Create new session
    return await context_manager.create_session(user_id=user_id)


# API Endpoints
@app.get("/")
async def root() -> dict[str, str]:
    """Root endpoint."""
    return {"message": "LLM RAG Codebase Query System API"}


@app.get("/health")
async def health_check() -> dict[str, Any]:
    """Health check endpoint."""
    return {
        "status": "healthy",
        "service": "llm-rag-backend",
        "version": "0.1.0",
        "environment": settings.environment,
    }


@app.get("/api/status")
async def api_status() -> StatusResponse:
    """Enhanced API status endpoint with agent system health."""
    try:
        # Check agent system status
        agents_status = {}
        if agent_registry:
            available_agents = agent_factory.get_available_agents() if agent_factory else {}
            for agent_type in available_agents:
                try:
                    # Try to get agent (this will create it if needed)
                    agent_registry.get_agent(agent_type)
                    agents_status[agent_type.value] = "operational"
                except Exception as e:
                    agents_status[agent_type.value] = f"error: {e!s}"
        else:
            agents_status = {"status": "not_initialized"}

        # Check vector store status
        vector_store_status = "operational"
        if ingestion_pipeline:
            try:
                # TODO: Add actual vector store health check
                vector_store_status = "operational"
            except Exception:
                vector_store_status = "error"
        else:
            vector_store_status = "not_initialized"

        # Check ingestion pipeline status
        pipeline_status = "operational" if ingestion_pipeline else "not_initialized"

        # Get active sessions count
        active_sessions = 0
        if agent_factory and agent_factory.context_manager:
            try:
                # TODO: Add method to get active session count
                active_sessions = 0  # Placeholder
            except Exception:
                active_sessions = -1

        return StatusResponse(
            api="operational",
            agents=agents_status,
            vector_store=vector_store_status,
            ingestion_pipeline=pipeline_status,
            active_sessions=active_sessions,
        )

    except Exception as e:
        logger.error(f"Status check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Status check failed: {e!s}") from e


@app.post("/api/query")
async def process_query(request: QueryRequest) -> QueryResponse:
    """Process a natural language query using the multi-agent system."""
    if not agent_factory or not agent_registry:
        raise HTTPException(status_code=500, detail="Agent system not initialized")

    try:
        logger.info(f"Processing query: {request.query[:100]}...")

        # Get or create conversation session
        conversation_context = await get_or_create_session(request.session_id, request.user_id)

        # Add user message to conversation history
        user_message = Message(
            role=MessageRole.USER,
            content=request.query,
            metadata={"repository": request.repository, "filters": request.context_filters}
        )
        await agent_factory.context_manager.add_message(conversation_context.session_id, user_message)

        # Create agent context
        agent_context = AgentContext(
            conversation_context=conversation_context,
            repository_context={"repository": request.repository} if request.repository else None,
            execution_metadata={
                "filters": request.context_filters or {},
                "user_id": request.user_id,
            }
        )

        # Get orchestrator agent
        orchestrator = agent_registry.get_agent(AgentType.ORCHESTRATOR)

        # Process query through orchestrator
        response = await orchestrator.process_query(request.query, agent_context)

        # Add assistant response to conversation history
        assistant_message = Message(
            role=MessageRole.ASSISTANT,
            content=response.content,
            metadata={
                "agent_type": response.agent_type.value,
                "confidence": response.confidence,
                "sources": response.sources,
            }
        )
        await agent_factory.context_manager.add_message(conversation_context.session_id, assistant_message)

        # Format structured response
        structured_data = {
            "agent_type": response.agent_type.value,
            "confidence": response.confidence,
            "metadata": response.metadata,
            "sources": response.sources,
            "session_id": conversation_context.session_id,
        }

        # Add task plan data if available (for Task Planner responses)
        if "task_plan" in response.metadata:
            structured_data["task_plan"] = response.metadata["task_plan"]

        # Add architecture analysis if available (for Technical Architect responses)
        if "architecture_analysis" in response.metadata:
            structured_data["architecture_analysis"] = response.metadata["architecture_analysis"]

        logger.info(f"Query processed successfully by {response.agent_type.value}")

        return QueryResponse(
            result_markdown=response.content,
            structured=structured_data,
            agent_type=response.agent_type.value,
            confidence=response.confidence,
            sources=response.sources,
            processing_time=response.processing_time,
            session_id=conversation_context.session_id,
        )

    except Exception as e:
        logger.error(f"Query processing failed: {e}")
        raise HTTPException(status_code=500, detail=f"Query processing failed: {e!s}") from e


@app.post("/api/ingest")
async def ingest_repository(request: IngestionRequest) -> IngestionResponse:
    """Ingest a GitHub repository into the knowledge base."""
    if not ingestion_pipeline:
        raise HTTPException(status_code=500, detail="Ingestion pipeline not initialized")

    try:
        logger.info(f"Starting ingestion for repository: {request.repository_url}")

        import time
        import uuid
        from urllib.parse import urlparse
        start_time = time.time()

        # Parse repository URL to extract owner and name
        url_parts = urlparse(request.repository_url)
        path_parts = url_parts.path.strip('/').split('/')
        if len(path_parts) >= 2:
            owner = path_parts[0]
            repo_name = path_parts[1].replace('.git', '')
        else:
            owner = "unknown"
            repo_name = "unknown"

        # Generate repository ID
        repo_id = str(uuid.uuid4())

        # Create repository metadata entry
        repo_metadata = RepositoryMetadata(
            id=repo_id,
            url=request.repository_url,
            name=repo_name,
            owner=owner,
            branch=request.branch or "main",
            commit_sha="",  # Will be updated after ingestion
            is_private=False,  # Will be updated with actual info
            description=None,
            language=None,
            size=None,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            ingested_at=datetime.now(),
            status="processing",
            processed_files=0,
            chunks_created=0,
            embeddings_generated=0,
            processing_time=0.0,
        )

        # Store repository metadata
        repository_storage[repo_id] = repo_metadata

        # Create initial progress tracking
        progress = IngestionProgress(
            repository_id=repo_id,
            repository_url=request.repository_url,
            status="processing",
            stage="initializing",
            progress_percentage=0.0,
            current_step="Initializing ingestion process...",
            files_discovered=0,
            files_processed=0,
            files_filtered=0,
            chunks_created=0,
            embeddings_generated=0,
            started_at=datetime.now(),
            estimated_completion=None,
            elapsed_time=0.0,
            errors=[],
            warnings=[]
        )
        progress_storage[repo_id] = progress

        # Run the ingestion pipeline
        result = await ingestion_pipeline.process_repository(
            repository_url=request.repository_url,
            branch=request.branch,
        )

        processing_time = time.time() - start_time

        # Update repository metadata with results
        repo_metadata.status = "completed"
        repo_metadata.processed_files = result.get("files_processed", 0)
        repo_metadata.chunks_created = result.get("chunks_generated", 0)
        repo_metadata.embeddings_generated = result.get("embeddings_created", 0)
        repo_metadata.processing_time = processing_time
        repo_metadata.updated_at = datetime.now()
        repository_storage[repo_id] = repo_metadata

        # Update final progress and then remove from active tracking
        if repo_id in progress_storage:
            final_progress = progress_storage[repo_id]
            final_progress.status = "completed"
            final_progress.stage = "completed"
            final_progress.progress_percentage = 100.0
            final_progress.current_step = "Ingestion completed successfully"
            final_progress.files_processed = result.get("files_processed", 0)
            final_progress.chunks_created = result.get("chunks_generated", 0)
            final_progress.embeddings_generated = result.get("embeddings_created", 0)
            final_progress.elapsed_time = processing_time
            # Remove from active tracking after a short delay (in real implementation, this would be handled differently)
            del progress_storage[repo_id]

        logger.info(f"Ingestion completed for {request.repository_url} in {processing_time:.2f}s")

        return IngestionResponse(
            status="completed",
            repository=request.repository_url,
            processed_files=result.get("files_processed", 0),
            chunks_created=result.get("chunks_generated", 0),
            embeddings_generated=result.get("embeddings_created", 0),
            processing_time=processing_time,
        )

    except Exception as e:
        # Update repository status on failure if it was created
        if 'repo_id' in locals() and repo_id in repository_storage:
            repo_metadata = repository_storage[repo_id]
            repo_metadata.status = "failed"
            repo_metadata.error_message = str(e)
            repository_storage[repo_id] = repo_metadata

            # Update progress tracking on failure
            if repo_id in progress_storage:
                failed_progress = progress_storage[repo_id]
                failed_progress.status = "failed"
                failed_progress.stage = "completed"
                failed_progress.current_step = f"Ingestion failed: {str(e)}"
                failed_progress.errors.append(str(e))
                failed_progress.elapsed_time = (datetime.now() - failed_progress.started_at).total_seconds()
                # Remove from active tracking
                del progress_storage[repo_id]

        logger.error(f"Repository ingestion failed: {e}")
        raise HTTPException(status_code=500, detail=f"Ingestion failed: {e!s}") from e


# Progress Tracking Endpoints

@app.get("/api/ingestion/progress/{repository_id}")
async def get_ingestion_progress(repository_id: str) -> IngestionProgress:
    """Get detailed ingestion progress for a repository."""
    try:
        # Check if there's active progress tracking
        if repository_id in progress_storage:
            return progress_storage[repository_id]

        # Check if repository exists and get its status
        if repository_id in repository_storage:
            repo = repository_storage[repository_id]

            # Create progress response based on repository status
            if repo.status == "completed":
                return IngestionProgress(
                    repository_id=repository_id,
                    repository_url=repo.url,
                    status="completed",
                    stage="completed",
                    progress_percentage=100.0,
                    current_step="Ingestion completed successfully",
                    files_discovered=repo.processed_files,
                    files_processed=repo.processed_files,
                    files_filtered=0,
                    chunks_created=repo.chunks_created,
                    embeddings_generated=repo.embeddings_generated,
                    started_at=repo.ingested_at,
                    estimated_completion=None,
                    elapsed_time=repo.processing_time,
                    errors=[],
                    warnings=[]
                )
            elif repo.status == "failed":
                return IngestionProgress(
                    repository_id=repository_id,
                    repository_url=repo.url,
                    status="failed",
                    stage="completed",
                    progress_percentage=0.0,
                    current_step=repo.error_message or "Ingestion failed",
                    files_discovered=0,
                    files_processed=0,
                    files_filtered=0,
                    chunks_created=0,
                    embeddings_generated=0,
                    started_at=repo.ingested_at,
                    estimated_completion=None,
                    elapsed_time=repo.processing_time,
                    errors=[repo.error_message] if repo.error_message else [],
                    warnings=[]
                )
            elif repo.status == "processing":
                return IngestionProgress(
                    repository_id=repository_id,
                    repository_url=repo.url,
                    status="processing",
                    stage="processing",
                    progress_percentage=50.0,  # Estimated
                    current_step="Processing repository...",
                    files_discovered=0,
                    files_processed=0,
                    files_filtered=0,
                    chunks_created=0,
                    embeddings_generated=0,
                    started_at=repo.ingested_at,
                    estimated_completion=None,
                    elapsed_time=(datetime.now() - repo.ingested_at).total_seconds(),
                    errors=[],
                    warnings=[]
                )

        raise HTTPException(status_code=404, detail="Repository or progress information not found")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get ingestion progress for {repository_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get progress: {e!s}") from e


@app.get("/api/ingestion/progress")
async def list_active_ingestions() -> list[IngestionProgress]:
    """List all active ingestion processes."""
    try:
        # Return all active progress entries
        active_progress = list(progress_storage.values())

        # Also include repositories that are currently processing but don't have detailed progress
        for repo_id, repo in repository_storage.items():
            if repo.status == "processing" and repo_id not in progress_storage:
                progress = IngestionProgress(
                    repository_id=repo_id,
                    repository_url=repo.url,
                    status="processing",
                    stage="processing",
                    progress_percentage=50.0,
                    current_step="Processing repository...",
                    files_discovered=0,
                    files_processed=0,
                    files_filtered=0,
                    chunks_created=0,
                    embeddings_generated=0,
                    started_at=repo.ingested_at,
                    estimated_completion=None,
                    elapsed_time=(datetime.now() - repo.ingested_at).total_seconds(),
                    errors=[],
                    warnings=[]
                )
                active_progress.append(progress)

        return active_progress

    except Exception as e:
        logger.error(f"Failed to list active ingestions: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list active ingestions: {e!s}") from e


@app.websocket("/ws/ingestion/progress/{repository_id}")
async def websocket_ingestion_progress(websocket: WebSocket, repository_id: str):
    """WebSocket endpoint for real-time ingestion progress updates."""
    await websocket.accept()

    try:
        import asyncio
        import json

        # Send initial progress if available
        if repository_id in progress_storage:
            progress = progress_storage[repository_id]
            await websocket.send_text(progress.model_dump_json())
        elif repository_id in repository_storage:
            # Send repository status
            repo = repository_storage[repository_id]
            status_update = {
                "repository_id": repository_id,
                "status": repo.status.value,
                "message": f"Repository status: {repo.status.value}"
            }
            await websocket.send_text(json.dumps(status_update))

        # Keep connection alive and send updates
        last_progress = None
        while True:
            await asyncio.sleep(1)  # Check for updates every second

            if repository_id in progress_storage:
                current_progress = progress_storage[repository_id]
                # Only send if progress has changed
                if last_progress is None or current_progress != last_progress:
                    await websocket.send_text(current_progress.model_dump_json())
                    last_progress = current_progress
            elif repository_id in repository_storage:
                # Repository completed or failed, send final status and close
                repo = repository_storage[repository_id]
                if repo.status in ["completed", "failed"]:
                    final_status = {
                        "repository_id": repository_id,
                        "status": repo.status.value,
                        "message": f"Repository ingestion {repo.status.value}",
                        "final": True
                    }
                    await websocket.send_text(json.dumps(final_status))
                    break
            else:
                # Repository not found, close connection
                await websocket.send_text(json.dumps({
                    "error": "Repository not found",
                    "repository_id": repository_id
                }))
                break

    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for repository {repository_id}")
    except Exception as e:
        logger.error(f"WebSocket error for repository {repository_id}: {e}")
        try:
            await websocket.send_text(json.dumps({
                "error": str(e),
                "repository_id": repository_id
            }))
        except:
            pass  # Connection might be closed


# Repository Management Endpoints

@app.get("/api/repositories")
async def list_repositories(
    page: int = 1,
    page_size: int = 20,
    status_filter: str | None = None,
    search_query: str | None = None,
    sort_by: str = "updated_at",
    sort_order: str = "desc"
) -> RepositoryListResponse:
    """List all repositories with filtering and pagination."""
    try:
        # Filter repositories
        filtered_repos = list(repository_storage.values())

        # Apply status filter
        if status_filter:
            filtered_repos = [repo for repo in filtered_repos if repo.status.value == status_filter]

        # Apply search filter
        if search_query:
            query_lower = search_query.lower()
            filtered_repos = [
                repo for repo in filtered_repos
                if query_lower in repo.name.lower() or query_lower in repo.owner.lower()
            ]

        # Sort repositories
        reverse = sort_order == "desc"
        if sort_by == "name":
            filtered_repos.sort(key=lambda x: x.name, reverse=reverse)
        elif sort_by == "owner":
            filtered_repos.sort(key=lambda x: x.owner, reverse=reverse)
        elif sort_by == "status":
            filtered_repos.sort(key=lambda x: x.status.value, reverse=reverse)
        else:  # default to updated_at
            filtered_repos.sort(key=lambda x: x.updated_at, reverse=reverse)

        # Apply pagination
        total_count = len(filtered_repos)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_repos = filtered_repos[start_idx:end_idx]

        total_pages = (total_count + page_size - 1) // page_size

        return RepositoryListResponse(
            repositories=paginated_repos,
            total_count=total_count,
            page=page,
            page_size=page_size,
            total_pages=total_pages,
        )

    except Exception as e:
        logger.error(f"Failed to list repositories: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list repositories: {e!s}") from e


@app.get("/api/repositories/{repository_id}")
async def get_repository(repository_id: str) -> RepositoryMetadata:
    """Get detailed information about a specific repository."""
    try:
        if repository_id not in repository_storage:
            raise HTTPException(status_code=404, detail="Repository not found")

        return repository_storage[repository_id]

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get repository {repository_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get repository: {e!s}") from e


@app.delete("/api/repositories/{repository_id}")
async def delete_repository(repository_id: str, force: bool = False) -> RepositoryDeleteResponse:
    """Delete a repository from the knowledge base."""
    try:
        if repository_id not in repository_storage:
            raise HTTPException(status_code=404, detail="Repository not found")

        repo = repository_storage[repository_id]

        # Check if repository is currently processing
        if repo.status in ["pending", "processing"] and not force:
            raise HTTPException(
                status_code=409,
                detail="Repository is currently being processed. Use force=true to delete anyway."
            )

        # Remove from storage
        del repository_storage[repository_id]

        # TODO: Remove from vector store and clean up associated data

        logger.info(f"Deleted repository {repository_id}: {repo.name}")

        return RepositoryDeleteResponse(
            repository_id=repository_id,
            success=True,
            message=f"Repository '{repo.name}' deleted successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete repository {repository_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to delete repository: {e!s}") from e


@app.post("/api/repositories/{repository_id}/re-ingest")
async def re_ingest_repository(repository_id: str, request: RepositoryReIngestRequest) -> IngestionResponse:
    """Re-ingest a repository with updated content."""
    if not ingestion_pipeline:
        raise HTTPException(status_code=500, detail="Ingestion pipeline not initialized")

    try:
        if repository_id not in repository_storage:
            raise HTTPException(status_code=404, detail="Repository not found")

        repo = repository_storage[repository_id]

        # Check if repository is currently processing
        if repo.status in ["pending", "processing"]:
            raise HTTPException(
                status_code=409,
                detail="Repository is currently being processed. Please wait for completion."
            )

        # Use specified branch or current branch
        branch = request.branch or repo.branch

        logger.info(f"Starting re-ingestion for repository: {repo.url}")

        import time
        start_time = time.time()

        # Update repository status
        repo.status = "processing"
        repository_storage[repository_id] = repo

        # Run the ingestion pipeline
        result = await ingestion_pipeline.process_repository(
            repository_url=str(repo.url),
            branch=branch,
        )

        processing_time = time.time() - start_time

        # Update repository metadata
        repo.status = "completed"
        repo.branch = branch
        repo.processed_files = result.get("files_processed", 0)
        repo.chunks_created = result.get("chunks_generated", 0)
        repo.embeddings_generated = result.get("embeddings_created", 0)
        repo.processing_time = processing_time
        repo.updated_at = datetime.now()
        repo.error_message = None
        repo.error_details = None
        repository_storage[repository_id] = repo

        logger.info(f"Re-ingestion completed for {repo.url} in {processing_time:.2f}s")

        return IngestionResponse(
            status="completed",
            repository=str(repo.url),
            processed_files=result.get("files_processed", 0),
            chunks_created=result.get("chunks_generated", 0),
            embeddings_generated=result.get("embeddings_created", 0),
            processing_time=processing_time,
        )

    except HTTPException:
        raise
    except Exception as e:
        # Update repository status on failure
        if repository_id in repository_storage:
            repo = repository_storage[repository_id]
            repo.status = "failed"
            repo.error_message = str(e)
            repository_storage[repository_id] = repo

        logger.error(f"Repository re-ingestion failed: {e}")
        raise HTTPException(status_code=500, detail=f"Re-ingestion failed: {e!s}") from e


@app.post("/api/repositories/batch")
async def batch_repository_operation(request: BatchOperationRequest) -> BatchOperationResponse:
    """Perform batch operations on multiple repositories."""
    try:
        results = []
        successful = 0
        failed = 0

        for repo_id in request.repository_ids:
            try:
                if repo_id not in repository_storage:
                    results.append({
                        "repository_id": repo_id,
                        "success": False,
                        "message": "Repository not found"
                    })
                    failed += 1
                    continue

                repo = repository_storage[repo_id]

                # Check if repository is processing and force is not set
                if repo.status in ["pending", "processing"] and not request.force:
                    results.append({
                        "repository_id": repo_id,
                        "success": False,
                        "message": "Repository is currently processing"
                    })
                    failed += 1
                    continue

                if request.operation == "delete":
                    del repository_storage[repo_id]
                    results.append({
                        "repository_id": repo_id,
                        "success": True,
                        "message": f"Repository '{repo.name}' deleted successfully"
                    })
                    successful += 1

                elif request.operation == "re_ingest":
                    # For batch re-ingestion, we'll just mark as pending
                    # The actual ingestion would be handled by a background task
                    repo.status = "pending"
                    repository_storage[repo_id] = repo
                    results.append({
                        "repository_id": repo_id,
                        "success": True,
                        "message": f"Repository '{repo.name}' queued for re-ingestion"
                    })
                    successful += 1

            except Exception as e:
                results.append({
                    "repository_id": repo_id,
                    "success": False,
                    "message": str(e)
                })
                failed += 1

        return BatchOperationResponse(
            operation=request.operation,
            total_requested=len(request.repository_ids),
            successful=successful,
            failed=failed,
            results=results
        )

    except Exception as e:
        logger.error(f"Batch operation failed: {e}")
        raise HTTPException(status_code=500, detail=f"Batch operation failed: {e!s}") from e


@app.get("/api/repositories/stats")
async def get_repository_stats() -> RepositoryStatsResponse:
    """Get repository statistics."""
    try:
        repos = list(repository_storage.values())
        total_repositories = len(repos)

        # Count by status
        by_status = {}
        for status in ["pending", "processing", "completed", "failed", "cancelled"]:
            by_status[status] = len([repo for repo in repos if repo.status.value == status])

        # Calculate totals
        total_files_processed = sum(repo.processed_files for repo in repos)
        total_chunks_created = sum(repo.chunks_created for repo in repos)
        total_embeddings = sum(repo.embeddings_generated for repo in repos)

        # Calculate average processing time (only for completed repositories)
        completed_repos = [repo for repo in repos if repo.status.value == "completed" and repo.processing_time > 0]
        average_processing_time = (
            sum(repo.processing_time for repo in completed_repos) / len(completed_repos)
            if completed_repos else 0.0
        )

        # Estimate storage usage (simplified calculation)
        storage_usage_mb = total_chunks_created * 0.5  # Rough estimate: 0.5KB per chunk

        return RepositoryStatsResponse(
            total_repositories=total_repositories,
            by_status=by_status,
            total_files_processed=total_files_processed,
            total_chunks_created=total_chunks_created,
            total_embeddings=total_embeddings,
            average_processing_time=average_processing_time,
            storage_usage_mb=storage_usage_mb
        )

    except Exception as e:
        logger.error(f"Failed to get repository stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get repository stats: {e!s}") from e


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler."""
    logger.error(f"Global exception: {exc}")
    return JSONResponse(status_code=500, content={"detail": "Internal server error"})


if __name__ == "__main__":
    uvicorn.run(
        "src.main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.environment == "development",
        log_level="info",
    )
